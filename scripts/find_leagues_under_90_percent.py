#!/usr/bin/env python3
"""
Find Leagues Under 90% Success Rate

This script analyzes all leagues in the database and identifies those
with less than 90% success rate that need fixing.
"""

import sys
from typing import List, Tuple, Dict

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def analyze_all_leagues() -> List[Dict]:
    """Analyze all leagues and return those under 90% success rate."""
    print("🔍 ANALYZING ALL LEAGUES FOR SUCCESS RATES")
    print("=" * 70)
    
    leagues_under_90 = []
    
    with get_database() as db:
        # Get all leagues
        leagues = db.execute_query("SELECT DISTINCT league_name FROM leagues ORDER BY league_name")
        
        if leagues.empty:
            print("❌ No leagues found")
            return []
        
        print(f"📊 Found {len(leagues)} total leagues")
        print("\n🔍 Analyzing each league...")
        
        for _, league_row in leagues.iterrows():
            league_name = league_row['league_name']
            
            # Get all teams in this league
            teams = db.execute_query('''
                SELECT t.team_name FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE l.league_name = ?
            ''', (league_name,))
            
            if teams.empty:
                continue
            
            # Count working teams
            working_teams = 0
            total_teams = len(teams)
            
            for _, team in teams.iterrows():
                stats = db.get_team_stats(team['team_name'], league_name)
                if not stats.empty:
                    working_teams += 1
            
            success_rate = (working_teams / total_teams) * 100 if total_teams > 0 else 0
            
            # Only include leagues under 90%
            if success_rate < 90:
                leagues_under_90.append({
                    'league_name': league_name,
                    'working_teams': working_teams,
                    'total_teams': total_teams,
                    'success_rate': success_rate
                })
                
                status = "🔴" if success_rate < 50 else "🟡" if success_rate < 70 else "🟠"
                print(f"{status} {league_name}: {working_teams}/{total_teams} ({success_rate:.1f}%)")
    
    return leagues_under_90

def prioritize_leagues(leagues: List[Dict]) -> List[Dict]:
    """Prioritize leagues based on fixing potential."""
    print(f"\n📋 PRIORITIZING {len(leagues)} LEAGUES FOR FIXING")
    print("=" * 60)
    
    # Sort by success rate (higher first) and total teams (more teams first)
    # This prioritizes leagues that are closer to 90% and have more teams
    prioritized = sorted(leagues, key=lambda x: (x['success_rate'], x['total_teams']), reverse=True)
    
    print("Priority order (best candidates first):")
    for i, league in enumerate(prioritized[:20], 1):  # Show top 20
        priority = "🥇" if i <= 5 else "🥈" if i <= 10 else "🥉" if i <= 15 else "📌"
        print(f"{priority} {i:2d}. {league['league_name']}: {league['working_teams']}/{league['total_teams']} ({league['success_rate']:.1f}%)")
    
    if len(prioritized) > 20:
        print(f"... and {len(prioritized) - 20} more leagues")
    
    return prioritized

def categorize_leagues(leagues: List[Dict]) -> Dict[str, List[Dict]]:
    """Categorize leagues by success rate ranges."""
    categories = {
        'high_priority': [],    # 70-89%
        'medium_priority': [],  # 50-69%
        'low_priority': [],     # 20-49%
        'very_low': []         # 0-19%
    }
    
    for league in leagues:
        rate = league['success_rate']
        if rate >= 70:
            categories['high_priority'].append(league)
        elif rate >= 50:
            categories['medium_priority'].append(league)
        elif rate >= 20:
            categories['low_priority'].append(league)
        else:
            categories['very_low'].append(league)
    
    print(f"\n📊 LEAGUE CATEGORIES")
    print("=" * 40)
    print(f"🟢 High Priority (70-89%): {len(categories['high_priority'])} leagues")
    print(f"🟡 Medium Priority (50-69%): {len(categories['medium_priority'])} leagues")
    print(f"🟠 Low Priority (20-49%): {len(categories['low_priority'])} leagues")
    print(f"🔴 Very Low (0-19%): {len(categories['very_low'])} leagues")
    
    return categories

def generate_fix_recommendations(categories: Dict[str, List[Dict]]) -> List[str]:
    """Generate recommendations for which leagues to fix first."""
    recommendations = []
    
    print(f"\n🎯 FIX RECOMMENDATIONS")
    print("=" * 40)
    
    # High priority leagues (70-89%) - best candidates
    high_priority = sorted(categories['high_priority'], key=lambda x: x['success_rate'], reverse=True)
    if high_priority:
        print(f"\n🥇 IMMEDIATE FIXES (70-89% success rate):")
        for league in high_priority[:10]:  # Top 10
            recommendations.append(league['league_name'])
            print(f"   • {league['league_name']}: {league['success_rate']:.1f}% ({league['working_teams']}/{league['total_teams']})")
    
    # Medium priority leagues (50-69%) - good candidates
    medium_priority = sorted(categories['medium_priority'], key=lambda x: (x['success_rate'], x['total_teams']), reverse=True)
    if medium_priority:
        print(f"\n🥈 SECONDARY FIXES (50-69% success rate):")
        for league in medium_priority[:10]:  # Top 10
            recommendations.append(league['league_name'])
            print(f"   • {league['league_name']}: {league['success_rate']:.1f}% ({league['working_teams']}/{league['total_teams']})")
    
    # Skip very low success rate leagues for now (likely missing data)
    if categories['very_low']:
        print(f"\n⚠️  SKIP FOR NOW (0-19% success rate): {len(categories['very_low'])} leagues")
        print("   These likely have missing CSV data files")
    
    return recommendations

def main():
    """Main function."""
    print("🎯 FINDING LEAGUES UNDER 90% SUCCESS RATE")
    print("=" * 70)
    print("This will identify leagues that need fixing to reach 90%+ success rate")
    
    # Analyze all leagues
    leagues_under_90 = analyze_all_leagues()
    
    if not leagues_under_90:
        print("\n🎉 Amazing! All leagues have 90%+ success rate!")
        return
    
    print(f"\n📊 SUMMARY: Found {len(leagues_under_90)} leagues under 90% success rate")
    
    # Prioritize leagues
    prioritized_leagues = prioritize_leagues(leagues_under_90)
    
    # Categorize by success rate
    categories = categorize_leagues(leagues_under_90)
    
    # Generate recommendations
    recommendations = generate_fix_recommendations(categories)
    
    print(f"\n🚀 NEXT STEPS")
    print("=" * 30)
    print(f"1. Focus on {len(categories['high_priority'])} high-priority leagues (70-89%)")
    print(f"2. Create fix scripts for top {min(20, len(recommendations))} recommended leagues")
    print(f"3. Run batch fixes to improve success rates")
    print(f"4. Re-analyze to track progress")
    
    # Save recommendations to file
    with open('leagues_to_fix.txt', 'w') as f:
        f.write("# Leagues Under 90% Success Rate - Fix Recommendations\n\n")
        f.write("## High Priority (70-89%)\n")
        for league in categories['high_priority']:
            f.write(f"- {league['league_name']}: {league['success_rate']:.1f}% ({league['working_teams']}/{league['total_teams']})\n")
        
        f.write("\n## Medium Priority (50-69%)\n")
        for league in categories['medium_priority']:
            f.write(f"- {league['league_name']}: {league['success_rate']:.1f}% ({league['working_teams']}/{league['total_teams']})\n")
        
        f.write("\n## Low Priority (20-49%)\n")
        for league in categories['low_priority']:
            f.write(f"- {league['league_name']}: {league['success_rate']:.1f}% ({league['working_teams']}/{league['total_teams']})\n")
    
    print(f"\n💾 Saved detailed analysis to 'leagues_to_fix.txt'")

if __name__ == "__main__":
    main()