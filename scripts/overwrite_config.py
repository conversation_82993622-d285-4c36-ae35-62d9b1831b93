import os
import json
import traceback
import argparse


def format_dict(d, indent=4):
    if d is None:
        return [f"{' ' * indent}None,"]
    lines = []
    for key, value in d.items():
        if isinstance(value, dict):
            lines.append(f"{' ' * indent}\"{key}\": {{")
            lines.extend(format_dict(value, indent + 4))
            lines.append(f"{' ' * indent}}},")
        elif value is None:
            lines.append(f"{' ' * indent}\"{key}\": None,")
        else:
            lines.append(f"{' ' * indent}\"{key}\": \"{value}\",")
    return lines


def process_json_file(file_path):
    try:
        with open(file_path, "r") as f:
            data = json.load(f)
        league_name = (
            os.path.splitext(os.path.basename(file_path))[0]
            .upper()
            .replace("_CONFIG", "")
        )
        formatted_data = [f'    "{league_name}": {{']
        formatted_data.extend(format_dict(data, 8))
        formatted_data[-1] = formatted_data[-1].rstrip(",")  # Remove trailing comma
        formatted_data.append("    },")
        return "\n".join(formatted_data)
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON in file {file_path}: {e}")
        return None
    except Exception as e:
        print(f"Error processing file {file_path}: {e}")
        return None


def overwrite_config(config_path, json_dir, target_leagues=None):
    try:
        print(f"Attempting to write to config file: {config_path}")
        with open(config_path, "w") as config_file:
            config_file.write("LEAGUE_CONFIGS = {\n")
            print(f"Searching for JSON files in: {json_dir}")
            json_files = [f for f in os.listdir(json_dir) if f.endswith(".json")]

            # Filter files if target leagues specified
            if target_leagues:
                filtered_files = []
                for filename in json_files:
                    league_name = filename.replace("_config.json", "")
                    if league_name in target_leagues:
                        filtered_files.append(filename)
                json_files = filtered_files
                print(f"Filtered to {len(json_files)} JSON files for target leagues: {target_leagues}")
            else:
                print(f"Found {len(json_files)} JSON files")

            for filename in sorted(json_files):
                file_path = os.path.join(json_dir, filename)
                print(f"Processing file: {file_path}")
                formatted_data = process_json_file(file_path)
                if formatted_data:
                    config_file.write(formatted_data + "\n\n")
            config_file.write("}\n")
        print(f"JSON files have been successfully written to {config_path}")
    except Exception as e:
        print(f"Error writing to config file: {e}")
        print("Detailed traceback:")
        traceback.print_exc()


# Usage
if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Overwrite config with data from JSON database")
    parser.add_argument("--league", type=str, help="Process a specific league only")
    parser.add_argument("--leagues", type=str, help="Process specific leagues (comma-separated)")
    args = parser.parse_args()

    # Determine target leagues
    target_leagues = None
    if args.league:
        target_leagues = [args.league]
    elif args.leagues:
        target_leagues = [league.strip() for league in args.leagues.split(",")]

    # Update paths to match new project structure
    config_path = os.path.join("src", "scrapers", "generated_league_data.py")
    json_dir = os.path.join("src", "config", "json_database")
    overwrite_config(config_path, json_dir, target_leagues)
