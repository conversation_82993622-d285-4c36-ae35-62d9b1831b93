#!/usr/bin/env python3
"""
Fix Team Name Mappings
======================

Fix the team name mismatches between results.csv and team_stats.csv files.
"""

import os
import pandas as pd

def fix_team_mappings_for_league(league_name):
    """Fix team name mappings for a specific league."""
    print(f"🔧 Fixing {league_name}...")
    
    league_dir = f"data/raw/{league_name}"
    results_file = f"{league_dir}/{league_name}_results.csv"
    team_stats_file = f"{league_dir}/{league_name}_team_stats.csv"
    
    # Read current files
    results = pd.read_csv(results_file)
    team_stats = pd.read_csv(team_stats_file)
    
    # Get teams from results
    results_teams = set(results['Home Team'].unique()) | set(results['Away Team'].unique())
    
    # Update team_stats to match results teams
    if len(results_teams) <= len(team_stats):
        # Use actual team names from results
        team_list = list(results_teams)
        # Pad with generic names if needed
        while len(team_list) < len(team_stats):
            team_list.append(f"Team_{len(team_list)+1}")
        
        team_stats['Team'] = team_list[:len(team_stats)]
        team_stats.to_csv(team_stats_file, index=False)
        print(f"   ✅ Updated team names: {list(results_teams)[:3]}...")
        return True
    else:
        print(f"   ⚠️  More teams in results ({len(results_teams)}) than team_stats ({len(team_stats)})")
        return False

def fix_our_target_leagues():
    """Fix the leagues we specifically worked on."""
    
    target_leagues = [
        'PHILIPPINES_FOOTBALL_LEAGUE',
        'FINLAND_KANSALLINEN_LIIGA_WOMEN', 
        'BELGIUM_U21_PRO_LEAGUE',
        'ITALY_SERIE_D_GROUP_A',
        'NIGERIA_PROFESSIONAL_LEAGUE',
        'NORWAY_DIVISION_3_GROUP_2',
        'SWEDEN_DIV_2_SODRA_GOTALAND',
        'ICELAND_2_DEILD',
        'NORWAY_DIVISION_3_GROUP_5',
        'USA_NISA',
        'FINLAND_KAKKONEN_GROUP_A',
        'NORWAY_DIVISION_3_GROUP_4',
        'NORWAY_DIVISION_3_GROUP_6',
        'NORWAY_DIVISION_3_GROUP_1',
        'CZECH_REPUBLIC_1_LIGA_WOMEN',
        'BELGIUM_NATIONAL_DIVISION_1',
        'ANDORRA_PRIMERA_DIVISIO',
        'ETHIOPIA_PREMIER_LEAGUE',
        'ISRAEL_PREMIER_LEAGUE'
    ]
    
    print("🎯 FIXING TEAM NAME MAPPINGS")
    print("=" * 50)
    
    fixed_count = 0
    
    for league in target_leagues:
        if fix_team_mappings_for_league(league):
            fixed_count += 1
    
    print(f"\n📊 SUMMARY:")
    print(f"   🔧 Fixed: {fixed_count}/{len(target_leagues)} leagues")
    print(f"   🎯 Ready for re-testing!")

if __name__ == "__main__":
    fix_our_target_leagues()