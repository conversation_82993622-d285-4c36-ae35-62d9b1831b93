import sqlite3
from collections import defaultdict
from fuzzywuzzy import fuzz

def get_team_groups(teams, threshold=95):
    team_groups = []
    processed_teams = set()

    for team_id1, team_name1 in teams:
        if team_id1 in processed_teams:
            continue

        # Start a new group with the current team
        current_group = [(team_id1, team_name1)]
        processed_teams.add(team_id1)

        # Find other teams that are similar
        for team_id2, team_name2 in teams:
            if team_id2 in processed_teams:
                continue

            if fuzz.ratio(team_name1.lower(), team_name2.lower()) > threshold:
                current_group.append((team_id2, team_name2))
                processed_teams.add(team_id2)
        
        if len(current_group) > 1:
            team_groups.append(current_group)
            
    return team_groups

def main():
    conn = sqlite3.connect('data/football_betting.db')
    cursor = conn.cursor()

    # 1. Get all teams
    cursor.execute("SELECT team_id, team_name FROM teams")
    all_teams = cursor.fetchall()

    # 2. Group similar teams
    team_groups = get_team_groups(all_teams)

    for group in team_groups:
        # 3. Find the canonical team_id (use the one with the lowest team_id as canonical)
        canonical_team_id = min(group, key=lambda x: x[0])[0]
        team_ids_to_update = [team_id for team_id, _ in group if team_id != canonical_team_id]

        # 4. Update team_stats to use the canonical team_id
        if team_ids_to_update:
            placeholders = ', '.join('?' for _ in team_ids_to_update)
            update_query = f"UPDATE team_stats SET team_id = ? WHERE team_id IN ({placeholders})"
            cursor.execute(update_query, [canonical_team_id] + team_ids_to_update)
            print(f"Updated {cursor.rowcount} rows in team_stats for group {group[0][1]}.")

        # 5. Deleting duplicate team entries is disabled to prevent incorrect deletions.
        # if team_ids_to_update:
        #     placeholders = ', '.join('?' for _ in team_ids_to_update)
        #     delete_query = f"DELETE FROM teams WHERE team_id IN ({placeholders})"
        #     cursor.execute(delete_query, team_ids_to_update)
        #     print(f"Deleted {cursor.rowcount} duplicate teams for group {group[0][1]}.")

    conn.commit()
    conn.close()

if __name__ == "__main__":
    main()
