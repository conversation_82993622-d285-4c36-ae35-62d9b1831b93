#!/usr/bin/env python3
"""
Simple wrapper script to run H2H batch scraping.
This script provides an easy way to start the batch H2H scraping process.
"""

import sys
import os
import argparse

# Add src/scrapers to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src', 'scrapers'))

from h2h_automation import main as automation_main

def main():
    """Main entry point for the H2H batch scraping wrapper."""
    parser = argparse.ArgumentParser(
        description="Run H2H batch scraping with automatic restarts",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_h2h_batch.py                    # Process all leagues
  python run_h2h_batch.py --league ENGLAND_PREMIER_LEAGUE  # Process specific league
  python run_h2h_batch.py --progress         # Show current progress
  python run_h2h_batch.py --max-iterations 10  # Limit to 10 iterations
        """
    )
    
    parser.add_argument(
        "--league", 
        type=str, 
        help="Target a specific league (default: all leagues)"
    )
    parser.add_argument(
        "--max-iterations", 
        type=int, 
        help="Maximum number of iterations to run (default: unlimited)"
    )
    parser.add_argument(
        "--progress", 
        action="store_true", 
        help="Show current progress and exit"
    )
    
    args = parser.parse_args()
    
    print("🚀 H2H Batch Scraper")
    print("=" * 50)
    
    if args.progress:
        print("📊 Current Progress:")
        print("-" * 20)
    elif args.league:
        print(f"🎯 Target League: {args.league}")
    else:
        print("🌍 Processing All Leagues")
    
    if args.max_iterations:
        print(f"🔄 Max Iterations: {args.max_iterations}")
    
    print()
    
    # Set up sys.argv for the automation script
    sys.argv = ['h2h_automation.py']
    if args.league:
        sys.argv.extend(['--league', args.league])
    if args.max_iterations:
        sys.argv.extend(['--max-iterations', str(args.max_iterations)])
    if args.progress:
        sys.argv.append('--progress')
    
    # Run the automation
    automation_main()

if __name__ == "__main__":
    main()