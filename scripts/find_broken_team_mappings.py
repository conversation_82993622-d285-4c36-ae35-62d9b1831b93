#!/usr/bin/env python3
"""
Find Broken Team Mappings Across All Leagues

This script identifies teams that have canonical mapping issues where:
1. Team exists in database but has no stats
2. Team points to a canonical team that has stats
3. The canonical resolution isn't working properly

Generates a comprehensive markdown report for manual investigation.
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Tuple, Set
from collections import defaultdict

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def analyze_league_team_issues(league_name: str, db) -> Dict:
    """Analyze a single league for team mapping issues."""
    issues = {
        'league_name': league_name,
        'teams_without_stats': [],
        'teams_with_broken_canonical': [],
        'teams_with_working_canonical': [],
        'total_teams': 0,
        'teams_with_stats': 0,
        'canonical_mapping_issues': []
    }
    
    try:
        # Get all teams in the league
        all_teams = db.execute_query('''
            SELECT t.team_name, t.team_id, t.canonical_team_id, 
                   t2.team_name as canonical_name
            FROM teams t
            LEFT JOIN teams t2 ON t.canonical_team_id = t2.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY t.team_name
        ''', (league_name,))
        
        if all_teams.empty:
            return issues
        
        issues['total_teams'] = len(all_teams)
        
        for _, team in all_teams.iterrows():
            team_name = team['team_name']
            team_id = team['team_id']
            canonical_id = team['canonical_team_id']
            canonical_name = team['canonical_name']
            
            # Check if team has stats directly
            direct_stats = db.execute_query(
                'SELECT COUNT(*) as count FROM team_stats WHERE team_id = ?', 
                (team_id,)
            )
            has_direct_stats = not direct_stats.empty and direct_stats.iloc[0]['count'] > 0
            
            # Check if team gets stats via canonical resolution
            resolved_stats = db.get_team_stats(team_name, league_name)
            has_resolved_stats = not resolved_stats.empty
            
            # Check if canonical team has stats (if different from self)
            canonical_has_stats = False
            if canonical_id != team_id and canonical_name:
                canonical_stats = db.execute_query(
                    'SELECT COUNT(*) as count FROM team_stats WHERE team_id = ?',
                    (canonical_id,)
                )
                canonical_has_stats = not canonical_stats.empty and canonical_stats.iloc[0]['count'] > 0
            
            # Categorize the team
            if has_direct_stats:
                issues['teams_with_stats'] += 1
            elif has_resolved_stats:
                issues['teams_with_working_canonical'].append({
                    'team_name': team_name,
                    'canonical_name': canonical_name,
                    'team_id': team_id,
                    'canonical_id': canonical_id
                })
                issues['teams_with_stats'] += 1
            elif canonical_has_stats and not has_resolved_stats:
                # This is a broken canonical mapping!
                issues['teams_with_broken_canonical'].append({
                    'team_name': team_name,
                    'canonical_name': canonical_name,
                    'team_id': team_id,
                    'canonical_id': canonical_id,
                    'issue': 'Canonical team has stats but resolution not working'
                })
            elif canonical_id != team_id and canonical_name:
                # Team points to different canonical but neither has stats
                issues['canonical_mapping_issues'].append({
                    'team_name': team_name,
                    'canonical_name': canonical_name,
                    'team_id': team_id,
                    'canonical_id': canonical_id,
                    'issue': 'Points to canonical team but no stats available'
                })
            else:
                # Team has no stats and no working canonical mapping
                issues['teams_without_stats'].append({
                    'team_name': team_name,
                    'team_id': team_id,
                    'canonical_id': canonical_id,
                    'canonical_name': canonical_name or 'Self'
                })
    
    except Exception as e:
        issues['error'] = str(e)
    
    return issues

def check_config_mappings(league_name: str) -> Dict:
    """Check if league has config mappings that might help."""
    config_path = f"src/scrapers/league_configs/{league_name}.py"
    
    if not os.path.exists(config_path):
        return {}
    
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("config", config_path)
        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)
        
        # Get the current config
        current_config = getattr(config_module, 'CURRENT_CONFIG', None)
        if current_config and 'TEAM_NAME_MAPPING' in current_config:
            return current_config['TEAM_NAME_MAPPING']
        
        # Fallback: try to get TEAM_NAME_MAPPING directly
        team_mapping = getattr(config_module, 'TEAM_NAME_MAPPING', None)
        if team_mapping:
            return team_mapping
            
        return {}
    except Exception:
        return {}

def generate_markdown_report(all_issues: List[Dict], output_file: str):
    """Generate a comprehensive markdown report."""
    
    # Calculate summary statistics
    total_leagues = len(all_issues)
    leagues_with_issues = len([l for l in all_issues if l['teams_with_broken_canonical'] or l['teams_without_stats']])
    total_broken_canonical = sum(len(l['teams_with_broken_canonical']) for l in all_issues)
    total_without_stats = sum(len(l['teams_without_stats']) for l in all_issues)
    total_teams = sum(l['total_teams'] for l in all_issues)
    total_working_teams = sum(l['teams_with_stats'] for l in all_issues)
    
    with open(output_file, 'w') as f:
        f.write("# Team Mapping Issues Report\n\n")
        f.write("This report identifies teams across all leagues that have canonical mapping issues.\n\n")
        
        # Executive Summary
        f.write("## 📊 Executive Summary\n\n")
        f.write(f"- **Total Leagues Analyzed**: {total_leagues}\n")
        f.write(f"- **Leagues with Issues**: {leagues_with_issues}\n")
        f.write(f"- **Total Teams**: {total_teams:,}\n")
        f.write(f"- **Teams Working**: {total_working_teams:,}\n")
        f.write(f"- **Teams with Broken Canonical Mapping**: {total_broken_canonical}\n")
        f.write(f"- **Teams without Stats**: {total_without_stats}\n")
        f.write(f"- **Overall Success Rate**: {(total_working_teams/total_teams)*100:.1f}%\n\n")
        
        # Priority Issues
        f.write("## 🚨 Priority Issues (Broken Canonical Mappings)\n\n")
        f.write("These teams have canonical mappings pointing to teams with stats, but the resolution isn't working:\n\n")
        
        priority_count = 0
        for league_issues in all_issues:
            if league_issues['teams_with_broken_canonical']:
                f.write(f"### {league_issues['league_name']}\n\n")
                for team in league_issues['teams_with_broken_canonical']:
                    f.write(f"- **{team['team_name']}** → {team['canonical_name']} (IDs: {team['team_id']} → {team['canonical_id']})\n")
                    priority_count += 1
                f.write("\n")
        
        if priority_count == 0:
            f.write("✅ No broken canonical mappings found!\n\n")
        
        # Leagues with Most Issues
        f.write("## 📈 Leagues with Most Issues\n\n")
        leagues_by_issues = sorted(all_issues, 
                                 key=lambda x: len(x['teams_with_broken_canonical']) + len(x['teams_without_stats']), 
                                 reverse=True)
        
        f.write("| League | Total Teams | Working | Broken Canonical | No Stats | Success Rate |\n")
        f.write("|--------|-------------|---------|------------------|----------|-------------|\n")
        
        for league in leagues_by_issues[:20]:  # Top 20
            if league['total_teams'] > 0:
                success_rate = (league['teams_with_stats'] / league['total_teams']) * 100
                broken = len(league['teams_with_broken_canonical'])
                no_stats = len(league['teams_without_stats'])
                
                if broken > 0 or no_stats > 0:  # Only show leagues with issues
                    f.write(f"| {league['league_name']} | {league['total_teams']} | {league['teams_with_stats']} | {broken} | {no_stats} | {success_rate:.1f}% |\n")
        
        f.write("\n")
        
        # Detailed Issues by League
        f.write("## 🔍 Detailed Issues by League\n\n")
        
        for league_issues in sorted(all_issues, key=lambda x: x['league_name']):
            broken_canonical = league_issues['teams_with_broken_canonical']
            no_stats = league_issues['teams_without_stats']
            mapping_issues = league_issues['canonical_mapping_issues']
            
            if broken_canonical or no_stats or mapping_issues:
                f.write(f"### {league_issues['league_name']}\n\n")
                f.write(f"**Stats**: {league_issues['teams_with_stats']}/{league_issues['total_teams']} teams working\n\n")
                
                # Check for config mappings
                config_mappings = check_config_mappings(league_issues['league_name'])
                if config_mappings:
                    f.write(f"**Config Mappings Available**: {len(config_mappings)} mappings\n")
                    for current, old in list(config_mappings.items())[:5]:  # Show first 5
                        f.write(f"- {current} ← {old}\n")
                    if len(config_mappings) > 5:
                        f.write(f"- ... and {len(config_mappings) - 5} more\n")
                    f.write("\n")
                
                if broken_canonical:
                    f.write("**🚨 Broken Canonical Mappings**:\n")
                    for team in broken_canonical:
                        f.write(f"- `{team['team_name']}` → `{team['canonical_name']}` (Fix needed)\n")
                    f.write("\n")
                
                if mapping_issues:
                    f.write("**⚠️ Canonical Mapping Issues**:\n")
                    for team in mapping_issues:
                        f.write(f"- `{team['team_name']}` → `{team['canonical_name']}` (No stats available)\n")
                    f.write("\n")
                
                if no_stats:
                    f.write("**📊 Teams Without Stats**:\n")
                    for team in no_stats[:10]:  # Limit to first 10
                        canonical_info = f" → {team['canonical_name']}" if team['canonical_name'] != 'Self' else ""
                        f.write(f"- `{team['team_name']}`{canonical_info}\n")
                    if len(no_stats) > 10:
                        f.write(f"- ... and {len(no_stats) - 10} more teams\n")
                    f.write("\n")
                
                f.write("---\n\n")
        
        # Recommendations
        f.write("## 💡 Recommendations\n\n")
        f.write("1. **Fix Broken Canonical Mappings**: These are the highest priority as the data exists but isn't accessible\n")
        f.write("2. **Use Config File Mappings**: Many leagues have team name mappings in their config files\n")
        f.write("3. **Investigate Teams Without Stats**: These may need data collection or different canonical mappings\n")
        f.write("4. **Focus on High-Impact Leagues**: Start with leagues that have many teams and high issue counts\n\n")
        
        f.write("## 🔧 Next Steps\n\n")
        f.write("1. Review the broken canonical mappings and fix the resolution logic\n")
        f.write("2. Apply config file mappings where available\n")
        f.write("3. Investigate teams without stats to determine if they need data or different mappings\n")
        f.write("4. Re-run this analysis after fixes to measure improvement\n")

def main():
    """Main function to analyze all leagues."""
    print("🔍 ANALYZING TEAM MAPPING ISSUES ACROSS ALL LEAGUES")
    print("=" * 70)
    print("This will identify teams that have canonical mapping issues...")
    
    all_issues = []
    
    with get_database() as db:
        # Get all leagues
        leagues = db.execute_query("SELECT league_name FROM leagues ORDER BY league_name")
        
        if leagues.empty:
            print("❌ No leagues found in database")
            return
        
        total_leagues = len(leagues)
        print(f"📊 Found {total_leagues} leagues to analyze")
        
        # Analyze each league
        for i, league_row in leagues.iterrows():
            league_name = league_row['league_name']
            print(f"🔍 Analyzing {league_name} ({i+1}/{total_leagues})")
            
            league_issues = analyze_league_team_issues(league_name, db)
            all_issues.append(league_issues)
            
            # Show progress for leagues with issues
            broken = len(league_issues['teams_with_broken_canonical'])
            no_stats = len(league_issues['teams_without_stats'])
            if broken > 0 or no_stats > 0:
                print(f"   ⚠️  Issues found: {broken} broken canonical, {no_stats} without stats")
    
    # Generate report
    output_file = "team_mapping_issues_report.md"
    print(f"\n📝 Generating report: {output_file}")
    generate_markdown_report(all_issues, output_file)
    
    # Summary
    total_broken = sum(len(l['teams_with_broken_canonical']) for l in all_issues)
    total_without_stats = sum(len(l['teams_without_stats']) for l in all_issues)
    leagues_with_issues = len([l for l in all_issues if l['teams_with_broken_canonical'] or l['teams_without_stats']])
    
    print(f"\n📊 ANALYSIS COMPLETE")
    print(f"Leagues with issues: {leagues_with_issues}/{total_leagues}")
    print(f"Teams with broken canonical mappings: {total_broken}")
    print(f"Teams without stats: {total_without_stats}")
    print(f"Report saved to: {output_file}")
    
    if total_broken > 0:
        print(f"\n🚨 PRIORITY: {total_broken} teams have broken canonical mappings that need immediate fixing!")
    else:
        print(f"\n✅ No broken canonical mappings found!")

if __name__ == "__main__":
    main()
