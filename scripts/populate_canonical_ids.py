#!/usr/bin/env python3
"""
Populate canonical_team_id for all teams in the database.
"""

import sqlite3

def main():
    db_path = 'data/football_betting.db'
    
    if not os.path.exists(db_path):
        print(f"Database not found: {db_path}")
        return

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get all teams that are missing a canonical_team_id
        cursor.execute("SELECT team_id FROM teams WHERE canonical_team_id IS NULL")
        teams_to_update = cursor.fetchall()

        if not teams_to_update:
            print("All teams already have a canonical ID.")
            return

        # Update canonical_team_id to its own team_id
        for team in teams_to_update:
            cursor.execute("UPDATE teams SET canonical_team_id = ? WHERE team_id = ?", (team[0], team[0]))

        conn.commit()
        print(f"Successfully updated {len(teams_to_update)} teams.")

    except sqlite3.Error as e:
        print(f"Database error: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    import os
    main()
