#!/usr/bin/env python3
"""
Enhanced orchestrator script for the betting project with comprehensive improvements.

This version includes:
1. Proper sequential processing with performance enhancements
2. Intelligent caching & dependency checking
3. Enhanced input validation & team name resolution
4. Batch resume capability
5. Better error handling and progress tracking
6. Parallelism only where safe (data scraping operations)
"""

import os
import sys
import argparse
import subprocess
import yaml
import logging
import concurrent.futures
import time
import json
import hashlib
from typing import List, Dict, Tuple, Optional, Set, Any
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from datetime import datetime, timedelta
from fuzzywuzzy import fuzz

# Try to import readline for better input experience
try:
    import readline
    READLINE_AVAILABLE = True
except ImportError:
    READLINE_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - [%(funcName)s] - %(message)s",
    handlers=[
        logging.FileHandler("predict_match_enhanced.log"),
        logging.StreamHandler()
    ]
)

class StepStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CACHED = "cached"

@dataclass
class WorkflowStep:
    name: str
    function: callable
    dependencies: Set[str]
    cache_key: Optional[str] = None
    cache_duration_hours: int = 24
    status: StepStatus = StepStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: Optional[str] = None

@dataclass
class CacheEntry:
    timestamp: float
    data_hash: str
    success: bool
    duration: float
    metadata: Dict[str, Any] = None

class DataFreshnessChecker:
    """Checks if data needs updating based on timestamps and dependencies."""
    
    def __init__(self, league: str):
        self.league = league
        self.cache_dir = Path(f"data/cache/{league}")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.cache_file = self.cache_dir / "freshness_cache.json"
        
    def load_cache(self) -> Dict[str, CacheEntry]:
        """Load cache from disk."""
        if not self.cache_file.exists():
            return {}
        
        try:
            with open(self.cache_file, 'r') as f:
                cache_data = json.load(f)
            
            cache = {}
            for key, data in cache_data.items():
                cache[key] = CacheEntry(**data)
            return cache
        except Exception as e:
            logging.warning(f"Failed to load cache: {e}")
            return {}
    
    def save_cache(self, cache: Dict[str, CacheEntry]):
        """Save cache to disk."""
        try:
            cache_data = {key: asdict(entry) for key, entry in cache.items()}
            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
        except Exception as e:
            logging.warning(f"Failed to save cache: {e}")
    
    def get_file_hash(self, file_path: str) -> str:
        """Get hash of file contents."""
        if not os.path.exists(file_path):
            return ""
        
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return ""
    
    def is_step_fresh(self, step_name: str, cache_duration_hours: int = 24) -> bool:
        """Check if a step's results are still fresh."""
        cache = self.load_cache()
        
        if step_name not in cache:
            return False
        
        entry = cache[step_name]
        age_hours = (time.time() - entry.timestamp) / 3600
        
        if age_hours > cache_duration_hours:
            logging.info(f"Step {step_name} cache expired ({age_hours:.1f}h > {cache_duration_hours}h)")
            return False
        
        if not entry.success:
            logging.info(f"Step {step_name} previously failed, retrying")
            return False
        
        return True
    
    def mark_step_completed(self, step_name: str, success: bool, duration: float, metadata: Dict = None):
        """Mark a step as completed in cache."""
        cache = self.load_cache()
        
        cache[step_name] = CacheEntry(
            timestamp=time.time(),
            data_hash="",  # Could be enhanced to track actual data changes
            success=success,
            duration=duration,
            metadata=metadata or {}
        )
        
        self.save_cache(cache)
    
    def check_data_freshness(self, force: bool = False) -> Dict[str, bool]:
        """Check which data needs updating."""
        if force:
            return {
                'urls_fresh': False,
                'team_mapping_fresh': False,
                'config_fresh': False,
                'scraped_data_fresh': False,
                'stats_fresh': False
            }
        
        return {
            'urls_fresh': self.is_step_fresh('extract_urls', 48),  # URLs change less frequently
            'team_mapping_fresh': self.is_step_fresh('map_team_names', 48),
            'config_fresh': self.is_step_fresh('overwrite_config', 24),
            'scraped_data_fresh': self.is_step_fresh('scrape_data', 6),  # Data changes more frequently
            'stats_fresh': self.is_step_fresh('calculate_stats', 6)
        }

class TeamNameResolver:
    """Enhanced team name validation and fuzzy matching."""
    
    def __init__(self, league: str):
        self.league = league
        self._team_cache = None
    
    def load_team_data(self) -> Optional[List[str]]:
        """Load available teams for the league."""
        if self._team_cache is not None:
            return self._team_cache
        
        try:
            # Try to load from team stats file
            team_stats_file = f"data/raw/{self.league}/{self.league}_team_stats.csv"
            if os.path.exists(team_stats_file):
                import pandas as pd
                df = pd.read_csv(team_stats_file)
                if 'Team' in df.columns:
                    self._team_cache = df['Team'].unique().tolist()
                    return self._team_cache
            
            # Fallback: try to load from config
            config_file = f"src/config/json_database/{self.league}_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    team_urls = config.get('TEAM_URLS', {})
                    if team_urls:
                        self._team_cache = list(team_urls.keys())
                        return self._team_cache
            
            logging.warning(f"No team data found for {self.league}")
            return None
            
        except Exception as e:
            logging.error(f"Error loading team data for {self.league}: {e}")
            return None
    
    def find_best_team_match(self, team_name: str, available_teams: List[str]) -> Dict[str, Any]:
        """Find the best fuzzy match for a team name."""
        if not available_teams:
            return {'team': team_name, 'confidence': 0.0, 'exact_match': False}
        
        # Check for exact match first
        if team_name in available_teams:
            return {'team': team_name, 'confidence': 1.0, 'exact_match': True}
        
        # Find best fuzzy match
        best_match = None
        best_score = 0
        
        for available_team in available_teams:
            # Try different fuzzy matching strategies
            scores = [
                fuzz.ratio(team_name.lower(), available_team.lower()),
                fuzz.partial_ratio(team_name.lower(), available_team.lower()),
                fuzz.token_sort_ratio(team_name.lower(), available_team.lower()),
                fuzz.token_set_ratio(team_name.lower(), available_team.lower())
            ]
            
            # Use the maximum score
            score = max(scores) / 100.0
            
            if score > best_score:
                best_score = score
                best_match = available_team
        
        return {
            'team': best_match or team_name,
            'confidence': best_score,
            'exact_match': False,
            'original': team_name
        }
    
    def validate_and_resolve_teams(self, home_team: str, away_team: str) -> Tuple[str, str, bool, List[str]]:
        """Validate teams exist and resolve fuzzy matches."""
        available_teams = self.load_team_data()
        warnings = []
        
        if not available_teams:
            warnings.append(f"Could not load team data for {self.league}")
            return home_team, away_team, False, warnings
        
        # Resolve home team
        home_match = self.find_best_team_match(home_team, available_teams)
        if not home_match['exact_match']:
            if home_match['confidence'] < 0.8:
                warnings.append(f"Low confidence match for home team '{home_team}' -> '{home_match['team']}' ({home_match['confidence']:.2f})")
            else:
                warnings.append(f"Fuzzy matched home team '{home_team}' -> '{home_match['team']}' ({home_match['confidence']:.2f})")
        
        # Resolve away team
        away_match = self.find_best_team_match(away_team, available_teams)
        if not away_match['exact_match']:
            if away_match['confidence'] < 0.8:
                warnings.append(f"Low confidence match for away team '{away_team}' -> '{away_match['team']}' ({away_match['confidence']:.2f})")
            else:
                warnings.append(f"Fuzzy matched away team '{away_team}' -> '{away_match['team']}' ({away_match['confidence']:.2f})")
        
        # Check if teams are the same
        if home_match['team'] == away_match['team']:
            warnings.append(f"Home and away teams resolved to the same team: '{home_match['team']}'")
            return home_team, away_team, False, warnings
        
        success = (home_match['confidence'] >= 0.6 and away_match['confidence'] >= 0.6)
        
        return home_match['team'], away_match['team'], success, warnings

class BatchProgressManager:
    """Manages progress and resume capability for batch operations."""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.progress_file = f"{config_path}.progress"
        self.failed_file = f"{config_path}.failed"
    
    def load_progress(self) -> Set[str]:
        """Load completed matches from progress file."""
        if not os.path.exists(self.progress_file):
            return set()
        
        try:
            with open(self.progress_file, 'r') as f:
                return set(line.strip() for line in f if line.strip())
        except Exception as e:
            logging.warning(f"Failed to load progress: {e}")
            return set()
    
    def load_failed(self) -> Set[str]:
        """Load failed matches from failed file."""
        if not os.path.exists(self.failed_file):
            return set()
        
        try:
            with open(self.failed_file, 'r') as f:
                return set(line.strip() for line in f if line.strip())
        except Exception as e:
            logging.warning(f"Failed to load failed matches: {e}")
            return set()
    
    def mark_completed(self, match_id: str):
        """Mark a match as completed."""
        try:
            with open(self.progress_file, 'a') as f:
                f.write(f"{match_id}\n")
        except Exception as e:
            logging.warning(f"Failed to mark {match_id} as completed: {e}")
    
    def mark_failed(self, match_id: str, error: str):
        """Mark a match as failed."""
        try:
            with open(self.failed_file, 'a') as f:
                f.write(f"{match_id}\t{error}\t{datetime.now().isoformat()}\n")
        except Exception as e:
            logging.warning(f"Failed to mark {match_id} as failed: {e}")
    
    def get_match_id(self, match: Dict[str, str]) -> str:
        """Generate a unique ID for a match."""
        league = match.get('league', '')
        home = match.get('home_team', match.get('home', ''))
        away = match.get('away_team', match.get('away', ''))
        return f"{league}_{home}_{away}".replace(' ', '_')
    
    def cleanup_progress_files(self):
        """Remove progress files after successful completion."""
        for file_path in [self.progress_file, self.failed_file]:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logging.info(f"Cleaned up progress file: {file_path}")
                except Exception as e:
                    logging.warning(f"Failed to cleanup {file_path}: {e}")

class ParallelWorkflowExecutor:
    """Executes workflow steps in parallel while respecting dependencies."""
    
    def __init__(self, max_workers: int = 3, league: str = None, force: bool = False):
        self.max_workers = max_workers
        self.league = league
        self.force = force
        self.steps: Dict[str, WorkflowStep] = {}
        self.completed_steps: Set[str] = set()
        self.failed_steps: Set[str] = set()
        self.freshness_checker = DataFreshnessChecker(league) if league else None
        
    def add_step(self, step: WorkflowStep):
        """Add a step to the workflow."""
        self.steps[step.name] = step
        
    def get_ready_steps(self) -> List[str]:
        """Get steps that are ready to run (all dependencies completed)."""
        ready = []
        for step_name, step in self.steps.items():
            if (step.status == StepStatus.PENDING and 
                step.dependencies.issubset(self.completed_steps)):
                ready.append(step_name)
        
        return ready
    
    def check_step_cache(self, step: WorkflowStep, force: bool = False) -> bool:
        """Check if step can be skipped due to fresh cache."""
        if not self.freshness_checker:
            return False
        
        # If force is True, skip cache check and force re-execution
        if force:
            return False
        
        if self.freshness_checker.is_step_fresh(step.name, step.cache_duration_hours):
            step.status = StepStatus.CACHED
            self.completed_steps.add(step.name)
            logging.info(f"⚡ Skipped {step.name} (cached)")
            return True
        
        return False
    
    def execute_step(self, step_name: str) -> bool:
        """Execute a single step."""
        step = self.steps[step_name]
        
        # Check cache first (unless force is True)
        if self.check_step_cache(step, self.force):
            return True
        
        step.status = StepStatus.RUNNING
        step.start_time = time.time()
        
        try:
            logging.info(f"🚀 Starting step: {step_name}")
            result = step.function()
            step.end_time = time.time()
            duration = step.end_time - step.start_time
            
            if result:
                step.status = StepStatus.COMPLETED
                self.completed_steps.add(step_name)
                
                # Update cache
                if self.freshness_checker:
                    self.freshness_checker.mark_step_completed(step_name, True, duration)
                
                logging.info(f"✅ Completed step: {step_name} ({duration:.1f}s)")
                return True
            else:
                step.status = StepStatus.FAILED
                self.failed_steps.add(step_name)
                step.error_message = "Function returned False"
                
                # Update cache
                if self.freshness_checker:
                    self.freshness_checker.mark_step_completed(step_name, False, duration)
                
                logging.error(f"❌ Failed step: {step_name} ({duration:.1f}s)")
                return False
                
        except Exception as e:
            step.end_time = time.time()
            duration = step.end_time - step.start_time
            step.status = StepStatus.FAILED
            step.error_message = str(e)
            self.failed_steps.add(step_name)
            
            # Update cache
            if self.freshness_checker:
                self.freshness_checker.mark_step_completed(step_name, False, duration)
            
            logging.error(f"❌ Failed step: {step_name} ({duration:.1f}s) - {e}")
            return False
    
    def execute_workflow(self) -> bool:
        """Execute the entire workflow with parallel processing."""
        logging.info(f"🔄 Starting parallel workflow execution with {self.max_workers} workers")
        
        total_steps = len(self.steps)
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            while len(self.completed_steps) + len(self.failed_steps) < total_steps:
                # Get steps ready to run
                ready_steps = self.get_ready_steps()
                
                if not ready_steps:
                    # No steps ready - check if we're done or stuck
                    remaining_steps = [name for name, step in self.steps.items() 
                                     if step.status == StepStatus.PENDING]
                    if remaining_steps:
                        logging.error(f"❌ Workflow stuck! Remaining steps: {remaining_steps}")
                        logging.error(f"Failed steps: {self.failed_steps}")
                        logging.error(f"Completed steps: {self.completed_steps}")
                        # Debug: show what each remaining step is waiting for
                        for step_name in remaining_steps:
                            step = self.steps[step_name]
                            missing_deps = step.dependencies - self.completed_steps
                            logging.error(f"  {step_name} waiting for: {missing_deps}")
                        return False
                    break  # All done
                
                # Submit ready steps and wait for completion
                future_to_step = {}
                for step_name in ready_steps:
                    future = executor.submit(self.execute_step, step_name)
                    future_to_step[future] = step_name
                
                # Wait for ALL submitted steps to complete
                if future_to_step:
                    done_futures, _ = concurrent.futures.wait(future_to_step.keys(), return_when=concurrent.futures.ALL_COMPLETED)
                    
                    for future in done_futures:
                        step_name = future_to_step[future]
                        try:
                            result = future.result()
                            if not result:
                                # Check if this failure blocks other steps
                                blocked_steps = self._get_blocked_steps(step_name)
                                if blocked_steps:
                                    logging.error(f"❌ Step {step_name} failure blocks: {blocked_steps}")
                        except Exception as e:
                            logging.error(f"❌ Unexpected error in step {step_name}: {e}")
                
                # Continue to next iteration to check for new ready steps
        
        # Final status
        success_count = len(self.completed_steps)
        failure_count = len(self.failed_steps)
        
        logging.info(f"📊 Workflow completed: {success_count} succeeded, {failure_count} failed")
        
        if failure_count > 0:
            logging.error("❌ Workflow failed due to step failures:")
            for step_name in self.failed_steps:
                step = self.steps[step_name]
                logging.error(f"  - {step_name}: {step.error_message}")
            return False
        
        logging.info("✅ Workflow completed successfully!")
        return True
    
    def _get_blocked_steps(self, failed_step: str) -> List[str]:
        """Get steps that are blocked by a failed step."""
        blocked = []
        for step_name, step in self.steps.items():
            if failed_step in step.dependencies and step.status == StepStatus.PENDING:
                blocked.append(step_name)
        return blocked
    
    def print_execution_summary(self):
        """Print a summary of execution times and status."""
        logging.info("\n" + "="*60)
        logging.info("📊 EXECUTION SUMMARY")
        logging.info("="*60)
        
        total_time = 0
        cached_count = 0
        
        for step_name, step in self.steps.items():
            duration = ""
            if step.start_time and step.end_time:
                step_duration = step.end_time - step.start_time
                duration = f"({step_duration:.1f}s)"
                total_time += step_duration
            
            status_emoji = {
                StepStatus.COMPLETED: "✅",
                StepStatus.FAILED: "❌", 
                StepStatus.SKIPPED: "⏭️",
                StepStatus.PENDING: "⏳",
                StepStatus.RUNNING: "🔄",
                StepStatus.CACHED: "⚡"
            }
            
            emoji = status_emoji.get(step.status, "❓")
            logging.info(f"{emoji} {step_name}: {step.status.value} {duration}")
            
            if step.status == StepStatus.CACHED:
                cached_count += 1
            
            if step.error_message:
                logging.info(f"    Error: {step.error_message}")
        
        logging.info(f"\n⏱️ Total execution time: {total_time:.1f}s")
        if cached_count > 0:
            logging.info(f"⚡ Cached steps: {cached_count}")

def run_command(command: List[str], cwd: Optional[str] = None) -> bool:
    """Run a command in a subprocess and handle errors."""
    try:
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=cwd
        )
        stdout, stderr = process.communicate()

        if process.returncode != 0:
            logging.error(f"Error executing command: {' '.join(command)}")
            logging.error(f"Return code: {process.returncode}")
            if stdout:
                logging.error(f"stdout:\n{stdout}")
            if stderr:
                logging.error(f"stderr:\n{stderr}")
            return False
        
        logging.debug(f"Successfully executed: {' '.join(command)}")
        if stdout:
            logging.debug(f"stdout:\n{stdout}")
        return True

    except FileNotFoundError:
        logging.error(f"Command not found: {command[0]}")
        return False
    except Exception as e:
        logging.error(f"An unexpected error occurred: {e}")
        return False

def run_command_with_progress(command: List[str], operation_name: str, cwd: Optional[str] = None) -> bool:
    """Run a command with real-time progress monitoring."""
    import threading
    import queue
    
    try:
        # Start the process
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # Combine stderr with stdout
            text=True,
            bufsize=1,  # Line buffered
            universal_newlines=True,
            cwd=cwd
        )
        
        # Queue to collect output
        output_queue = queue.Queue()
        
        def read_output():
            """Read output from process in separate thread."""
            try:
                for line in iter(process.stdout.readline, ''):
                    output_queue.put(('line', line.rstrip()))
                output_queue.put(('done', None))
            except Exception as e:
                output_queue.put(('error', str(e)))
        
        # Start output reading thread
        output_thread = threading.Thread(target=read_output)
        output_thread.daemon = True
        output_thread.start()
        
        # Progress tracking variables
        start_time = time.time()
        last_progress_time = start_time
        progress_indicators = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        progress_index = 0
        
        # Keywords to look for in output to show meaningful progress
        progress_keywords = [
            'Starting', 'Processing', 'Scraping', 'Completed', 'Found', 'Saving',
            'iteration', 'match', 'team', 'league', 'URL', 'data', 'results',
            'SUCCESS', 'INFO', 'WARNING', 'ERROR', '✅', '❌', '🔄', '📊'
        ]
        
        logging.info(f"🚀 {operation_name} started - monitoring progress...")
        
        # Monitor output and show progress
        while True:
            try:
                # Check for new output (non-blocking)
                try:
                    msg_type, content = output_queue.get(timeout=1.0)
                except queue.Empty:
                    # No output for 1 second, show spinner
                    current_time = time.time()
                    elapsed = current_time - start_time
                    
                    if current_time - last_progress_time >= 2.0:  # Update every 2 seconds
                        spinner = progress_indicators[progress_index % len(progress_indicators)]
                        progress_index += 1
                        logging.info(f"{spinner} {operation_name} in progress... ({elapsed:.0f}s elapsed)")
                        last_progress_time = current_time
                    continue
                
                if msg_type == 'done':
                    break
                elif msg_type == 'error':
                    logging.error(f"Error reading output: {content}")
                    break
                elif msg_type == 'line':
                    # Filter and show meaningful output
                    line = content.strip()
                    if line:
                        # Check if line contains progress keywords
                        show_line = any(keyword.lower() in line.lower() for keyword in progress_keywords)
                        
                        # Always show lines with specific patterns
                        if any(pattern in line for pattern in ['✅', '❌', '🔄', '📊', 'SUCCESS', 'COMPLETED', 'ERROR', 'WARNING']):
                            show_line = True
                        
                        # Show lines that look like progress updates
                        if any(pattern in line.lower() for pattern in ['processing', 'scraping', 'found', 'completed', 'iteration']):
                            show_line = True
                        
                        # Show numbered progress (like "1/10", "Match 5", etc.)
                        if any(char.isdigit() for char in line) and any(sep in line for sep in ['/', 'of', 'Match', '#']):
                            show_line = True
                        
                        if show_line:
                            # Clean up the line for better display
                            clean_line = line.replace('[INFO]', '').replace('[WARNING]', '⚠️').replace('[ERROR]', '❌')
                            clean_line = clean_line.strip()
                            if clean_line:
                                logging.info(f"📋 {operation_name}: {clean_line}")
            
            except KeyboardInterrupt:
                logging.warning(f"⚠️ {operation_name} interrupted by user")
                process.terminate()
                return False
        
        # Wait for process to complete
        return_code = process.wait()
        total_time = time.time() - start_time
        
        if return_code == 0:
            logging.info(f"✅ {operation_name} completed successfully in {total_time:.1f}s")
            return True
        else:
            logging.error(f"❌ {operation_name} failed with return code {return_code} after {total_time:.1f}s")
            return False
            
    except FileNotFoundError:
        logging.error(f"Command not found: {command[0]}")
        return False
    except Exception as e:
        logging.error(f"An unexpected error occurred during {operation_name}: {e}")
        return False

# Original workflow functions (enhanced with better error handling)
def extract_urls(league: str, force: bool) -> bool:
    """Run the URL extraction script."""
    logging.info(f"Extracting URLs for {league}...")
    command = ["python3", "src/url_extractor.py", "--league", league]
    if force:
        command.append("--force")
    return run_command(command)

def map_team_names(league: str) -> bool:
    """Run the team name mapping script."""
    logging.info(f"Mapping team names for {league}...")
    command = ["python3", "scripts/team_name_mapper.py", "--league", league]
    return run_command(command)

def overwrite_config(league: str) -> bool:
    """Run the overwrite config script."""
    logging.info(f"Overwriting config for {league}...")
    command = ["python3", "scripts/overwrite_config.py", "--league", league]
    return run_command(command)

def split_config(league: str) -> bool:
    """Run the split config script."""
    logging.info(f"Splitting config for {league}...")
    command = ["python3", "src/scrapers/split_config.py", "--league", league]
    return run_command(command)

def format_dictionaries() -> bool:
    """Run the format dictionaries script."""
    logging.info("Formatting dictionaries...")
    command = ["python3", "scripts/format_config_dictionaries.py"]
    return run_command(command)

def remove_match_ids() -> bool:
    """Run the remove match IDs script."""
    logging.info("Removing match IDs...")
    command = ["python3", "scripts/remove_match_ids.py"]
    return run_command(command)

def scrape_data(league: str) -> bool:
    """Run the data scraping script with progress monitoring."""
    logging.info(f"🔄 Starting data scraping for {league}...")
    logging.info("💡 This includes league stats, team stats, and match results")
    command = ["python3", "src/scrapers/main.py", "--league", league, "--refresh-league-data", "--refresh-results"]
    return run_command_with_progress(command, "Data scraping")

def calculate_stats(league: str) -> bool:
    """Run the calculate last 8 stats script."""
    logging.info(f"Calculating last 8 stats for {league}...")
    command = ["python3", "src/utils/calculate_last_8_stats.py", "--league", league]
    return run_command(command)

def scrape_h2h_data(league: str) -> bool:
    """Run the H2H and recent results scraping script with progress monitoring."""
    logging.info(f"🔄 Starting H2H data scraping for {league}...")
    logging.info("💡 This may take several minutes - progress will be shown below")
    command = ["bash", "scripts/run_h2h_automation.sh", "--league", league]
    return run_command_with_progress(command, "H2H scraping")

def scrape_recent_results(league: str) -> bool:
    """Run the recent results scraping script with progress monitoring."""
    logging.info(f"🔄 Starting recent results scraping for {league}...")
    logging.info("💡 This may take several minutes - progress will be shown below")
    command = ["bash", "scripts/run_h2h_automation.sh", "--league", league, "--recent-results-only"]
    return run_command_with_progress(command, "Recent results scraping")

def generate_prediction(league: str, home_team: str, away_team: str) -> bool:
    """Run the prediction script."""
    logging.info(f"Generating prediction for {league}: {home_team} vs {away_team}...")
    command = ["python3", "src/main.py", "--league", league, "--home", home_team, "--away", away_team]
    return run_command(command)

def create_sequential_workflow_for_league(league: str, force: bool, parallel_scraping: bool) -> ParallelWorkflowExecutor:
    """Create a sequential workflow that maintains correct order while allowing parallelism where safe."""
    max_workers = 10 if parallel_scraping else 3
    logging.info(f"Setting max workers to: {max_workers}")
    executor = ParallelWorkflowExecutor(max_workers=max_workers, league=league, force=force)
    
    # Define workflow steps with proper sequential dependencies
    # Following the required sequence: url extraction -> team name mapping -> overwrite config -> 
    # split config -> format config dictionaries -> remove match ids -> data scraping -> predictions
    steps = [
        # Step 1: URL extraction (must be first)
        WorkflowStep("extract_urls", lambda: extract_urls(league, force), set(),
                    cache_duration_hours=24),
        
        # Step 2: Team mapping (depends on URL extraction)
        WorkflowStep("map_team_names", lambda: map_team_names(league), {"extract_urls"},
                    cache_duration_hours=24),
        
        # Step 3: Overwrite config (depends on team mapping)
        WorkflowStep("overwrite_config", lambda: overwrite_config(league), {"map_team_names"},
                    cache_duration_hours=12),
        
        # Step 4: Split config (depends on overwrite config)
        WorkflowStep("split_config", lambda: split_config(league), {"overwrite_config"},
                    cache_duration_hours=12),
        
        # Step 5: Format config dictionaries (depends on split config)
        WorkflowStep("format_dictionaries", format_dictionaries, {"split_config"}, 
                    cache_duration_hours=48),
        
        # Step 6: Remove match IDs (depends on format dictionaries)
        WorkflowStep("remove_match_ids", remove_match_ids, {"format_dictionaries"}, 
                    cache_duration_hours=48),
        
        # Step 7: Data scraping (depends on remove match ids - this is where parallelism can start)
        WorkflowStep("scrape_data", lambda: scrape_data(league), {"remove_match_ids"},
                    cache_duration_hours=6),
        
        # These can run in parallel after data scraping
        WorkflowStep("scrape_h2h_data", lambda: scrape_h2h_data(league), {"scrape_data"},
                    cache_duration_hours=6),
        WorkflowStep("calculate_stats", lambda: calculate_stats(league), {"scrape_data"},
                    cache_duration_hours=6),
        
        # Recent results scraping (depends on H2H data)
        WorkflowStep("scrape_recent_results", lambda: scrape_recent_results(league), {"scrape_h2h_data"},
                    cache_duration_hours=3),
    ]
    
    for step in steps:
        executor.add_step(step)
    
    return executor

def run_enhanced_workflow(league: str, home_team: str, away_team: str, force: bool = False, parallel_scraping: bool = True) -> bool:
    """Execute the enhanced sequential workflow with performance improvements."""
    logging.info(f"🚀 Starting ENHANCED SEQUENTIAL workflow for {league}: {home_team} vs {away_team}")
    
    # Step 1: Validate and resolve team names
    logging.info("🔍 Validating team names...")
    resolver = TeamNameResolver(league)
    resolved_home, resolved_away, success, warnings = resolver.validate_and_resolve_teams(home_team, away_team)
    
    if warnings:
        for warning in warnings:
            logging.warning(f"⚠️ {warning}")
    
    if not success:
        logging.error(f"❌ Team validation failed for {league}")
        return False
    
    if resolved_home != home_team or resolved_away != away_team:
        logging.info(f"📝 Team names resolved: '{home_team}' -> '{resolved_home}', '{away_team}' -> '{resolved_away}'")
        home_team, away_team = resolved_home, resolved_away
    
    # Step 2: Create and execute the sequential workflow with performance enhancements
    workflow = create_sequential_workflow_for_league(league, force, parallel_scraping)
    
    start_time = time.time()
    success = workflow.execute_workflow()
    end_time = time.time()
    
    # Print execution summary
    workflow.print_execution_summary()
    
    total_time = end_time - start_time
    logging.info(f"⏱️ Total workflow time: {total_time:.1f} seconds")
    
    if not success:
        logging.error(f"❌ Enhanced sequential workflow failed for {league}")
        return False
    
    # Step 3: Run the final prediction step
    logging.info("🎯 Running final prediction...")
    if not generate_prediction(league, home_team, away_team):
        logging.error(f"❌ Prediction generation failed for {league}")
        return False
    
    logging.info(f"✅ Enhanced sequential workflow completed successfully for {league}: {home_team} vs {away_team}")
    return True

def run_enhanced_batch_workflow(config_path: str, force: bool = False) -> None:
    """Execute enhanced batch workflow with resume capability."""
    logging.info(f"🚀 Starting enhanced batch workflow from {config_path}...")
    
    # Load configuration
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        logging.error(f"Batch config file not found: {config_path}")
        return
    except yaml.YAMLError as e:
        logging.error(f"Error parsing YAML file: {e}")
        return

    matches = config.get("matches", [])
    if not matches:
        logging.error("No matches found in the batch configuration.")
        return

    # Initialize progress manager
    progress_manager = BatchProgressManager(config_path)
    completed_matches = progress_manager.load_progress()
    failed_matches = progress_manager.load_failed()
    
    logging.info(f"📊 Batch status: {len(completed_matches)} completed, {len(failed_matches)} failed, {len(matches)} total")
    
    # Process matches
    successful_count = 0
    failed_count = 0
    skipped_count = 0
    
    for i, match in enumerate(matches, 1):
        league = match.get("league")
        home_team = match.get("home_team", match.get("home"))
        away_team = match.get("away_team", match.get("away"))

        if not all([league, home_team, away_team]):
            logging.error(f"❌ Skipping invalid match entry: {match}")
            continue

        match_id = progress_manager.get_match_id(match)
        
        # Check if already completed
        if match_id in completed_matches:
            logging.info(f"⏭️ Skipping completed match {i}/{len(matches)}: {match_id}")
            skipped_count += 1
            continue
        
        # Check if previously failed (but allow retry)
        if match_id in failed_matches:
            logging.info(f"🔄 Retrying previously failed match {i}/{len(matches)}: {match_id}")
        
        logging.info(f"🎯 Processing match {i}/{len(matches)}: {league} - {home_team} vs {away_team}")
        
        try:
            if run_enhanced_workflow(league, home_team, away_team, force):
                progress_manager.mark_completed(match_id)
                successful_count += 1
                logging.info(f"✅ Completed {match_id}")
            else:
                error_msg = "Workflow execution failed"
                progress_manager.mark_failed(match_id, error_msg)
                failed_count += 1
                logging.error(f"❌ Failed {match_id}: {error_msg}")
                
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            progress_manager.mark_failed(match_id, error_msg)
            failed_count += 1
            logging.error(f"❌ Failed {match_id}: {error_msg}")
            continue
    
    # Final summary
    total_processed = successful_count + failed_count
    logging.info(f"\n📊 BATCH SUMMARY:")
    logging.info(f"   ✅ Successful: {successful_count}")
    logging.info(f"   ❌ Failed: {failed_count}")
    logging.info(f"   ⏭️ Skipped: {skipped_count}")
    logging.info(f"   📈 Success rate: {successful_count/total_processed*100:.1f}%" if total_processed > 0 else "   📈 Success rate: N/A")
    
    # Cleanup progress files if all successful
    if failed_count == 0 and total_processed > 0:
        progress_manager.cleanup_progress_files()
        logging.info("🧹 Cleaned up progress files (all matches successful)")

def run_sequential_workflow(league: str, home_team: str, away_team: str, force: bool) -> bool:
    """Execute the original sequential workflow (for comparison/fallback)."""
    logging.info(f"🐌 Starting SEQUENTIAL workflow for {league}: {home_team} vs {away_team}")

    if not extract_urls(league, force):
        return False
    if not map_team_names(league):
        return False
    if not overwrite_config(league):
        return False
    if not split_config(league):
        return False
    if not format_dictionaries():
        return False
    if not remove_match_ids():
        return False
    if not scrape_data(league):
        return False
    if not scrape_h2h_data(league):
        return False
    if not scrape_recent_results(league):
        return False
    if not calculate_stats(league):
        return False
    if not generate_prediction(league, home_team, away_team):
        return False

    logging.info(f"✅ Sequential workflow completed successfully for {league}: {home_team} vs {away_team}")
    return True

def validate_league_exists(league: str) -> bool:
    """Validate that a league exists in the system."""
    # Check if league config exists
    config_file = f"src/config/json_database/{league}_config.json"
    if os.path.exists(config_file):
        return True
    
    # Check if league data directory exists
    data_dir = f"data/raw/{league}"
    if os.path.exists(data_dir):
        return True
    
    logging.error(f"League '{league}' not found. Check league name or run data extraction first.")
    return False

def list_available_leagues() -> List[str]:
    """List all available leagues in the system."""
    leagues = set()
    
    # Check config files
    config_dir = "src/config/json_database"
    if os.path.exists(config_dir):
        for file in os.listdir(config_dir):
            if file.endswith("_config.json"):
                league = file.replace("_config.json", "")
                leagues.add(league)
    
    # Check data directories
    data_dir = "data/raw"
    if os.path.exists(data_dir):
        for subdir in os.listdir(data_dir):
            if os.path.isdir(os.path.join(data_dir, subdir)) and not subdir.startswith('.'):
                leagues.add(subdir)
    
    return sorted(list(leagues))

class AutoCompleter:
    """Provides autocomplete functionality for interactive mode."""
    
    def __init__(self):
        self.leagues = []
        self.teams = {}
        self.load_leagues()
    
    def load_leagues(self):
        """Load available leagues for autocomplete."""
        self.leagues = list_available_leagues()
    
    def load_teams(self, league: str):
        """Load teams for a specific league."""
        if league not in self.teams:
            resolver = TeamNameResolver(league)
            teams = resolver.load_team_data()
            self.teams[league] = teams or []
    
    def complete_league(self, text: str) -> List[str]:
        """Complete league names."""
        text_lower = text.lower()
        matches = [league for league in self.leagues if league.lower().startswith(text_lower)]
        return matches
    
    def complete_team(self, league: str, text: str) -> List[str]:
        """Complete team names for a specific league."""
        self.load_teams(league)
        teams = self.teams.get(league, [])
        text_lower = text.lower()
        matches = [team for team in teams if team.lower().startswith(text_lower)]
        return matches

def setup_readline_completion(completer: AutoCompleter):
    """Setup readline completion if available."""
    if not READLINE_AVAILABLE:
        return
    
    def complete_function(text, state):
        """Readline completion function."""
        try:
            line = readline.get_line_buffer()
            words = line.split()
            
            if not words or (len(words) == 1 and not line.endswith(' ')):
                # Completing first word (league)
                matches = completer.complete_league(text)
            else:
                # Completing team names - need to determine league
                league = words[0] if words else ""
                matches = completer.complete_team(league, text)
            
            if state < len(matches):
                return matches[state]
            return None
        except Exception:
            return None
    
    readline.set_completer(complete_function)
    readline.parse_and_bind("tab: complete")
    readline.set_completer_delims(' \t\n')

def interactive_prediction_mode():
    """Enhanced interactive mode with autocomplete."""
    print("\n🎮 INTERACTIVE PREDICTION MODE")
    print("=" * 50)
    print("💡 Tips:")
    if READLINE_AVAILABLE:
        print("   • Use TAB for autocomplete")
        print("   • Use arrow keys for history")
    print("   • Type 'quit' or 'exit' to quit")
    print("   • Press Ctrl+C to cancel")
    
    completer = AutoCompleter()
    setup_readline_completion(completer)
    
    try:
        while True:
            print("\n" + "-" * 30)
            
            # Get league with autocomplete
            while True:
                try:
                    if READLINE_AVAILABLE:
                        league = input("🏆 League (TAB for autocomplete): ").strip()
                    else:
                        league = input("🏆 League: ").strip()
                    
                    if league.lower() in ['quit', 'exit', 'q']:
                        return
                    
                    if not league:
                        print("❌ Please enter a league name")
                        continue
                    
                    # Validate league
                    if not validate_league_exists(league):
                        # Try fuzzy matching
                        matches = completer.complete_league(league)
                        if matches:
                            print(f"❓ Did you mean one of these?")
                            for i, match in enumerate(matches[:5], 1):
                                print(f"   {i}. {match}")
                            choice = input("Enter number (1-5) or type new league: ").strip()
                            if choice.isdigit() and 1 <= int(choice) <= len(matches):
                                league = matches[int(choice) - 1]
                                break
                        continue
                    break
                    
                except KeyboardInterrupt:
                    print("\n👋 Goodbye!")
                    return
            
            print(f"✅ Selected league: {league}")
            
            # Load teams for autocomplete
            completer.load_teams(league)
            
            # Get home team with autocomplete
            while True:
                try:
                    if READLINE_AVAILABLE:
                        home_team = input("🏠 Home team (TAB for autocomplete): ").strip()
                    else:
                        home_team = input("🏠 Home team: ").strip()
                    
                    if home_team.lower() in ['quit', 'exit', 'q']:
                        return
                    
                    if not home_team:
                        print("❌ Please enter a home team name")
                        continue
                    
                    break
                    
                except KeyboardInterrupt:
                    print("\n👋 Goodbye!")
                    return
            
            # Get away team with autocomplete
            while True:
                try:
                    if READLINE_AVAILABLE:
                        away_team = input("✈️ Away team (TAB for autocomplete): ").strip()
                    else:
                        away_team = input("✈️ Away team: ").strip()
                    
                    if away_team.lower() in ['quit', 'exit', 'q']:
                        return
                    
                    if not away_team:
                        print("❌ Please enter an away team name")
                        continue
                    
                    if away_team.lower() == home_team.lower():
                        print("❌ Home and away teams cannot be the same")
                        continue
                    
                    break
                    
                except KeyboardInterrupt:
                    print("\n👋 Goodbye!")
                    return
            
            # Validate teams
            print(f"\n🔍 Validating teams...")
            resolver = TeamNameResolver(league)
            resolved_home, resolved_away, success, warnings = resolver.validate_and_resolve_teams(home_team, away_team)
            
            if warnings:
                print("⚠️ Warnings:")
                for warning in warnings:
                    print(f"   - {warning}")
            
            if not success:
                print("❌ Team validation failed. Please try again.")
                continue
            
            # Confirm prediction
            print(f"\n📋 Prediction Summary:")
            print(f"   League: {league}")
            print(f"   Home: {resolved_home}")
            print(f"   Away: {resolved_away}")
            
            confirm = input("\n🚀 Run prediction? (y/n): ").strip().lower()
            if confirm in ['y', 'yes']:
                print(f"\n🎯 Running prediction...")
                success = run_enhanced_workflow(league, resolved_home, resolved_away, False)
                if success:
                    print("✅ Prediction completed successfully!")
                else:
                    print("❌ Prediction failed. Check logs for details.")
            
            # Ask for another prediction
            another = input("\n➕ Run another prediction? (y/n): ").strip().lower()
            if another not in ['y', 'yes']:
                break
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")

def main():
    """Main function with enhanced argument parsing and execution."""
    parser = argparse.ArgumentParser(
        description="Enhanced orchestrator script with proper sequential processing and performance improvements.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Enhanced sequential workflow with performance improvements (default)
  python3 scripts/predict_match_enhanced.py --league ENGLAND_PREMIER_LEAGUE --home-team "Manchester United" --away-team "Arsenal"

  # Interactive mode with autocomplete
  python3 scripts/predict_match_enhanced.py --interactive

  # Force refresh all data
  python3 scripts/predict_match_enhanced.py --league ENGLAND_PREMIER_LEAGUE --home-team "Liverpool" --away-team "Arsenal" --force

  # Original sequential workflow (basic implementation)
  python3 scripts/predict_match_enhanced.py --league ENGLAND_PREMIER_LEAGUE --home-team "Chelsea" --away-team "Arsenal" --sequential

  # Enhanced batch processing with resume capability
  python3 scripts/predict_match_enhanced.py --batch-config batch_configs/predictions.yaml

  # Compare enhanced vs original sequential performance
  python3 scripts/predict_match_enhanced.py --league ENGLAND_PREMIER_LEAGUE --home-team "Liverpool" --away-team "Arsenal" --benchmark

  # List available leagues
  python3 scripts/predict_match_enhanced.py --list-leagues

  # Validate team names for a league
  python3 scripts/predict_match_enhanced.py --validate-teams ENGLAND_PREMIER_LEAGUE "Manchester United" "Arsenal"
"""
    )
    
    # Main arguments
    parser.add_argument("--league", help="The name of the league (e.g., ENGLAND_PREMIER_LEAGUE).")
    parser.add_argument("--home-team", help="The name of the home team.")
    parser.add_argument("--away-team", help="The name of the away team.")
    parser.add_argument("--batch-config", help="The path to a YAML file for batch predictions.")
    
    # Workflow options
    parser.add_argument("--force", action="store_true", help="Force re-extraction and re-processing of all data.")
    parser.add_argument("--sequential", action="store_true", help="Use original basic sequential processing instead of enhanced sequential.")
    parser.add_argument("--no-parallel-scraping", action="store_true", help="Disable parallel scraping and revert to sequential.")
    parser.add_argument("--benchmark", action="store_true", help="Run both enhanced and sequential workflows for performance comparison.")
    parser.add_argument("--interactive", "-i", action="store_true", help="Interactive mode with autocomplete support.")
    
    # Utility options
    parser.add_argument("--list-leagues", action="store_true", help="List all available leagues.")
    parser.add_argument("--validate-teams", nargs=3, metavar=('LEAGUE', 'HOME_TEAM', 'AWAY_TEAM'), 
                       help="Validate and resolve team names for a league.")
    parser.add_argument("--clear-cache", help="Clear cache for a specific league.")
    
    args = parser.parse_args()

    # Handle utility commands
    if args.list_leagues:
        leagues = list_available_leagues()
        print("\n📊 Available Leagues:")
        for i, league in enumerate(leagues, 1):
            print(f"  {i:2d}. {league}")
        print(f"\nTotal: {len(leagues)} leagues")
        return

    if args.validate_teams:
        league, home_team, away_team = args.validate_teams
        print(f"\n🔍 Validating teams for {league}:")
        print(f"   Home: {home_team}")
        print(f"   Away: {away_team}")
        
        resolver = TeamNameResolver(league)
        resolved_home, resolved_away, success, warnings = resolver.validate_and_resolve_teams(home_team, away_team)
        
        print(f"\n📝 Results:")
        print(f"   Resolved Home: {resolved_home}")
        print(f"   Resolved Away: {resolved_away}")
        print(f"   Success: {'✅' if success else '❌'}")
        
        if warnings:
            print(f"\n⚠️ Warnings:")
            for warning in warnings:
                print(f"   - {warning}")
        return

    if args.clear_cache:
        cache_dir = Path(f"data/cache/{args.clear_cache}")
        if cache_dir.exists():
            import shutil
            shutil.rmtree(cache_dir)
            print(f"✅ Cleared cache for {args.clear_cache}")
        else:
            print(f"❌ No cache found for {args.clear_cache}")
        return

    # Main workflow execution
    if args.interactive:
        interactive_prediction_mode()
    elif args.batch_config:
        run_enhanced_batch_workflow(args.batch_config, args.force)
    elif args.league and args.home_team and args.away_team:
        # Validate league exists
        if not validate_league_exists(args.league):
            return
        
        if args.benchmark:
            # Run both versions for comparison
            logging.info("🏁 BENCHMARK MODE: Running both enhanced sequential and original sequential workflows")
            
            # Sequential version
            logging.info("\n" + "="*60)
            logging.info("🐌 SEQUENTIAL WORKFLOW")
            logging.info("="*60)
            start_time = time.time()
            sequential_success = run_sequential_workflow(args.league, args.home_team, args.away_team, args.force)
            sequential_time = time.time() - start_time
            
            # Enhanced version
            logging.info("\n" + "="*60)
            logging.info("🚀 ENHANCED SEQUENTIAL WORKFLOW")
            logging.info("="*60)
            start_time = time.time()
            enhanced_success = run_enhanced_workflow(args.league, args.home_team, args.away_team, args.force)
            enhanced_time = time.time() - start_time
            
            # Results
            logging.info("\n" + "="*60)
            logging.info("📊 BENCHMARK RESULTS")
            logging.info("="*60)
            logging.info(f"Sequential: {sequential_time:.1f}s ({'✅' if sequential_success else '❌'})")
            logging.info(f"Enhanced:   {enhanced_time:.1f}s ({'✅' if enhanced_success else '❌'})")
            if sequential_time > 0 and enhanced_time > 0:
                speedup = sequential_time / enhanced_time
                time_saved = sequential_time - enhanced_time
                logging.info(f"Speedup:    {speedup:.1f}x")
                logging.info(f"Time saved: {time_saved:.1f}s ({time_saved/sequential_time*100:.1f}%)")
                
        elif args.sequential:
            run_sequential_workflow(args.league, args.home_team, args.away_team, args.force)
        else:
            run_enhanced_workflow(args.league, args.home_team, args.away_team, args.force, not args.no_parallel_scraping)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
