import pandas as pd
import yaml

def create_prediction_config():
    """
    Creates a predictions.yaml file from the predictable_matches.csv file.
    """
    matches_dict = {}
    with open('data/today_matches/predictable_matches.csv', 'r') as f:
        header = f.readline().strip().split(',')
        try:
            league_name_index = header.index('league_name')
            home_team_index = header.index('home_team')
            away_team_index = header.index('away_team')
        except ValueError as e:
            print(f"Error: Missing required column in CSV header - {e}")
            return

        for line in f:
            values = line.strip().split(',')
            if len(values) > max(league_name_index, home_team_index, away_team_index):
                league = values[league_name_index]
                home_team = values[home_team_index]
                away_team = values[away_team_index]

                if league not in matches_dict:
                    matches_dict[league] = {'fixtures': []}
                matches_dict[league]['fixtures'].append({
                    'home': home_team,
                    'away': away_team
                })

    # Restructure for YAML
    final_config = {'matches': []}
    for league, data in matches_dict.items():
        final_config['matches'].append({
            'league': league,
            'fixtures': data['fixtures']
        })

    with open('predictions.yaml', 'w') as f:
        yaml.dump(final_config, f, default_flow_style=False, sort_keys=False)

    print("Successfully created predictions.yaml")

if __name__ == '__main__':
    create_prediction_config()