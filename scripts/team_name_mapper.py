import json
import os
import argparse
from fuzzywuzzy import fuzz


def load_json(file_path):
    with open(file_path, "r") as f:
        return json.load(f)


def save_json(data, file_path):
    with open(file_path, "w") as f:
        json.dump(data, f, indent=2)


def create_team_name_mapping(soccerstats_teams, footystats_teams):
    mapping = {}
    if not footystats_teams:
        print("Warning: footystats_teams is empty")
    for soccerstats_name in soccerstats_teams:
        best_match = max(
            footystats_teams,
            key=lambda x: fuzz.ratio(soccerstats_name.lower(), x.lower()),
        )
        mapping[soccerstats_name] = best_match
    return mapping


def process_league_file(file_path):
    data = load_json(file_path)
    soccerstats_teams = list(data.get("TEAM_URLS", {}).keys())

    # Extract team names from HEAD_TO_HEAD_URLS
    footystats_teams = set()
    head_to_head_urls = data.get("HEAD_TO_HEAD_URLS", {})
    if not head_to_head_urls:
        print(f"Warning: HEAD_TO_HEAD_URLS is empty in {file_path}")
    for match in data.get("HEAD_TO_HEAD_URLS", {}).keys():
        teams = match.split(" vs ")
        footystats_teams.update(teams)

    if not footystats_teams:
        print(f"Warning: No teams extracted from HEAD_TO_HEAD_URLS in {file_path}")
        return

    mapping = create_team_name_mapping(soccerstats_teams, list(footystats_teams))
    data["TEAM_NAME_MAPPING"] = mapping
    save_json(data, file_path)
    print(f"Updated {file_path} with team name mapping")


def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Create team name mappings for betting leagues")
    parser.add_argument("--league", type=str, help="Process a specific league only")
    parser.add_argument("--leagues", type=str, help="Process specific leagues (comma-separated)")
    args = parser.parse_args()

    json_directory = os.path.join("src", "config", "json_database")

    # Get target leagues
    target_leagues = None
    if args.league:
        target_leagues = [args.league]
    elif args.leagues:
        target_leagues = [league.strip() for league in args.leagues.split(",")]

    processed_count = 0
    for filename in os.listdir(json_directory):
        if filename.endswith("_config.json"):
            # Extract league name from filename
            league_name = filename.replace("_config.json", "")

            # Skip if not in target leagues (when specified)
            if target_leagues and league_name not in target_leagues:
                continue

            file_path = os.path.join(json_directory, filename)
            process_league_file(file_path)
            processed_count += 1

    if target_leagues:
        print(f"Processed {processed_count} league(s): {target_leagues}")
    else:
        print(f"Processed {processed_count} league(s) (all leagues)")


if __name__ == "__main__":
    main()
