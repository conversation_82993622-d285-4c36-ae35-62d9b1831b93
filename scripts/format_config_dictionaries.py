#!/usr/bin/env python3
"""
Script to format dictionaries in Python config files.
This script makes dictionaries in league config files more readable by
properly formatting them with indentation and line breaks.
"""

import ast
import re
from pathlib import Path

# Directory containing the league config Python files
LEAGUE_CONFIGS_DIR = "src/scrapers/league_configs"

def format_file(file_path):
    """Format dictionaries in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Parse the file to get the AST
        tree = ast.parse(content)

        # Find all dictionary assignments in the file
        dict_assignments = {}

        # Walk through the AST to find dictionary assignments
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Subscript) and isinstance(target.value, ast.Name):
                        if target.value.id == "LEAGUE_CONFIGS" and isinstance(node.value, ast.Dict):
                            # This is a dictionary assignment to LEAGUE_CONFIGS[...]
                            for _, value in zip(node.value.keys, node.value.values):
                                if isinstance(value, ast.Dict):
                                    # This is a nested dictionary
                                    for k, v in zip(value.keys, value.values):
                                        if isinstance(k, ast.Constant) and k.value in ["TEAM_URLS", "HEAD_TO_HEAD_URLS", "TEAM_NAME_MAPPING"]:
                                            # Found a target dictionary
                                            dict_assignments[k.value] = (k.value, v)

        # If we found dictionary assignments, format them
        if not dict_assignments:
            return False

        # Use regex to find and replace the dictionaries in the content
        modified_content = content

        # Format each dictionary
        for _, (dict_key, _) in dict_assignments.items():
            # Find the dictionary in the content
            pattern = rf"['\"]({dict_key})['\"]:\s*(\{{[^}}]*\}})"

            # Function to replace the dictionary with a formatted version
            def replace_dict(match):
                key = match.group(1)
                dict_str = match.group(2)

                try:
                    # Parse the dictionary
                    dict_obj = ast.literal_eval(dict_str)

                    # Format the dictionary with pprint
                    formatted = "{\n"
                    for k, v in dict_obj.items():
                        formatted += f"            '{k}': '{v}',\n"
                    formatted += "        }"

                    return f"'{key}': {formatted}"
                except (SyntaxError, ValueError) as e:
                    print(f"Error parsing dictionary {key} in {file_path}: {e}")
                    return match.group(0)

            # Replace the dictionary in the content
            modified_content = re.sub(pattern, replace_dict, modified_content, flags=re.DOTALL)

        # Write the modified content back to the file
        if modified_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            return True

        return False

    except Exception as e:
        print(f"Error processing file {file_path}: {e}")
        return False

def format_file_simple(file_path):
    """Format dictionaries in a Python file using a simpler approach."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Define patterns to match the dictionaries
        patterns = [
            r'"TEAM_URLS":\s*({[^}]*})',
            r'"HEAD_TO_HEAD_URLS":\s*({[^}]*})',
            r'"TEAM_NAME_MAPPING":\s*({[^}]*})'
        ]

        modified_content = content

        # Process each pattern
        for pattern in patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for dict_str in matches:
                try:
                    # Parse the dictionary
                    dict_obj = ast.literal_eval(dict_str)

                    # Format the dictionary
                    formatted = "{\n"
                    for k, v in dict_obj.items():
                        formatted += f"            '{k}': '{v}',\n"
                    formatted += "        }"

                    # Replace in the content
                    modified_content = modified_content.replace(dict_str, formatted)
                except (SyntaxError, ValueError) as e:
                    print(f"Error parsing dictionary in {file_path}: {e}")

        # Write the modified content back to the file
        if modified_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            return True

        return False

    except Exception as e:
        print(f"Error processing file {file_path}: {e}")
        return False

def format_all_files():
    """Format dictionaries in all Python files in the league configs directory."""
    config_dir = Path(LEAGUE_CONFIGS_DIR)

    if not config_dir.exists():
        print(f"Directory not found: {LEAGUE_CONFIGS_DIR}")
        return

    # Get all Python files in the directory
    python_files = list(config_dir.glob("*.py"))
    total_files = len(python_files)

    print(f"Found {total_files} Python files in {LEAGUE_CONFIGS_DIR}")

    # Format each file
    formatted_count = 0
    for i, file_path in enumerate(python_files, 1):
        print(f"Processing file {i}/{total_files}: {file_path.name}...", end="\r")

        if format_file_simple(file_path):
            formatted_count += 1

    print(f"\nFormatted dictionaries in {formatted_count} out of {total_files} files.")

if __name__ == "__main__":
    format_all_files()
