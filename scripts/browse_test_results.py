#!/usr/bin/env python3
"""
Test Results Browser
===================

Helper script to browse and analyze league testing results.

Usage:
    python browse_test_results.py                    # Show latest test summary
    python browse_test_results.py --failed           # Show only failed leagues
    python browse_test_results.py --successful       # Show only successful leagues
    python browse_test_results.py --league EPL       # Show specific league details
    python browse_test_results.py --issues           # Analyze common issues
"""

import os
import json
import argparse
from datetime import datetime
from typing import Dict, List, Optional
import glob

def find_latest_test_run() -> Optional[str]:
    """Find the most recent test run directory."""
    test_dirs = glob.glob("logs/league_testing_*")
    if not test_dirs:
        return None
    return max(test_dirs, key=os.path.getctime)

def load_summary(test_dir: str) -> Dict:
    """Load the summary file from a test directory."""
    summary_file = os.path.join(test_dir, "summary.json")
    if os.path.exists(summary_file):
        with open(summary_file, 'r') as f:
            return json.load(f)
    return {}

def load_league_result(test_dir: str, league_name: str) -> Dict:
    """Load detailed results for a specific league."""
    result_file = os.path.join(test_dir, league_name, "result.json")
    if os.path.exists(result_file):
        with open(result_file, 'r') as f:
            return json.load(f)
    return {}

def show_summary(test_dir: str):
    """Show overall test summary."""
    summary = load_summary(test_dir)
    if not summary:
        print("❌ No summary found")
        return
    
    print("🏆 LEAGUE TESTING SUMMARY")
    print("=" * 50)
    print(f"📁 Test Directory: {test_dir}")
    print(f"🕒 Last Updated: {summary.get('timestamp', 'Unknown')}")
    print(f"🎯 Mode: {summary.get('mode', 'Unknown').upper()}")
    print(f"📊 Total Tested: {summary.get('total_tested', 0)}")
    print(f"✅ Successful: {summary.get('successful', 0)}")
    print(f"❌ Failed: {summary.get('failed', 0)}")
    
    if summary.get('total_tested', 0) > 0:
        success_rate = (summary.get('successful', 0) / summary.get('total_tested', 1)) * 100
        print(f"📈 Success Rate: {success_rate:.1f}%")

def show_failed_leagues(test_dir: str):
    """Show details of failed leagues."""
    summary = load_summary(test_dir)
    failed_leagues = summary.get('failed_leagues', [])
    
    if not failed_leagues:
        print("🎉 No failed leagues!")
        return
    
    print(f"❌ FAILED LEAGUES ({len(failed_leagues)})")
    print("=" * 50)
    
    issue_counts = {}
    
    for league in failed_leagues:
        result = load_league_result(test_dir, league)
        issues = result.get('issues', [])
        
        print(f"\n🔴 {league}")
        if issues:
            for issue in issues:
                print(f"   • {issue}")
                # Count issue types
                issue_type = issue.split(':')[0] if ':' in issue else issue
                issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1
        else:
            print("   • No specific issues recorded")
    
    # Show issue analysis
    if issue_counts:
        print(f"\n📊 ISSUE ANALYSIS:")
        print("-" * 30)
        for issue_type, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"   {issue_type}: {count} leagues")

def show_successful_leagues(test_dir: str):
    """Show successful leagues."""
    summary = load_summary(test_dir)
    successful_leagues = summary.get('successful_leagues', [])
    
    if not successful_leagues:
        print("😞 No successful leagues yet!")
        return
    
    print(f"✅ SUCCESSFUL LEAGUES ({len(successful_leagues)})")
    print("=" * 50)
    
    for i, league in enumerate(successful_leagues, 1):
        print(f"{i:3d}. {league}")

def show_league_details(test_dir: str, league_name: str):
    """Show detailed results for a specific league."""
    result = load_league_result(test_dir, league_name)
    
    if not result:
        print(f"❌ No results found for league: {league_name}")
        return
    
    print(f"🔍 DETAILED RESULTS: {league_name}")
    print("=" * 60)
    print(f"Overall Status: {result.get('overall_status', 'Unknown').upper()}")
    
    # Data Loading
    data_loading = result.get('data_loading', {})
    print(f"\n📊 Data Loading: {data_loading.get('status', 'Unknown').upper()}")
    if data_loading.get('details'):
        details = data_loading['details']
        print(f"   • Results: {details.get('results_shape', 'Unknown')}")
        print(f"   • Team Stats: {details.get('team_stats_shape', 'Unknown')}")
        print(f"   • League Stats: {details.get('league_stats_shape', 'Unknown')}")
        print(f"   • H2H Stats: {details.get('h2h_stats_shape', 'Unknown')}")
        print(f"   • League Table: {details.get('league_table_shape', 'Unknown')}")
    
    # Feature Preparation
    feature_prep = result.get('feature_preparation', {})
    print(f"\n🔧 Feature Preparation: {feature_prep.get('status', 'Unknown').upper()}")
    if feature_prep.get('details'):
        details = feature_prep['details']
        print(f"   • Prepared Shape: {details.get('prepared_shape', 'Unknown')}")
        print(f"   • Feature Count: {details.get('feature_count', 'Unknown')}")
        print(f"   • NaN Columns: {details.get('nan_columns_count', 'Unknown')}")
    
    # Issues and Warnings
    issues = result.get('issues', [])
    if issues:
        print(f"\n❌ Issues:")
        for issue in issues:
            print(f"   • {issue}")
    
    warnings = result.get('warnings', [])
    if warnings:
        print(f"\n⚠️  Warnings:")
        for warning in warnings:
            print(f"   • {warning}")

def analyze_common_issues(test_dir: str):
    """Analyze common issues across all leagues."""
    summary = load_summary(test_dir)
    all_leagues = summary.get('successful_leagues', []) + summary.get('failed_leagues', [])
    
    issue_patterns = {}
    warning_patterns = {}
    
    print("🔍 COMMON ISSUES ANALYSIS")
    print("=" * 50)
    
    for league in all_leagues:
        result = load_league_result(test_dir, league)
        
        # Analyze issues
        for issue in result.get('issues', []):
            issue_type = issue.split(':')[0] if ':' in issue else issue
            if issue_type not in issue_patterns:
                issue_patterns[issue_type] = []
            issue_patterns[issue_type].append(league)
        
        # Analyze warnings
        for warning in result.get('warnings', []):
            warning_type = warning.split(':')[0] if ':' in warning else warning
            if warning_type not in warning_patterns:
                warning_patterns[warning_type] = []
            warning_patterns[warning_type].append(league)
    
    if issue_patterns:
        print("\n❌ ISSUE PATTERNS:")
        for issue_type, leagues in sorted(issue_patterns.items(), key=lambda x: len(x[1]), reverse=True):
            print(f"\n   {issue_type} ({len(leagues)} leagues):")
            for league in leagues[:5]:  # Show first 5
                print(f"      • {league}")
            if len(leagues) > 5:
                print(f"      ... and {len(leagues) - 5} more")
    
    if warning_patterns:
        print("\n⚠️  WARNING PATTERNS:")
        for warning_type, leagues in sorted(warning_patterns.items(), key=lambda x: len(x[1]), reverse=True):
            print(f"\n   {warning_type} ({len(leagues)} leagues):")
            for league in leagues[:3]:  # Show first 3
                print(f"      • {league}")
            if len(leagues) > 3:
                print(f"      ... and {len(leagues) - 3} more")

def main():
    parser = argparse.ArgumentParser(description="Browse league testing results")
    parser.add_argument('--test-dir', type=str, help='Specific test directory to analyze')
    parser.add_argument('--failed', action='store_true', help='Show only failed leagues')
    parser.add_argument('--successful', action='store_true', help='Show only successful leagues')
    parser.add_argument('--league', type=str, help='Show details for specific league')
    parser.add_argument('--issues', action='store_true', help='Analyze common issues')
    
    args = parser.parse_args()
    
    # Find test directory
    if args.test_dir:
        test_dir = args.test_dir
    else:
        test_dir = find_latest_test_run()
    
    if not test_dir or not os.path.exists(test_dir):
        print("❌ No test results found. Run the testing suite first.")
        return
    
    # Execute requested action
    if args.failed:
        show_failed_leagues(test_dir)
    elif args.successful:
        show_successful_leagues(test_dir)
    elif args.league:
        show_league_details(test_dir, args.league)
    elif args.issues:
        analyze_common_issues(test_dir)
    else:
        show_summary(test_dir)

if __name__ == "__main__":
    main()