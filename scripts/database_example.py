#!/usr/bin/env python3
"""
Football Database Usage Examples

This script demonstrates how to use the new SQLite database system
to access football data efficiently.
"""

import sys
import os
sys.path.append('src')

from database.football_db import FootballDatabase, get_database, get_available_leagues, QueryBuilder
import pandas as pd

def main():
    print("🏈 Football Database Usage Examples")
    print("=" * 50)
    
    # Check if database exists
    db_path = 'data/football_betting.db'
    if not os.path.exists(db_path):
        print("❌ Database not found!")
        print("Please run: python create_database.py")
        return
    
    # Example 1: Basic database connection and summary
    print("\n1. Database Summary")
    print("-" * 20)
    
    with get_database() as db:
        summary = db.get_database_summary()
        print(f"📊 Database contains:")
        print(f"   • {summary['leagues']:,} leagues")
        print(f"   • {summary['teams']:,} teams")
        print(f"   • {summary['team_stats']:,} team stat records")
        print(f"   • {summary['match_results']:,} match results")
        print(f"   • {summary['head_to_head_stats']:,} H2H records")
        print(f"   • Database size: {summary['database_size_mb']:.1f} MB")
        
        if 'most_teams_league' in summary:
            print(f"   • Largest league: {summary['most_teams_league']} ({summary['most_teams_count']} teams)")
    
    # Example 2: Get available leagues
    print("\n2. Available Leagues")
    print("-" * 20)
    
    leagues = get_available_leagues()
    print(f"📋 Found {len(leagues)} leagues:")
    
    # Show first 10 leagues
    for i, league in enumerate(leagues[:10], 1):
        print(f"   {i:2d}. {league}")
    
    if len(leagues) > 10:
        print(f"   ... and {len(leagues) - 10} more leagues")
    
    # Example 3: League-specific analysis
    print("\n3. League Analysis - Premier League")
    print("-" * 35)
    
    target_league = "ENGLAND_PREMIER_LEAGUE"
    if target_league in leagues:
        with get_database() as db:
            # Get league table
            table = db.get_league_table(target_league)
            if not table.empty:
                print("🏆 League Table (Top 5):")
                print(table.head()[['position', 'team_name', 'MP', 'W', 'D', 'L', 'Pts']].to_string(index=False))
            
            # Get league statistics
            league_stats = db.get_league_stats(target_league)
            if not league_stats.empty:
                print(f"\n📈 League Statistics:")
                key_stats = ['avg_goals_per_match', 'home_win_percentage', 'away_win_percentage', 'both_teams_scored_percentage']
                for stat in key_stats:
                    stat_row = league_stats[league_stats['stat_name'] == stat]
                    if not stat_row.empty:
                        print(f"   • {stat}: {stat_row.iloc[0]['stat_value']}")
            
            # Get top scorers
            top_scorers = db.get_league_top_scorers(target_league, 5)
            if not top_scorers.empty:
                print(f"\n⚽ Top Scoring Teams:")
                print(top_scorers[['team_name', 'goals_scored_all', 'goals_scored_per_match_all']].to_string(index=False))
    
    # Example 4: Team analysis
    print("\n4. Team Analysis - Arsenal")
    print("-" * 25)
    
    target_team = "Arsenal"
    with get_database() as db:
        # Get team stats
        team_stats = db.get_team_stats(target_team, target_league)
        if not team_stats.empty:
            stats = team_stats.iloc[0]
            print(f"📊 {target_team} Statistics:")
            
            # Handle None values safely
            total_played = stats['total_played'] if pd.notna(stats['total_played']) else 0
            ppg = stats['points_per_game'] if pd.notna(stats['points_per_game']) else 0
            goals_scored = stats['goals_scored_all'] if pd.notna(stats['goals_scored_all']) else 0
            goals_per_match = stats['goals_scored_per_match_all'] if pd.notna(stats['goals_scored_per_match_all']) else 0
            goals_conceded = stats['goals_conceded_all'] if pd.notna(stats['goals_conceded_all']) else 0
            goals_conceded_per_match = stats['goals_conceded_per_match_all'] if pd.notna(stats['goals_conceded_per_match_all']) else 0
            
            print(f"   • Matches played: {total_played}")
            print(f"   • Points per game: {ppg:.2f}")
            print(f"   • Goals scored: {goals_scored} ({goals_per_match:.2f} per match)")
            print(f"   • Goals conceded: {goals_conceded} ({goals_conceded_per_match:.2f} per match)")
        
        # Get recent form
        recent_form = db.get_team_form(target_team, target_league, 5)
        if not recent_form.empty:
            print(f"\n🔥 Recent Form (Last 5 matches):")
            for _, match in recent_form.iterrows():
                venue = match['venue']
                opponent = match['away_team'] if venue == 'Home' else match['home_team']
                score = f"{match['home_score']}-{match['away_score']}"
                result = match['result']
                print(f"   • vs {opponent} ({venue}): {score} ({result})")
    
    # Example 5: Head-to-head analysis
    print("\n5. Head-to-Head Analysis - Arsenal vs Chelsea")
    print("-" * 45)
    
    team1, team2 = "Arsenal", "Chelsea"
    with get_database() as db:
        h2h_stats = db.get_head_to_head_stats(team1, team2, target_league)
        if not h2h_stats.empty:
            h2h = h2h_stats.iloc[0]
            print(f"⚔️  {team1} vs {team2} Head-to-Head:")
            
            # Handle None values safely
            total_matches = h2h['total_matches'] if pd.notna(h2h['total_matches']) else 0
            home_win_pct = h2h['home_win_percentage'] if pd.notna(h2h['home_win_percentage']) else 0
            away_win_pct = h2h['away_win_percentage'] if pd.notna(h2h['away_win_percentage']) else 0
            draw_pct = h2h['draw_percentage'] if pd.notna(h2h['draw_percentage']) else 0
            home_wins = h2h['home_wins'] if pd.notna(h2h['home_wins']) else 0
            away_wins = h2h['away_wins'] if pd.notna(h2h['away_wins']) else 0
            draws = h2h['draws'] if pd.notna(h2h['draws']) else 0
            btts_pct = h2h['btts_percentage'] if pd.notna(h2h['btts_percentage']) else 0
            over_2_5_pct = h2h['over_2_5_percentage'] if pd.notna(h2h['over_2_5_percentage']) else 0
            
            print(f"   • Total matches: {total_matches}")
            print(f"   • {h2h['home_team']} wins: {home_win_pct:.1f}% ({home_wins} matches)")
            print(f"   • {h2h['away_team']} wins: {away_win_pct:.1f}% ({away_wins} matches)")
            print(f"   • Draws: {draw_pct:.1f}% ({draws} matches)")
            print(f"   • BTTS: {btts_pct:.1f}%")
            print(f"   • Over 2.5 goals: {over_2_5_pct:.1f}%")
    
    # Example 6: Custom queries using QueryBuilder
    print("\n6. Custom Analysis - Team Performance")
    print("-" * 35)
    
    with get_database() as db:
        query = QueryBuilder.get_team_performance_query(target_league, min_matches=10)
        performance = db.execute_query(query, (target_league, 10))
        
        if not performance.empty:
            print("🎯 Team Performance Analysis (Min 10 matches):")
            # Convert numeric columns and handle None values
            performance['points_per_game'] = pd.to_numeric(performance['points_per_game'], errors='coerce').fillna(0)
            performance['goal_difference_per_match'] = pd.to_numeric(performance['goal_difference_per_match'], errors='coerce').fillna(0)
            performance['position'] = pd.to_numeric(performance['position'], errors='coerce').fillna(999)
            
            # Show top 5 teams by points per game
            top_performers = performance.nlargest(5, 'points_per_game')
            print(top_performers[['team_name', 'points_per_game', 'goal_difference_per_match', 'position']].to_string(index=False))
    
    # Example 7: Recent matches
    print("\n7. Recent Matches")
    print("-" * 18)
    
    with get_database() as db:
        recent_matches = db.get_match_results(target_league, limit=5)
        if not recent_matches.empty:
            print("🕐 Latest Match Results:")
            for _, match in recent_matches.iterrows():
                score = f"{match['home_score']}-{match['away_score']}" if pd.notna(match['home_score']) else "TBD"
                print(f"   • {match['match_date']}: {match['home_team']} {score} {match['away_team']}")
    
    print("\n✅ Database examples completed!")
    print("\n💡 Tips:")
    print("   • Use 'with get_database() as db:' for automatic connection management")
    print("   • All methods return pandas DataFrames for easy analysis")
    print("   • Use QueryBuilder for complex custom queries")
    print("   • Check the football_db.py module for all available methods")

if __name__ == "__main__":
    main()