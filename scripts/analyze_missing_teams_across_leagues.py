#!/usr/bin/env python3
"""
Analyze the missing team stats warnings across all leagues to determine
if we need a comprehensive URL extraction refresh.
"""

import re
import os
from collections import defaultdict, Counter
import pandas as pd

def analyze_missing_teams_from_log():
    """Analyze the log file to extract all missing team stats warnings."""
    
    log_path = 'logs/league_testing_20250718_052623.log'
    
    leagues_with_warnings = defaultdict(list)
    successful_leagues = set()
    failed_leagues = set()
    current_league = None
    
    with open(log_path, 'r') as f:
        for line in f:
            # Check for league testing start
            league_match = re.search(r'🔍 Testing League \d+/\d+: (.+)', line)
            if league_match:
                current_league = league_match.group(1)
                continue
            
            # Check for missing team stats warnings
            if 'Missing team stats for match:' in line and current_league:
                match_info = re.search(r'Missing team stats for match: (.+)', line)
                if match_info:
                    leagues_with_warnings[current_league].append(match_info.group(1))
                continue
            
            # Check for success/failure status
            success_match = re.search(r'✅ (.+): SUCCESS', line)
            if success_match:
                successful_leagues.add(success_match.group(1))
                continue
                
            failure_match = re.search(r'❌ (.+): FAILED', line)
            if failure_match:
                failed_leagues.add(failure_match.group(1))
                continue
    
    return leagues_with_warnings, successful_leagues, failed_leagues

def analyze_team_name_patterns(leagues_with_warnings):
    """Analyze patterns in missing team names to identify common issues."""
    
    all_missing_matches = []
    missing_teams = Counter()
    
    for league, warnings in leagues_with_warnings.items():
        for warning in warnings:
            all_missing_matches.append((league, warning))
            # Extract team names from "Team A vs Team B" format
            if ' vs ' in warning:
                teams = warning.split(' vs ')
                for team in teams:
                    missing_teams[team.strip()] += 1
    
    return all_missing_matches, missing_teams

def check_config_file_ages():
    """Check when league configuration files were last updated."""
    
    config_dir = 'src/scrapers/league_configs'
    config_files = []
    
    if os.path.exists(config_dir):
        for filename in os.listdir(config_dir):
            if filename.endswith('.py') and filename != '__init__.py':
                filepath = os.path.join(config_dir, filename)
                stat = os.stat(filepath)
                config_files.append({
                    'league': filename.replace('.py', ''),
                    'last_modified': stat.st_mtime,
                    'size': stat.st_size
                })
    
    # Sort by last modified time
    config_files.sort(key=lambda x: x['last_modified'])
    
    return config_files

def check_url_format_in_configs():
    """Check how many leagues still use old team.asp format vs new teamstats.asp format."""
    
    config_dir = 'src/scrapers/league_configs'
    old_format_count = 0
    new_format_count = 0
    leagues_with_old_format = []
    leagues_with_new_format = []
    
    if os.path.exists(config_dir):
        for filename in os.listdir(config_dir):
            if filename.endswith('.py') and filename != '__init__.py':
                filepath = os.path.join(config_dir, filename)
                league_name = filename.replace('.py', '')
                
                try:
                    with open(filepath, 'r') as f:
                        content = f.read()
                        
                    if 'team.asp' in content:
                        old_format_count += 1
                        leagues_with_old_format.append(league_name)
                    elif 'teamstats.asp' in content:
                        new_format_count += 1
                        leagues_with_new_format.append(league_name)
                        
                except Exception as e:
                    print(f"Error reading {filepath}: {e}")
    
    return {
        'old_format_count': old_format_count,
        'new_format_count': new_format_count,
        'leagues_with_old_format': leagues_with_old_format,
        'leagues_with_new_format': leagues_with_new_format
    }

def main():
    print("🔍 ANALYZING MISSING TEAM STATS ACROSS ALL LEAGUES")
    print("=" * 80)
    
    # Analyze the log file
    leagues_with_warnings, successful_leagues, failed_leagues = analyze_missing_teams_from_log()
    
    print(f"\n📊 OVERALL STATISTICS:")
    print(f"Total successful leagues: {len(successful_leagues)}")
    print(f"Total failed leagues: {len(failed_leagues)}")
    print(f"Successful leagues with missing team warnings: {len(leagues_with_warnings)}")
    print(f"Percentage of successful leagues with warnings: {len(leagues_with_warnings)/len(successful_leagues)*100:.1f}%")
    
    # Analyze missing team patterns
    all_missing_matches, missing_teams = analyze_team_name_patterns(leagues_with_warnings)
    
    print(f"\n🚨 MISSING TEAM STATS ANALYSIS:")
    print(f"Total missing team warnings: {sum(len(warnings) for warnings in leagues_with_warnings.values())}")
    print(f"Unique missing teams: {len(missing_teams)}")
    
    print(f"\n🔝 TOP 10 MOST FREQUENTLY MISSING TEAMS:")
    for team, count in missing_teams.most_common(10):
        print(f"  {count:2d}x - {team}")
    
    # Check URL formats
    url_analysis = check_url_format_in_configs()
    
    print(f"\n🔗 URL FORMAT ANALYSIS:")
    print(f"Leagues with OLD format (team.asp): {url_analysis['old_format_count']}")
    print(f"Leagues with NEW format (teamstats.asp): {url_analysis['new_format_count']}")
    
    if url_analysis['old_format_count'] > 0:
        print(f"\n❌ LEAGUES STILL USING OLD FORMAT (need URL refresh):")
        for league in sorted(url_analysis['leagues_with_old_format'])[:20]:  # Show first 20
            print(f"  • {league}")
        if len(url_analysis['leagues_with_old_format']) > 20:
            print(f"  ... and {len(url_analysis['leagues_with_old_format']) - 20} more")
    
    # Check configuration file ages
    config_files = check_config_file_ages()
    
    print(f"\n📅 CONFIGURATION FILE AGES:")
    print("Oldest 10 configuration files:")
    for config in config_files[:10]:
        import datetime
        mod_time = datetime.datetime.fromtimestamp(config['last_modified'])
        print(f"  {mod_time.strftime('%Y-%m-%d %H:%M')} - {config['league']}")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    
    if url_analysis['old_format_count'] > 0:
        print(f"1. 🚨 URGENT: {url_analysis['old_format_count']} leagues still use old team.asp format")
        print("   → Run URL extraction refresh for these leagues immediately")
    
    if len(leagues_with_warnings) > len(successful_leagues) * 0.1:  # More than 10% have warnings
        print(f"2. 🚨 HIGH PRIORITY: {len(leagues_with_warnings)} leagues ({len(leagues_with_warnings)/len(successful_leagues)*100:.1f}%) have missing team warnings")
        print("   → This suggests widespread team composition changes (promotions/relegations)")
        print("   → Consider running comprehensive URL extraction refresh for all leagues")
    
    print(f"3. 💡 PROCESS: Follow the workflow from docs/how_to_use.md:")
    print("   → Extract URLs → Map team names → Update configs → Format → Clean → Re-scrape")
    
    print(f"\n🔧 SUGGESTED COMMANDS:")
    if url_analysis['old_format_count'] > 0:
        print("# For leagues with old format:")
        print("python3 src/url_extractor.py --batch-file batch_configs/old_format_leagues.yaml")
    
    print("# For comprehensive refresh of all leagues:")
    print("python3 src/url_extractor.py --all-leagues --force")
    
    print(f"\n📈 IMPACT ASSESSMENT:")
    total_warnings = sum(len(warnings) for warnings in leagues_with_warnings.values())
    print(f"• {total_warnings} matches currently cannot be processed due to missing team stats")
    print(f"• This affects {len(leagues_with_warnings)} leagues out of {len(successful_leagues)} successful leagues")
    print(f"• Fixing this could improve data coverage significantly")

if __name__ == "__main__":
    main()