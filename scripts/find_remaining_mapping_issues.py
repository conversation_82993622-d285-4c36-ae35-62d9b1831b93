#!/usr/bin/env python3
"""
Find Remaining Team Mapping Issues

This script identifies teams that still have incorrect canonical mappings
or are missing mappings that should exist based on pattern analysis.
"""

import sys
import sqlite3
from difflib import SequenceMatcher

sys.path.append('src')
from database.football_db import get_database

def find_incorrect_self_mappings():
    """Find teams that are mapped to themselves but should map to other teams."""
    issues = []
    
    with get_database() as db:
        # Find teams that are mapped to themselves but there's another team with stats
        query = """
            SELECT 
                t1.team_id,
                t1.team_name,
                t1.canonical_team_id,
                l.league_name,
                t2.team_name as potential_canonical,
                t2.team_id as potential_canonical_id
            FROM teams t1
            JOIN leagues l ON t1.league_id = l.league_id
            LEFT JOIN team_stats ts1 ON t1.team_id = ts1.team_id
            JOIN teams t2 ON t1.league_id = t2.league_id AND t1.team_id != t2.team_id
            LEFT JOIN team_stats ts2 ON t2.team_id = ts2.team_id
            WHERE ts1.team_id IS NULL  -- t1 has no stats
            AND ts2.team_id IS NOT NULL  -- t2 has stats
            AND t1.canonical_team_id = t1.team_id  -- t1 is mapped to itself
            AND (
                -- Similar name patterns that should be mapped
                (t1.team_name = t2.team_name || ' FC' OR t1.team_name = t2.team_name || ' F.C.')
                OR (t1.team_name = t2.team_name || ' Wanderers')
                OR (t1.team_name = t2.team_name || ' United')
                OR (t1.team_name = t2.team_name || ' City')
                OR (REPLACE(t1.team_name, ' FC', '') = t2.team_name)
                OR (REPLACE(t1.team_name, ' F.C.', '') = t2.team_name)
                OR (REPLACE(t1.team_name, ' Wanderers', '') = t2.team_name)
                OR (REPLACE(t1.team_name, ' United', '') = t2.team_name)
                OR (REPLACE(t1.team_name, ' City', '') = t2.team_name)
            )
            ORDER BY l.league_name, t1.team_name
        """
        
        results = db.execute_query(query)
        
        for _, row in results.iterrows():
            issues.append({
                'type': 'incorrect_self_mapping',
                'league': row['league_name'],
                'team_name': row['team_name'],
                'team_id': row['team_id'],
                'current_canonical_id': row['canonical_team_id'],
                'suggested_canonical_name': row['potential_canonical'],
                'suggested_canonical_id': row['potential_canonical_id']
            })
    
    return issues

def find_missing_mappings():
    """Find teams without any canonical mapping that should have one."""
    issues = []
    
    with get_database() as db:
        # Find teams with no canonical mapping but similar teams exist with stats
        query = """
            SELECT 
                t1.team_id,
                t1.team_name,
                l.league_name,
                t2.team_name as potential_canonical,
                t2.team_id as potential_canonical_id
            FROM teams t1
            JOIN leagues l ON t1.league_id = l.league_id
            LEFT JOIN team_stats ts1 ON t1.team_id = ts1.team_id
            JOIN teams t2 ON t1.league_id = t2.league_id AND t1.team_id != t2.team_id
            LEFT JOIN team_stats ts2 ON t2.team_id = ts2.team_id
            WHERE ts1.team_id IS NULL  -- t1 has no stats
            AND ts2.team_id IS NOT NULL  -- t2 has stats
            AND t1.canonical_team_id IS NULL  -- t1 has no canonical mapping
            AND (
                -- Similar name patterns
                (t1.team_name = t2.team_name || ' FC' OR t1.team_name = t2.team_name || ' F.C.')
                OR (t1.team_name = t2.team_name || ' Wanderers')
                OR (t1.team_name = t2.team_name || ' United')
                OR (t1.team_name = t2.team_name || ' City')
                OR (REPLACE(t1.team_name, ' FC', '') = t2.team_name)
                OR (REPLACE(t1.team_name, ' F.C.', '') = t2.team_name)
                OR (REPLACE(t1.team_name, ' Wanderers', '') = t2.team_name)
                OR (REPLACE(t1.team_name, ' United', '') = t2.team_name)
                OR (REPLACE(t1.team_name, ' City', '') = t2.team_name)
            )
            ORDER BY l.league_name, t1.team_name
        """
        
        results = db.execute_query(query)
        
        for _, row in results.iterrows():
            issues.append({
                'type': 'missing_mapping',
                'league': row['league_name'],
                'team_name': row['team_name'],
                'team_id': row['team_id'],
                'current_canonical_id': None,
                'suggested_canonical_name': row['potential_canonical'],
                'suggested_canonical_id': row['potential_canonical_id']
            })
    
    return issues

def apply_fixes(issues, dry_run=True):
    """Apply the suggested fixes."""
    if dry_run:
        print(f"\n=== DRY RUN: Would fix {len(issues)} issues ===")
        for issue in issues:
            print(f"League: {issue['league']}")
            print(f"  Fix: '{issue['team_name']}' -> '{issue['suggested_canonical_name']}'")
            print(f"  Type: {issue['type']}")
            print(f"  SET canonical_team_id = {issue['suggested_canonical_id']} WHERE team_id = {issue['team_id']}")
            print()
        return
    
    print(f"\n=== Applying {len(issues)} fixes ===")
    
    with get_database() as db:
        for issue in issues:
            try:
                db.conn.execute(
                    "UPDATE teams SET canonical_team_id = ? WHERE team_id = ?",
                    (issue['suggested_canonical_id'], issue['team_id'])
                )
                print(f"✅ Fixed '{issue['team_name']}' -> '{issue['suggested_canonical_name']}'")
            except Exception as e:
                print(f"❌ Error fixing '{issue['team_name']}': {e}")
        
        db.conn.commit()
        print(f"\n✅ Successfully applied {len(issues)} fixes!")

def main():
    """Main function."""
    print("🔍 Finding remaining team mapping issues...")
    
    print("\n1. Checking for incorrect self-mappings...")
    self_mapping_issues = find_incorrect_self_mappings()
    print(f"Found {len(self_mapping_issues)} incorrect self-mappings")
    
    print("\n2. Checking for missing mappings...")
    missing_mapping_issues = find_missing_mappings()
    print(f"Found {len(missing_mapping_issues)} missing mappings")
    
    all_issues = self_mapping_issues + missing_mapping_issues
    
    if not all_issues:
        print("✅ No additional mapping issues found!")
        return
    
    print(f"\n📊 Found {len(all_issues)} total issues to fix")
    
    # Show dry run first
    apply_fixes(all_issues, dry_run=True)
    
    # Ask for confirmation
    response = input("\nDo you want to apply these fixes? (y/N): ").strip().lower()
    
    if response == 'y':
        apply_fixes(all_issues, dry_run=False)
    else:
        print("❌ Fixes not applied.")

if __name__ == "__main__":
    main()
