#!/usr/bin/env python3
"""
Script to reorganize the current directory to match betting.bak structure
"""

import os
import shutil
from pathlib import Path

def create_directories():
    """Create the directory structure matching betting.bak"""
    directories = [
        'app',
        'docs', 
        'logs',
        'models',
        'scripts',
        'tests',
        'tests/csv_tests',
        'tests/data_validation', 
        'tests/database_tests',
        'tests/integration_tests',
        'tests/prediction_tests',
        'tests/results'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")

def move_files():
    """Move files to their appropriate directories"""
    
    # Files to move to scripts/
    script_files = [
        'browse_test_results.py',
        'cleanup_root_files.py', 
        'comprehensive_team_mapping_fix.py',
        'consolidate_teams.py',
        'database_example.py',
        'efficient_testing_framework.py',
        'enhanced_ml_test.py',
        'find_broken_team_mappings.py',
        'find_leagues_under_90_percent.py',
        'find_next_league.py',
        'find_remaining_mapping_issues.py',
        'fix_team_name_mappings.py',
        'format_config_dictionaries.py',
        'investigate_db.py',
        'league_fix_template.py',
        'overwrite_config.py',
        'populate_canonical_ids.py',
        'quick_component_tests.py',
        'remove_match_ids.py',
        'run_h2h_batch.py',
        'team_name_mapper.py',
        'verify_database.py',
        'run_h2h_automation.sh'
    ]
    
    # Files to move to tests/
    test_files = [
        'test_10_epl_matches.py',
        'test_all_leagues.py', 
        'test_canonical_resolution.py',
        'test_model_training.py',
        'test_model_training_batch.py',
        'test_prediction_generation.py',
        'test_prediction_pipeline.py',
        'test_specific_leagues.py'
    ]
    
    # Files to move to docs/
    doc_files = [
        'BETTING_MARKETS_IMPLEMENTATION.md',
        'DATABASE_MIGRATION_SUMMARY.md',
        'EFFICIENT_TESTING_STRATEGY.md',
        'LEAGUE_FIXING_GUIDE.md',
        'LEAGUE_TESTING_REPORT.md',
        'how_to_use.md',
        'scraping_features.md'
    ]
    
    # Files to move to app/
    app_files = [
        'web_app.py'
    ]
    
    # Move script files
    for file in script_files:
        if os.path.exists(file):
            shutil.move(file, f'scripts/{file}')
            print(f"Moved {file} to scripts/")
    
    # Move test files  
    for file in test_files:
        if os.path.exists(file):
            shutil.move(file, f'tests/{file}')
            print(f"Moved {file} to tests/")
    
    # Move doc files
    for file in doc_files:
        if os.path.exists(file):
            shutil.move(file, f'docs/{file}')
            print(f"Moved {file} to docs/")
    
    # Move app files
    for file in app_files:
        if os.path.exists(file):
            shutil.move(file, f'app/{file}')
            print(f"Moved {file} to app/")
    
    # Move static and templates to app/
    if os.path.exists('static'):
        if os.path.exists('app/static'):
            shutil.rmtree('app/static')
        shutil.move('static', 'app/static')
        print("Moved static/ to app/static/")
    
    if os.path.exists('templates'):
        if os.path.exists('app/templates'):
            shutil.rmtree('app/templates')
        shutil.move('templates', 'app/templates')
        print("Moved templates/ to app/templates/")

def clean_root():
    """Remove files that should be cleaned from root"""
    files_to_remove = [
        '1',  # This seems like a temporary file
    ]
    
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"Removed {file} from root")

def main():
    print("Starting project reorganization...")
    
    # Create directory structure
    create_directories()
    
    # Move files to appropriate directories
    move_files()
    
    # Clean up root directory
    clean_root()
    
    print("\nReorganization complete!")
    print("\nRemaining root files should be:")
    print("- .gitignore")
    print("- .python-version") 
    print("- README.md")
    print("- create_database.py")
    print("- predictions.yaml")
    print("- pyproject.toml")
    print("- requirements.txt")
    print("- uv.lock")
    print("- batch_configs/ (directory)")
    print("- checkpoints/ (directory)")
    print("- data/ (directory)")
    print("- memory-bank/ (directory)")
    print("- roocode_tools_docs/ (directory)")
    print("- src/ (directory)")
    print("- betting.bak/ (directory)")

if __name__ == "__main__":
    main()