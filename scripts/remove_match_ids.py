#!/usr/bin/env python3
"""
Script to remove match IDs from HEAD_TO_HEAD_URLS in league config files.
This script removes the '#match_id' portion from URLs to make them point to
general head-to-head stats pages rather than specific fixtures.
"""

import os
import re
from pathlib import Path

# Define path to league configs
CONFIGS_DIR = Path("src/scrapers/league_configs")

def remove_match_ids():
    """
    Remove match IDs from HEAD_TO_HEAD_URLS in all league config files.
    """
    # Get all Python files in the configs directory
    config_files = list(CONFIGS_DIR.glob("*.py"))
    print(f"Found {len(config_files)} config files.")
    
    # Track statistics
    modified_files = 0
    modified_urls = 0
    
    # Process each config file
    for config_file in config_files:
        league_name = config_file.stem
        
        try:
            # Read the file content
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if the file contains HEAD_TO_HEAD_URLS
            if "HEAD_TO_HEAD_URLS" not in content:
                print(f"Skipping {league_name} - no HEAD_TO_HEAD_URLS found")
                continue
            
            # Use regex to find URLs with match IDs and remove the match IDs
            pattern = r"(https?://footystats\.org/[^'\"#]+)#\d+"
            updated_content, count = re.subn(pattern, r"\1", content)
            
            # If changes were made, save the file
            if count > 0:
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                
                modified_files += 1
                modified_urls += count
                print(f"Updated {league_name} - removed {count} match IDs")
            else:
                print(f"No match IDs found in {league_name}")
                
        except Exception as e:
            print(f"Error processing {league_name}: {str(e)}")
    
    # Print summary
    print("\nRemoval Summary:")
    print(f"Modified {modified_files} out of {len(config_files)} config files")
    print(f"Removed {modified_urls} match IDs from URLs")

def main():
    """Main function to run the removal process."""
    print("Starting match ID removal...")
    remove_match_ids()
    print("Match ID removal completed!")

if __name__ == "__main__":
    main()
