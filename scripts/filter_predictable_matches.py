import pandas as pd
import re
from fuzzywuzzy import process

CUSTOM_LEAGUE_MAPPING = {
    "SUPER_LEAGUE": "CHINA_SUPER_LEAGUE",
    "PROMOTION_LEAGUE": "SWITZERLAND_PROMOTION_LEAGUE",
    "DIVISION_1": "FRANCE_FEMININE_DIVISION_1",
    "NATIONAL_LEAGUE": "ENGLAND_NATIONAL_LEAGUE",
    "QUEENSLAND_PREMIER_LEAGUE": "AUSTRALIA_QUEENSLAND_PREMIER_LEAGUE",
    "CALCUTTA_PREMIER_DIVISION": "INDIA_CALCUTTA_PREMIER_DIVISION",
    "NPL_PREMIER_DIVISION": "ENGLAND_NPL_PREMIER_DIVISION",
    "NSW_LEAGUE_ONE": "AUSTRALIA_NSW_LEAGUE_1",
    "LEAGUE_ONE": "SCOTLAND_LEAGUE_ONE",
    "LEAGUE_TWO": "SCOTLAND_LEAGUE_TWO",
    "PRIMERA_B": "CHILE_PRIMERA_B",
    "PRIMERA_A": "COLOMBIA_PRIMERA_A",
    "A_LYGA": "LITHUANIA_A_LYGA",
    "1._DEILD": "FAROE_ISLANDS_1_DEILD",
    "2ND_DIVISION": "DENMARK_2ND_DIVISION",
    "1ST_DIVISION": "DENMARK_1ST_DIVISION",
    "PROFESSIONAL_DEVELOPMENT_LEAGUE": "ENGLAND_PROFESSIONAL_DEVELOPMENT_LEAGUE",
    "REGIONALLIGA_SUDWEST": "GERMANY_REGIONALLIGA_SUDWEST",
    "REGIONALLIGA_WEST": "GERMANY_REGIONALLIGA_WEST",
    "REGIONALLIGA_NORTH": "GERMANY_REGIONALLIGA_NORDOST",
    "USL_LEAGUE_ONE": "USA_USL_LEAGUE_ONE",
    "WK_LEAGUE_WOMEN": "SOUTH_KOREA_WK_LEAGUE_WOMEN",
    "LIGA_WOMEN": "AUSTRIA_BUNDESLIGA_WOMEN",
    "U19_LEAGUE": "CZECH_REPUBLIC_U19_LEAGUE",
    "YKKONEN": "FINLAND_YKKONEN",
    "ESILIIGA_B": "ESTONIA_ESILIIGA_B",
    "LIGUE_1": "FRANCE_LIGUE_1",
    "LIGA_3": "GEORGIA_LIGA_3",
    "I_LYGA": "LITHUANIA_I_LYGA",
    "CHALLENGE_LEAGUE": "SWITZERLAND_CHALLENGE_LEAGUE",
    "SUPERLIGA": "ALBANIA_SUPERLIGA",
    "LIGUE_2": "FRANCE_LIGUE_2",
    "ELITESERIEN": "NORWAY_ELITESERIEN",
    "1._LIGA_WOMEN": "CZECH_REPUBLIC_1_LIGA_WOMEN",
    "DIVISION_2": "NORWAY_DIVISION_2_GROUP_1",
    "SWPL_1_WOMEN": "SCOTLAND_SWPL_1_WOMEN",
    "REGIONALLIGA_BAYERN": "GERMANY_REGIONALLIGA_BAYERN",
    "SUPER_LIG": "TURKEY_SUPER_LIG",
    "2._LIGA": "SLOVAKIA_2._LIGA",
    "EKSTRALIGA_WOMEN": "POLAND_EKSTRALIGA_WOMEN",
    "1._LIG": "TURKEY_1_LIG",
    "KANSALLINEN_LIIGA_WOMEN": "FINLAND_KANSALLINEN_LIIGA_WOMEN",
    "THAI_LEAGUE_2": "THAILAND_THAI_LEAGUE_2",
    "VTORA_LIGA": "BULGARIA_VTORA_LIGA",
    "DRUGA_NL": "CROATIA_DRUGA_HNL",
    "2._BUNDESLIGA": "GERMANY_2_BUNDESLIGA_WOMEN",
    "PREMIER_SOCCER_LEAGUE": "ZIMBABWE_PREMIER_SOCCER_LEAGUE",
    "VEIKKAUSLIIGA": "FINLAND_VEIKKAUSLIIGA",
    "EERSTE_DIVISIE": "NETHERLANDS_EERSTE_DIVISIE",
    "PRIMAVERA_1": "ITALY_PRIMAVERA_1",
    "PRVA_NL": "CROATIA_PRVA_NL",
    "THAI_LEAGUE_1": "THAILAND_THAI_LEAGUE_1",
    "EKSTRAKLASA": "POLAND_EKSTRAKLASA",
    "NB_I.": "HUNGARY_NB_I",
    "REGIONALLIGA_NORDOST": "GERMANY_REGIONALLIGA_NORDOST",
    "CHAMPIONSHIP": "USA_USL_CHAMPIONSHIP",
    "LIGA_DE_ASCENSO": "COSTA_RICA_LIGA_DE_ASCENSO",
    "HNL": "CROATIA_PRVA_HNL",
    "YKKOSLIIGA": "FINLAND_YKKOSLIIGA",
    "LIGA_1": "INDONESIA_LIGA_1",
    "MEISTRILIIGA": "ESTONIA_MEISTRILIIGA",
    "PRVA_LIGA": "SERBIA_PRVA_LIGA",
    "BUNDESLIGA": "AUSTRIA_BUNDESLIGA_WOMEN",
    "PREMIER_DIVISION": "IRELAND_PREMIER_DIVISION",
    "NATIONAL_2_-_GROUP_A": "FRANCE_NATIONAL_2_GROUP_A",
    "NATIONAL_2_-_GROUP_C": "FRANCE_NATIONAL_2_GROUP_C",
    "NATIONAL_2_-_GROUP_B": "FRANCE_NATIONAL_2_GROUP_B",
    "VYSSHAYA_LIGA": "TAJIKISTAN_VYSSHAYA_LIGA",
    "PRIMERA_NACIONAL": "ARGENTINA_PRIMERA_NACIONAL",
    "SERIE_B": "ITALY_SERIE_B",
    "LEAGUE_CUP_A": "ICELAND_LEAGUE_CUP_A",
    "COPA_DE_LA_LIGA_PROFESIONAL": "ARGENTINA_COPA_DE_LA_LIGA_PROFESIONAL",
    "AFC_CUP": "AFC_CUP",
    "AFC_CHAMPIONS_LEAGUE": "AFC_CHAMPIONS_LEAGUE",
    "CAF_CHAMPIONS_LEAGUE": "CAF_CHAMPIONS_LEAGUE",
    "CAF_CONFEDERATION_CUP": "CAF_CONFEDERATION_CUP",
    "AFC_U23_ASIAN_CUP_QUALIFICATION": "AFC_U23_ASIAN_CUP_QUALIFICATION",
    "UEFA_EUROPA_CONFERENCE_LEAGUE": "UEFA_EUROPA_CONFERENCE_LEAGUE",
    "UEFA_EUROPA_LEAGUE": "UEFA_EUROPA_LEAGUE",
    "UEFA_CHAMPIONS_LEAGUE": "UEFA_CHAMPIONS_LEAGUE",
    "COPA_LIBERTADORES": "COPA_LIBERTADORES",
    "COPA_SUDAMERICANA": "COPA_SUDAMERICANA",
    "CONCACAF_CHAMPIONS_CUP": "CONCACAF_CHAMPIONS_CUP",
    "CONCACAF_CENTRAL_AMERICAN_CUP": "CONCACAF_CENTRAL_AMERICAN_CUP",
    "CONCACAF_CARIBBEAN_CUP": "CONCACAF_CARIBBEAN_CUP",
}

def get_supported_leagues():
    """
    Reads the list of supported leagues from the output of the --list-leagues command.
    """
    with open('supported_leagues.txt', 'r') as f:
        leagues_output = f.read()

    leagues = set()
    for line in leagues_output.split('\n'):
        if '•' in line:
            match = re.search(r'•\s(.*?)\s\(', line)
            if match:
                leagues.add(match.group(1).strip())
    return leagues

def find_best_match(league_name, supported_leagues):
    """
    Finds the best match for a league name from the list of supported leagues.
    """
    if league_name in CUSTOM_LEAGUE_MAPPING:
        return CUSTOM_LEAGUE_MAPPING[league_name]
    
    best_match, score = process.extractOne(league_name, supported_leagues)
    if score > 85:  # Confidence threshold
        return best_match
    return None

def filter_matches(supported_leagues):
    """
    Filters the matches from the todays_matches.csv file based on the supported leagues.
    """
    predictable_matches = []
    with open('data/today_matches/todays_matches.csv', 'r') as f:
        header = f.readline().strip().split(',')
        try:
            league_name_index = header.index('league_name')
        except ValueError:
            print("Error: 'league_name' column not found in the CSV header.")
            return

        for line in f:
            values = line.strip().split(',')
            if len(values) > league_name_index:
                league_name = values[league_name_index].upper().replace(' ', '_')
                best_match = find_best_match(league_name, supported_leagues)
                if best_match:
                    # Replace the original league name with the matched one
                    values[league_name_index] = best_match
                    predictable_matches.append(','.join(values))

    with open('data/today_matches/predictable_matches.csv', 'w') as f:
        f.write(','.join(header) + '\n')
        for match in predictable_matches:
            f.write(match + '\n')
    
    print(f"Successfully filtered {len(predictable_matches)} predictable matches.")

if __name__ == '__main__':
    supported_leagues = get_supported_leagues()
    filter_matches(supported_leagues)
