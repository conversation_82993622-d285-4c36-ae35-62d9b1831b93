#!/bin/bash

# H2H Automation Runner Script
# This script runs the H2H automation in a loop, restarting the process between iterations
# to avoid rate limiting issues that occur when running multiple iterations in the same process.

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AUTOMATION_SCRIPT="$SCRIPT_DIR/../src/scrapers/h2h_automation.py"
LOG_FILE="$SCRIPT_DIR/h2h_automation_runner.log"
MIN_DELAY=5    # Minimum seconds between iterations
MAX_DELAY=10   # Maximum seconds between iterations
MAX_ITERATIONS=100000  # Safety limit to prevent infinite loops

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to log colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to generate random delay
random_delay() {
    echo $(( RANDOM % (MAX_DELAY - MIN_DELAY + 1) + MIN_DELAY ))
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --league LEAGUE_NAME    Target a specific league (default: auto-select first with work)"
    echo "  --max-iterations N      Maximum number of iterations (default: $MAX_ITERATIONS)"
    echo "  --min-delay N          Minimum delay between iterations in seconds (default: $MIN_DELAY)"
    echo "  --max-delay N          Maximum delay between iterations in seconds (default: $MAX_DELAY)"
    echo "  --progress             Show current progress and exit"
    echo "  --reset-failed         Reset failed leagues to allow retrying them"
    echo "  --show-failed          Show currently failed leagues"
    echo "  --recent-results-only  Scrape only recent results data (skip H2H stats)"
    echo "  --help                 Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Auto-select first league with work"
    echo "  $0 --league SERBIA_PRVA_LIGA         # Focus on specific league until complete"
    echo "  $0 --max-iterations 50               # Limit to 50 iterations"
    echo "  $0 --progress                        # Show progress only"
    echo "  $0 --reset-failed                    # Reset failed leagues"
    echo "  $0 --show-failed                     # Show failed leagues"
}

# Parse command line arguments
LEAGUE=""
PROGRESS_ONLY=false
RESET_FAILED=false
SHOW_FAILED=false
RECENT_RESULTS_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --league)
            LEAGUE="$2"
            shift 2
            ;;
        --max-iterations)
            MAX_ITERATIONS="$2"
            shift 2
            ;;
        --min-delay)
            MIN_DELAY="$2"
            shift 2
            ;;
        --max-delay)
            MAX_DELAY="$2"
            shift 2
            ;;
        --progress)
            PROGRESS_ONLY=true
            shift
            ;;
        --reset-failed)
            RESET_FAILED=true
            shift
            ;;
        --show-failed)
            SHOW_FAILED=true
            shift
            ;;
        --recent-results-only)
            RECENT_RESULTS_ONLY=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate script exists
if [[ ! -f "$AUTOMATION_SCRIPT" ]]; then
    log_error "Automation script not found: $AUTOMATION_SCRIPT"
    exit 1
fi

# Handle progress-only mode
if [[ "$PROGRESS_ONLY" == "true" ]]; then
    log_info "Showing progress summary..."
    source "$SCRIPT_DIR/../.venv/bin/activate" && python "$AUTOMATION_SCRIPT" --progress
    exit 0
fi

# Handle reset-failed mode
if [[ "$RESET_FAILED" == "true" ]]; then
    log_info "Resetting failed leagues..."
    source "$SCRIPT_DIR/../.venv/bin/activate" && python "$AUTOMATION_SCRIPT" --reset-failed
    exit 0
fi

# Handle show-failed mode
if [[ "$SHOW_FAILED" == "true" ]]; then
    log_info "Showing failed leagues..."
    source "$SCRIPT_DIR/../.venv/bin/activate" && python "$AUTOMATION_SCRIPT" --show-failed
    exit 0
fi

# Validate Python environment
if ! command -v python3 &> /dev/null; then
    log_error "python3 is not installed or not in PATH"
    exit 1
fi

# Create log file
touch "$LOG_FILE"

# Main execution
if [[ "$RECENT_RESULTS_ONLY" == "true" ]]; then
    log_info "Starting Recent Results Automation Runner"
else
    log_info "Starting H2H Automation Runner"
fi
log_info "Script: $AUTOMATION_SCRIPT"
log_info "Log file: $LOG_FILE"
log_info "Max iterations: $MAX_ITERATIONS"
log_info "Delay range: ${MIN_DELAY}-${MAX_DELAY} seconds"

if [[ "$RECENT_RESULTS_ONLY" == "true" ]]; then
    log_info "Mode: Recent Results Only (4 URLs per batch)"
else
    log_info "Mode: Full H2H Stats (4 URLs per batch)"
fi

if [[ -n "$LEAGUE" ]]; then
    log_info "Target league: $LEAGUE (will process until complete)"
else
    log_info "Target: Auto-select first league with remaining work"
fi

log_info "=========================================="

iteration=0
total_start_time=$(date +%s)

# Trap to handle interruption
trap 'log_warning "Automation interrupted by user (Ctrl+C)"; log_info "Progress has been saved. You can resume by running this script again."; exit 130' INT

while [[ $iteration -lt $MAX_ITERATIONS ]]; do
    iteration=$((iteration + 1))
    
    log_info "Starting iteration $iteration/$MAX_ITERATIONS"
    
    # Build command with virtual environment activation
    cmd="source \"$SCRIPT_DIR/../.venv/bin/activate\" && python \"$AUTOMATION_SCRIPT\""
    if [[ -n "$LEAGUE" ]]; then
        cmd="$cmd --league \"$LEAGUE\""
    fi
    if [[ "$RECENT_RESULTS_ONLY" == "true" ]]; then
        cmd="$cmd --recent-results-only"
    fi
    
    # Run the automation script
    iteration_start_time=$(date +%s)
    
    if eval "$cmd"; then
        # Exit code 0 means all work is complete
        iteration_end_time=$(date +%s)
        iteration_duration=$((iteration_end_time - iteration_start_time))
        
        log_success "Iteration $iteration completed successfully in ${iteration_duration}s"
        
        if [[ -n "$LEAGUE" ]]; then
            log_success "🎉 League $LEAGUE completed!"
        else
            log_success "🎉 All H2H scraping completed!"
        fi
        
        total_end_time=$(date +%s)
        total_duration=$((total_end_time - total_start_time))
        log_info "Total runtime: ${total_duration}s across $iteration iterations"
        
        break
    else
        exit_code=$?
        iteration_end_time=$(date +%s)
        iteration_duration=$((iteration_end_time - iteration_start_time))
        
        if [[ $exit_code -eq 1 ]]; then
            # Exit code 1 means more work remains
            log_info "Iteration $iteration completed in ${iteration_duration}s - more work remains"
            
            # Calculate delay before next iteration
            delay=$(random_delay)
            log_info "Waiting ${delay} seconds before next iteration..."
            sleep "$delay"
            
        else
            # Other exit codes indicate errors
            log_error "Iteration $iteration failed with exit code $exit_code"
            log_error "Check the automation logs for details"
            exit $exit_code
        fi
    fi
done

# Check if we hit the iteration limit
if [[ $iteration -ge $MAX_ITERATIONS ]]; then
    log_warning "Reached maximum iteration limit ($MAX_ITERATIONS)"
    log_warning "There may still be work remaining. Check progress with: $0 --progress"
fi

total_end_time=$(date +%s)
total_duration=$((total_end_time - total_start_time))
log_info "Automation runner finished. Total runtime: ${total_duration}s across $iteration iterations"
