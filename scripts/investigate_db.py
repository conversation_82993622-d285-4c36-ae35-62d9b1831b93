import sqlite3

conn = sqlite3.connect('data/football_betting.db')
cursor = conn.cursor()

cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
tables = cursor.fetchall()
print('Tables:', tables)

print('\nColumns in team_stats:')
cursor.execute("PRAGMA table_info(team_stats)")
print(cursor.fetchall())

print('\nSample data from team_stats:')
cursor.execute("SELECT * FROM team_stats LIMIT 5")
print(cursor.fetchall())

print('\nSearching for KS Egnatia Rrogozhinë in teams table:')
cursor.execute("SELECT * FROM teams WHERE team_name LIKE '%Egnatia%'")
print(cursor.fetchall())

conn.close()