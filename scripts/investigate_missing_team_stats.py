#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to investigate the missing team stats issue in LITHUANIA_A_LYGA
and provide a solution.
"""

import pandas as pd
import os
from typing import Set, List, <PERSON><PERSON>

def analyze_team_name_mismatch(league_name: str) -> <PERSON><PERSON>[Set[str], Set[str], Set[str]]:
    """
    Analyze team name mismatches between results and team stats files.
    
    Returns:
        Tuple of (teams_in_results, teams_in_stats, missing_from_stats)
    """
    data_dir = f"data/raw/{league_name}"
    
    # Load results file
    results_file = os.path.join(data_dir, f"{league_name}_results.csv")
    results_df = pd.read_csv(results_file)
    
    # Load team stats file
    team_stats_file = os.path.join(data_dir, f"{league_name}_team_stats.csv")
    team_stats_df = pd.read_csv(team_stats_file)
    
    # Get unique team names from results
    teams_in_results = set()
    teams_in_results.update(results_df['Home Team'].unique())
    teams_in_results.update(results_df['Away Team'].unique())
    teams_in_results.discard(None)  # Remove any None values
    
    # Get unique team names from team stats
    teams_in_stats = set(team_stats_df['Team'].dropna().unique())
    
    # Find teams that are in results but missing from stats
    missing_from_stats = teams_in_results - teams_in_stats
    
    return teams_in_results, teams_in_stats, missing_from_stats

def demonstrate_validation_function():
    """
    Demonstrate how the validate_team_stats function works.
    """
    print("🔍 DEMONSTRATING THE VALIDATION FUNCTION")
    print("=" * 60)
    
    # Load the team stats
    team_stats_df = pd.read_csv("data/raw/LITHUANIA_A_LYGA/LITHUANIA_A_LYGA_team_stats.csv")
    
    # Test cases
    test_cases = [
        ("Banga", "Dainava"),      # Both teams exist
        ("Banga", "Riteriai"),     # Riteriai missing
        ("Nonexistent", "Banga"),  # Nonexistent missing
    ]
    
    for home_team, away_team in test_cases:
        print(f"\n🏠 Testing: {home_team} vs {away_team}")
        
        # Simulate the validation logic
        home_team_stats = team_stats_df[team_stats_df["Team"] == home_team]
        away_team_stats = team_stats_df[team_stats_df["Team"] == away_team]
        
        if home_team_stats.empty:
            print(f"   ❌ Home team '{home_team}' not found in team stats")
        else:
            print(f"   ✅ Home team '{home_team}' found")
            
        if away_team_stats.empty:
            print(f"   ❌ Away team '{away_team}' not found in team stats")
        else:
            print(f"   ✅ Away team '{away_team}' found")
            
        if home_team_stats.empty or away_team_stats.empty:
            print(f"   ⚠️  WARNING: Missing team stats for match: {home_team} vs {away_team}")
        else:
            print(f"   ✅ Match validation successful")

def suggest_solutions():
    """
    Suggest solutions for the missing team stats issue.
    """
    print("\n🛠️  SUGGESTED SOLUTIONS")
    print("=" * 60)
    
    solutions = [
        {
            "title": "1. Data Collection Issue",
            "description": "The team 'Riteriai' exists in match results but wasn't scraped for team statistics",
            "actions": [
                "Check if Riteriai has a team page on the source website",
                "Verify the team URL extraction process",
                "Re-scrape team statistics with updated team URLs"
            ]
        },
        {
            "title": "2. Team Name Variation",
            "description": "The team might exist under a different name in team stats",
            "actions": [
                "Check for name variations (e.g., 'Riteriai' vs 'FK Riteriai')",
                "Implement fuzzy matching for team names",
                "Create team name mapping files"
            ]
        },
        {
            "title": "3. Missing Team Handling",
            "description": "Handle cases where teams have no statistics gracefully",
            "actions": [
                "Create default/placeholder statistics for missing teams",
                "Skip matches with missing team data",
                "Log missing teams for manual review"
            ]
        },
        {
            "title": "4. Data Validation",
            "description": "Implement better data validation during scraping",
            "actions": [
                "Validate that all teams in results have corresponding stats",
                "Add warnings during data collection phase",
                "Implement automated data consistency checks"
            ]
        }
    ]
    
    for solution in solutions:
        print(f"\n{solution['title']}")
        print(f"Description: {solution['description']}")
        print("Actions:")
        for action in solution['actions']:
            print(f"  • {action}")

def main():
    league_name = "LITHUANIA_A_LYGA"
    
    print(f"🔍 INVESTIGATING MISSING TEAM STATS ISSUE")
    print(f"League: {league_name}")
    print("=" * 60)
    
    # Analyze team name mismatches
    teams_in_results, teams_in_stats, missing_from_stats = analyze_team_name_mismatch(league_name)
    
    print(f"\n📊 ANALYSIS RESULTS:")
    print(f"Teams in match results: {len(teams_in_results)}")
    print(f"Teams in team statistics: {len(teams_in_stats)}")
    print(f"Teams missing from statistics: {len(missing_from_stats)}")
    
    print(f"\n📋 TEAMS IN MATCH RESULTS:")
    for team in sorted(teams_in_results):
        print(f"  • {team}")
    
    print(f"\n📋 TEAMS IN TEAM STATISTICS:")
    for team in sorted(teams_in_stats):
        print(f"  • {team}")
    
    if missing_from_stats:
        print(f"\n❌ TEAMS MISSING FROM STATISTICS:")
        for team in sorted(missing_from_stats):
            print(f"  • {team}")
    else:
        print(f"\n✅ All teams from results have corresponding statistics!")
    
    # Demonstrate the validation function
    demonstrate_validation_function()
    
    # Suggest solutions
    suggest_solutions()
    
    print(f"\n🎯 ROOT CAUSE:")
    print("The 'Missing team stats for match' warning occurs when:")
    print("1. A team appears in match results (Home Team or Away Team)")
    print("2. But that same team name is not found in the team_stats.csv file")
    print("3. The validate_team_stats() function in feature_engineering/utils.py")
    print("   performs an exact string match: team_stats[team_stats['Team'] == team_name]")
    print("4. If no match is found, it logs the warning and returns None, None")
    print("5. This causes the match to be skipped during feature engineering")

if __name__ == "__main__":
    main()