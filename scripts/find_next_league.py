#!/usr/bin/env python3
"""
Find Next League to Fix

This script helps identify the next best league to fix based on:
1. Success rate (70-90% are good candidates)
2. Number of teams (more teams = higher impact)
3. Availability of config mappings
4. Alphabetical order for systematic coverage
"""

import sys
import os
from pathlib import Path

# Add src to path for database imports
sys.path.append('src')
from database.football_db import get_database

def get_league_priority_score(league_name: str, working: int, total: int, has_config: bool) -> float:
    """Calculate a priority score for a league."""
    if total == 0:
        return 0
    
    success_rate = working / total
    
    # Skip leagues that are too good (>95%) or too bad (<20%)
    if success_rate > 0.95 or success_rate < 0.20:
        return 0
    
    # Skip youth leagues and very small leagues
    if any(keyword in league_name.lower() for keyword in ['u21', 'u19', 'u20', 'youth', 'reserve']):
        return 0
    
    if total < 8:  # Very small leagues
        return 0
    
    # Calculate priority score
    # Factors: team count (impact), success rate (fixability), config availability
    team_factor = min(total / 30, 1.0)  # Normalize to max 30 teams
    success_factor = 1.0 - abs(success_rate - 0.80)  # Prefer ~80% success rate
    config_factor = 1.2 if has_config else 1.0  # Bonus for config availability
    
    score = team_factor * success_factor * config_factor * 100
    return score

def check_config_availability(league_name: str) -> bool:
    """Check if a league has config mappings available."""
    config_path = f"src/scrapers/league_configs/{league_name}.py"
    
    if not os.path.exists(config_path):
        return False
    
    try:
        with open(config_path, 'r') as f:
            content = f.read()
            return 'TEAM_NAME_MAPPING' in content
    except:
        return False

def analyze_all_leagues():
    """Analyze all leagues and suggest the next ones to fix."""
    print("🔍 FINDING NEXT LEAGUE TO FIX")
    print("=" * 50)
    
    league_candidates = []
    
    with get_database() as db:
        # Get all leagues
        leagues = db.execute_query("SELECT league_name FROM leagues ORDER BY league_name")
        
        if leagues.empty:
            print("❌ No leagues found")
            return
        
        print(f"📊 Analyzing {len(leagues)} leagues...")
        
        for _, league_row in leagues.iterrows():
            league_name = league_row['league_name']
            
            # Get team stats for this league
            teams = db.execute_query('''
                SELECT t.team_name
                FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE l.league_name = ?
            ''', (league_name,))
            
            if teams.empty:
                continue
            
            total_teams = len(teams)
            working_teams = 0
            
            # Count working teams
            for _, team in teams.iterrows():
                stats = db.get_team_stats(team['team_name'], league_name)
                if not stats.empty:
                    working_teams += 1
            
            success_rate = working_teams / total_teams if total_teams > 0 else 0
            has_config = check_config_availability(league_name)
            priority_score = get_league_priority_score(league_name, working_teams, total_teams, has_config)
            
            if priority_score > 0:  # Only include viable candidates
                league_candidates.append({
                    'league_name': league_name,
                    'working_teams': working_teams,
                    'total_teams': total_teams,
                    'success_rate': success_rate,
                    'has_config': has_config,
                    'priority_score': priority_score,
                    'issues': total_teams - working_teams
                })
    
    # Sort by priority score (descending)
    league_candidates.sort(key=lambda x: x['priority_score'], reverse=True)
    
    # Display top candidates
    print(f"\n🎯 TOP LEAGUE CANDIDATES TO FIX")
    print("=" * 70)
    print(f"{'Rank':<4} {'League':<35} {'Teams':<8} {'Rate':<6} {'Config':<6} {'Score':<6}")
    print("-" * 70)
    
    for i, league in enumerate(league_candidates[:20], 1):
        config_status = "✅" if league['has_config'] else "❌"
        print(f"{i:<4} {league['league_name']:<35} "
              f"{league['working_teams']}/{league['total_teams']:<8} "
              f"{league['success_rate']*100:.1f}%{'':<2} "
              f"{config_status:<6} "
              f"{league['priority_score']:.1f}")
    
    # Show next recommended league
    if league_candidates:
        next_league = league_candidates[0]
        print(f"\n🚀 RECOMMENDED NEXT LEAGUE: {next_league['league_name']}")
        print(f"   Teams: {next_league['working_teams']}/{next_league['total_teams']} "
              f"({next_league['success_rate']*100:.1f}%)")
        print(f"   Issues to fix: {next_league['issues']} teams")
        print(f"   Config available: {'Yes' if next_league['has_config'] else 'No'}")
        print(f"   Priority score: {next_league['priority_score']:.1f}")
        
        # Show command to start fixing
        print(f"\n💡 TO START FIXING:")
        print(f"   1. cp league_fix_template.py fix_{next_league['league_name'].lower()}.py")
        print(f"   2. Edit the LEAGUE_NAME and SPECIFIC_MAPPINGS in the new file")
        print(f"   3. python3 fix_{next_league['league_name'].lower()}.py")
        
        return next_league['league_name']
    else:
        print("\n⚠️  No suitable league candidates found")
        return None

def show_alphabetical_next():
    """Show next leagues alphabetically for systematic coverage."""
    print(f"\n📋 ALPHABETICAL APPROACH")
    print("=" * 30)
    
    # Fixed leagues (update this list as you fix more)
    fixed_leagues = {
        'ARMENIA_PREMIER_LEAGUE'  # Add more as you fix them
    }
    
    with get_database() as db:
        leagues = db.execute_query("SELECT league_name FROM leagues ORDER BY league_name")
        
        print("Next leagues alphabetically:")
        count = 0
        for _, league_row in leagues.iterrows():
            league_name = league_row['league_name']
            
            if league_name not in fixed_leagues:
                # Check if it's worth fixing
                teams = db.execute_query('''
                    SELECT COUNT(*) as count FROM teams t
                    JOIN leagues l ON t.league_id = l.league_id
                    WHERE l.league_name = ?
                ''', (league_name,))
                
                team_count = teams.iloc[0]['count'] if not teams.empty else 0
                
                if team_count >= 8:  # Only show leagues with reasonable team counts
                    has_config = check_config_availability(league_name)
                    config_status = "✅" if has_config else "❌"
                    print(f"   {league_name} ({team_count} teams) Config: {config_status}")
                    count += 1
                    
                    if count >= 10:  # Show first 10
                        break

def main():
    """Main function."""
    # Find priority-based recommendation
    recommended_league = analyze_all_leagues()
    
    # Show alphabetical options
    show_alphabetical_next()
    
    print(f"\n🎯 SUMMARY")
    print("=" * 20)
    if recommended_league:
        print(f"Priority recommendation: {recommended_league}")
    print("Use the priority list for maximum impact, or go alphabetically for systematic coverage.")
    print("Avoid leagues with <20% or >95% success rates.")

if __name__ == "__main__":
    main()
