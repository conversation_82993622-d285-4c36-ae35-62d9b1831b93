#!/usr/bin/env python3
"""
SQLite Database Creation and Migration Script

This script creates a centralized SQLite database from all CSV files in the data/raw directory.
It replaces the cumbersome CSV file system with a structured database for better data management.
"""

import sqlite3
import pandas as pd
import os
import glob
import logging
from pathlib import Path
from datetime import datetime
import re

# Setup logging
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/database_creation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_PATH = 'data/football_betting.db'
RAW_DATA_DIR = 'data/raw'

class FootballDatabaseCreator:
    def __init__(self, db_path=DATABASE_PATH, raw_data_dir=RAW_DATA_DIR):
        self.db_path = db_path
        self.raw_data_dir = raw_data_dir
        self.conn = None
        
        # Ensure database directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
    def connect(self):
        """Create database connection."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.execute("PRAGMA foreign_keys = ON")
            logger.info(f"Connected to database: {self.db_path}")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def disconnect(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()
            logger.info("Database connection closed")
    
    def create_tables(self):
        """Create all database tables with proper schema."""
        
        # Leagues table
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS leagues (
                league_id INTEGER PRIMARY KEY AUTOINCREMENT,
                league_name TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # League statistics table
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS league_stats (
                stat_id INTEGER PRIMARY KEY AUTOINCREMENT,
                league_id INTEGER NOT NULL,
                stat_name TEXT NOT NULL,
                stat_value REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (league_id) REFERENCES leagues (league_id),
                UNIQUE(league_id, stat_name)
            )
        ''')
        
        # Teams table
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS teams (
                team_id INTEGER PRIMARY KEY AUTOINCREMENT,
                league_id INTEGER NOT NULL,
                team_name TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (league_id) REFERENCES leagues (league_id),
                UNIQUE(league_id, team_name)
            )
        ''')
        
        # Team statistics table
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS team_stats (
                stat_id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id INTEGER NOT NULL,
                total_home_played INTEGER,
                total_home_wins INTEGER,
                total_home_draws INTEGER,
                total_home_losses INTEGER,
                total_away_played INTEGER,
                total_away_wins INTEGER,
                total_away_draws INTEGER,
                total_away_losses INTEGER,
                total_played INTEGER,
                total_wins INTEGER,
                total_draws INTEGER,
                total_losses INTEGER,
                points_per_game REAL,
                home_points_per_game REAL,
                away_points_per_game REAL,
                goals_scored_home INTEGER,
                goals_scored_away INTEGER,
                goals_scored_all INTEGER,
                goals_scored_per_match_home REAL,
                goals_scored_per_match_away REAL,
                goals_scored_per_match_all REAL,
                goals_conceded_home INTEGER,
                goals_conceded_away INTEGER,
                goals_conceded_all INTEGER,
                goals_conceded_per_match_home REAL,
                goals_conceded_per_match_away REAL,
                goals_conceded_per_match_all REAL,
                gf_ga_per_match_home REAL,
                gf_ga_per_match_away REAL,
                gf_ga_per_match_all REAL,
                ppg_last_8 REAL,
                avg_goals_scored_last_8 REAL,
                avg_goals_conceded_last_8 REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (team_id) REFERENCES teams (team_id)
            )
        ''')
        
        # League table/standings
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS league_table (
                table_id INTEGER PRIMARY KEY AUTOINCREMENT,
                team_id INTEGER NOT NULL,
                position INTEGER NOT NULL,
                matches_played INTEGER,
                wins INTEGER,
                draws INTEGER,
                losses INTEGER,
                goals_for INTEGER,
                goals_against INTEGER,
                goal_difference INTEGER,
                points INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (team_id) REFERENCES teams (team_id)
            )
        ''')
        
        # Match results
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS match_results (
                match_id INTEGER PRIMARY KEY AUTOINCREMENT,
                league_id INTEGER NOT NULL,
                match_date TEXT,
                home_team_id INTEGER NOT NULL,
                away_team_id INTEGER NOT NULL,
                home_score INTEGER,
                away_score INTEGER,
                result_code INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (league_id) REFERENCES leagues (league_id),
                FOREIGN KEY (home_team_id) REFERENCES teams (team_id),
                FOREIGN KEY (away_team_id) REFERENCES teams (team_id)
            )
        ''')
        
        # Head-to-head statistics
        self.conn.execute('''
            CREATE TABLE IF NOT EXISTS head_to_head_stats (
                h2h_id INTEGER PRIMARY KEY AUTOINCREMENT,
                league_id INTEGER NOT NULL,
                home_team_id INTEGER NOT NULL,
                away_team_id INTEGER NOT NULL,
                matchup TEXT NOT NULL,
                total_matches INTEGER,
                home_win_percentage REAL,
                away_win_percentage REAL,
                draw_percentage REAL,
                home_wins INTEGER,
                away_wins INTEGER,
                draws INTEGER,
                home_goals INTEGER,
                away_goals INTEGER,
                over_1_5_percentage REAL,
                over_2_5_percentage REAL,
                over_3_5_percentage REAL,
                btts_percentage REAL,
                home_clean_sheet_percentage REAL,
                away_clean_sheet_percentage REAL,
                recent_results TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (league_id) REFERENCES leagues (league_id),
                FOREIGN KEY (home_team_id) REFERENCES teams (team_id),
                FOREIGN KEY (away_team_id) REFERENCES teams (team_id),
                UNIQUE(league_id, home_team_id, away_team_id)
            )
        ''')
        
        # Create indexes for better performance
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_league_stats_league_id ON league_stats(league_id)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_teams_league_id ON teams(league_id)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_team_stats_team_id ON team_stats(team_id)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_league_table_team_id ON league_table(team_id)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_match_results_league_id ON match_results(league_id)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_match_results_teams ON match_results(home_team_id, away_team_id)')
        self.conn.execute('CREATE INDEX IF NOT EXISTS idx_h2h_league_teams ON head_to_head_stats(league_id, home_team_id, away_team_id)')
        
        self.conn.commit()
        logger.info("Database tables created successfully")
    
    def get_or_create_league(self, league_name):
        """Get league ID or create new league."""
        cursor = self.conn.execute(
            'SELECT league_id FROM leagues WHERE league_name = ?',
            (league_name,)
        )
        result = cursor.fetchone()
        
        if result:
            return result[0]
        else:
            cursor = self.conn.execute(
                'INSERT INTO leagues (league_name) VALUES (?)',
                (league_name,)
            )
            self.conn.commit()
            return cursor.lastrowid
    
    def get_or_create_team(self, league_id, team_name):
        """Get team ID or create new team."""
        cursor = self.conn.execute(
            'SELECT team_id FROM teams WHERE league_id = ? AND team_name = ?',
            (league_id, team_name)
        )
        result = cursor.fetchone()
        
        if result:
            return result[0]
        else:
            cursor = self.conn.execute(
                'INSERT INTO teams (league_id, team_name) VALUES (?, ?)',
                (league_id, team_name)
            )
            self.conn.commit()
            return cursor.lastrowid
    
    def import_league_stats(self, league_id, csv_file):
        """Import league statistics from CSV."""
        try:
            df = pd.read_csv(csv_file)
            
            # Clear existing stats for this league
            self.conn.execute('DELETE FROM league_stats WHERE league_id = ?', (league_id,))
            
            for _, row in df.iterrows():
                self.conn.execute('''
                    INSERT OR REPLACE INTO league_stats (league_id, stat_name, stat_value)
                    VALUES (?, ?, ?)
                ''', (league_id, row['Stat'], row['Value']))
            
            self.conn.commit()
            logger.info(f"Imported {len(df)} league statistics")
            
        except Exception as e:
            logger.error(f"Error importing league stats from {csv_file}: {e}")
    
    def import_team_stats(self, league_id, csv_file):
        """Import team statistics from CSV."""
        try:
            df = pd.read_csv(csv_file)
            
            for _, row in df.iterrows():
                team_id = self.get_or_create_team(league_id, row['Team'])
                
                # Clear existing stats for this team
                self.conn.execute('DELETE FROM team_stats WHERE team_id = ?', (team_id,))
                
                # Prepare data, handling NaN values
                data = {}
                for col in df.columns:
                    if col != 'Team':
                        value = row[col]
                        data[col] = None if pd.isna(value) else value
                
                # Insert team stats
                self.conn.execute('''
                    INSERT INTO team_stats (
                        team_id, total_home_played, total_home_wins, total_home_draws, total_home_losses,
                        total_away_played, total_away_wins, total_away_draws, total_away_losses,
                        total_played, total_wins, total_draws, total_losses,
                        points_per_game, home_points_per_game, away_points_per_game,
                        goals_scored_home, goals_scored_away, goals_scored_all,
                        goals_scored_per_match_home, goals_scored_per_match_away, goals_scored_per_match_all,
                        goals_conceded_home, goals_conceded_away, goals_conceded_all,
                        goals_conceded_per_match_home, goals_conceded_per_match_away, goals_conceded_per_match_all,
                        gf_ga_per_match_home, gf_ga_per_match_away, gf_ga_per_match_all,
                        ppg_last_8, avg_goals_scored_last_8, avg_goals_conceded_last_8
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    team_id,
                    data.get('total_home_played'), data.get('total_home_wins'), data.get('total_home_draws'), data.get('total_home_losses'),
                    data.get('total_away_played'), data.get('total_away_wins'), data.get('total_away_draws'), data.get('total_away_losses'),
                    data.get('total_played'), data.get('total_wins'), data.get('total_draws'), data.get('total_losses'),
                    data.get('points_per_game'), data.get('home_points_per_game'), data.get('away_points_per_game'),
                    data.get('goals_scored_home'), data.get('goals_scored_away'), data.get('goals_scored_all'),
                    data.get('goals_scored_per_match_home'), data.get('goals_scored_per_match_away'), data.get('goals_scored_per_match_all'),
                    data.get('goals_conceded_home'), data.get('goals_conceded_away'), data.get('goals_conceded_all'),
                    data.get('goals_conceded_per_match_home'), data.get('goals_conceded_per_match_away'), data.get('goals_conceded_per_match_all'),
                    data.get('gf_ga_per_match_home'), data.get('gf_ga_per_match_away'), data.get('gf_ga_per_match_all'),
                    data.get('ppg_last_8'), data.get('avg_goals_scored_last_8'), data.get('avg_goals_conceded_last_8')
                ))
            
            self.conn.commit()
            logger.info(f"Imported {len(df)} team statistics")
            
        except Exception as e:
            logger.error(f"Error importing team stats from {csv_file}: {e}")
    
    def import_league_table(self, league_id, csv_file):
        """Import league table from CSV."""
        try:
            df = pd.read_csv(csv_file)
            
            # Clear existing table for this league
            self.conn.execute('''
                DELETE FROM league_table 
                WHERE team_id IN (SELECT team_id FROM teams WHERE league_id = ?)
            ''', (league_id,))
            
            for _, row in df.iterrows():
                team_id = self.get_or_create_team(league_id, row['Team'])
                
                self.conn.execute('''
                    INSERT INTO league_table (
                        team_id, position, matches_played, wins, draws, losses,
                        goals_for, goals_against, goal_difference, points
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    team_id, row['Position'], row['MP'], row['W'], row['D'], row['L'],
                    row['GF'], row['GA'], row['GD'], row['Pts']
                ))
            
            self.conn.commit()
            logger.info(f"Imported {len(df)} league table entries")
            
        except Exception as e:
            logger.error(f"Error importing league table from {csv_file}: {e}")
    
    def parse_score(self, score_str):
        """Parse score string like '1 - 1' into home and away scores."""
        try:
            if pd.isna(score_str) or not score_str:
                return None, None
            
            # Handle various score formats
            score_str = str(score_str).strip()
            
            # Try different patterns
            patterns = [
                r'(\d+)\s*-\s*(\d+)',  # "1 - 1" or "1-1"
                r'(\d+)\s*:\s*(\d+)',  # "1:1"
                r'(\d+)\s+(\d+)',      # "1 1"
            ]
            
            for pattern in patterns:
                match = re.search(pattern, score_str)
                if match:
                    return int(match.group(1)), int(match.group(2))
            
            return None, None
            
        except Exception:
            return None, None
    
    def import_match_results(self, league_id, csv_file):
        """Import match results from CSV."""
        try:
            df = pd.read_csv(csv_file)
            
            # Clear existing results for this league
            self.conn.execute('DELETE FROM match_results WHERE league_id = ?', (league_id,))
            
            for _, row in df.iterrows():
                home_team_id = self.get_or_create_team(league_id, row['Home Team'])
                away_team_id = self.get_or_create_team(league_id, row['Away Team'])
                
                home_score, away_score = self.parse_score(row['Score'])
                
                self.conn.execute('''
                    INSERT INTO match_results (
                        league_id, match_date, home_team_id, away_team_id,
                        home_score, away_score, result_code
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    league_id, row['Date'], home_team_id, away_team_id,
                    home_score, away_score, row.get('Result')
                ))
            
            self.conn.commit()
            logger.info(f"Imported {len(df)} match results")
            
        except Exception as e:
            logger.error(f"Error importing match results from {csv_file}: {e}")
    
    def import_head_to_head_stats(self, league_id, csv_file):
        """Import head-to-head statistics from CSV."""
        try:
            df = pd.read_csv(csv_file)
            
            # Clear existing H2H stats for this league
            self.conn.execute('DELETE FROM head_to_head_stats WHERE league_id = ?', (league_id,))
            
            for _, row in df.iterrows():
                home_team_id = self.get_or_create_team(league_id, row['home_team_name'])
                away_team_id = self.get_or_create_team(league_id, row['away_team_name'])
                
                # Handle NaN values
                data = {}
                for col in df.columns:
                    if col not in ['home_team_name', 'away_team_name']:
                        value = row[col]
                        data[col] = None if pd.isna(value) else value
                
                self.conn.execute('''
                    INSERT INTO head_to_head_stats (
                        league_id, home_team_id, away_team_id, matchup,
                        total_matches, home_win_percentage, away_win_percentage, draw_percentage,
                        home_wins, away_wins, draws, home_goals, away_goals,
                        over_1_5_percentage, over_2_5_percentage, over_3_5_percentage,
                        btts_percentage, home_clean_sheet_percentage, away_clean_sheet_percentage,
                        recent_results
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    league_id, home_team_id, away_team_id, data.get('Matchup'),
                    data.get('total_matches'), data.get('home_win_percentage'), data.get('away_win_percentage'), data.get('draw_percentage'),
                    data.get('home_wins'), data.get('away_wins'), data.get('draws'), data.get('home_goals'), data.get('away_goals'),
                    data.get('over_1_5_percentage'), data.get('over_2_5_percentage'), data.get('over_3_5_percentage'),
                    data.get('btts_percentage'), data.get('home_clean_sheet_percentage'), data.get('away_clean_sheet_percentage'),
                    data.get('recent_results')
                ))
            
            self.conn.commit()
            logger.info(f"Imported {len(df)} head-to-head statistics")
            
        except Exception as e:
            logger.error(f"Error importing H2H stats from {csv_file}: {e}")
    
    def import_all_csv_data(self):
        """Import all CSV data from the raw data directory."""
        logger.info("Starting CSV data import...")
        
        # Get all league directories
        league_dirs = [d for d in os.listdir(self.raw_data_dir) 
                      if os.path.isdir(os.path.join(self.raw_data_dir, d))]
        
        total_leagues = len(league_dirs)
        logger.info(f"Found {total_leagues} leagues to process")
        
        for i, league_name in enumerate(league_dirs, 1):
            logger.info(f"Processing league {i}/{total_leagues}: {league_name}")
            
            league_dir = os.path.join(self.raw_data_dir, league_name)
            league_id = self.get_or_create_league(league_name)
            
            # Import different types of CSV files
            csv_files = {
                'league_stats': f"{league_name}_league_stats.csv",
                'team_stats': f"{league_name}_team_stats.csv",
                'league_table': f"{league_name}_league_table.csv",
                'results': f"{league_name}_results.csv",
                'head_to_head_stats': f"{league_name}_head_to_head_stats.csv",
                'recent_results': f"{league_name}_recent_results.csv"
            }
            
            for data_type, filename in csv_files.items():
                csv_path = os.path.join(league_dir, filename)
                
                if os.path.exists(csv_path):
                    try:
                        if data_type == 'league_stats':
                            self.import_league_stats(league_id, csv_path)
                        elif data_type == 'team_stats':
                            self.import_team_stats(league_id, csv_path)
                        elif data_type == 'league_table':
                            self.import_league_table(league_id, csv_path)
                        elif data_type == 'results':
                            self.import_match_results(league_id, csv_path)
                        elif data_type == 'head_to_head_stats':
                            self.import_head_to_head_stats(league_id, csv_path)
                        # Note: recent_results is often derived from H2H, so we skip it for now
                        
                    except Exception as e:
                        logger.error(f"Error importing {data_type} for {league_name}: {e}")
                else:
                    logger.warning(f"CSV file not found: {csv_path}")
        
        logger.info("CSV data import completed")
    
    def get_database_stats(self):
        """Get statistics about the database contents."""
        stats = {}
        
        tables = ['leagues', 'teams', 'league_stats', 'team_stats', 
                 'league_table', 'match_results', 'head_to_head_stats']
        
        for table in tables:
            cursor = self.conn.execute(f'SELECT COUNT(*) FROM {table}')
            stats[table] = cursor.fetchone()[0]
        
        # Database file size
        if os.path.exists(self.db_path):
            stats['database_size_mb'] = os.path.getsize(self.db_path) / (1024 * 1024)
        
        return stats
    
    def create_database(self):
        """Main method to create the complete database."""
        logger.info("Starting database creation process...")
        
        try:
            self.connect()
            self.create_tables()
            self.import_all_csv_data()
            
            # Print statistics
            stats = self.get_database_stats()
            logger.info("Database creation completed successfully!")
            logger.info("Database Statistics:")
            for table, count in stats.items():
                if table == 'database_size_mb':
                    logger.info(f"  Database size: {count:.2f} MB")
                else:
                    logger.info(f"  {table}: {count:,} records")
            
        except Exception as e:
            logger.error(f"Database creation failed: {e}")
            raise
        finally:
            self.disconnect()

def main():
    """Main function to create the database."""
    creator = FootballDatabaseCreator()
    creator.create_database()

if __name__ == "__main__":
    main()