#!/usr/bin/env python3
"""
<PERSON><PERSON>t to fix team name mappings in league config files by copying the correct mappings
from the team_names_mapped directory to the league_configs directory.

This script addresses the issue where league config files have incorrect TEAM_NAME_MAPPING
that causes "Missing team stats for match" warnings during testing.
"""

import os
import sys
import json
import importlib.util
from pathlib import Path

def load_config_from_py_file(file_path):
    """Load configuration from a Python file."""
    try:
        spec = importlib.util.spec_from_file_location("config", file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Get the LEAGUE_CONFIGS dictionary
        if hasattr(module, 'LEAGUE_CONFIGS'):
            return module.LEAGUE_CONFIGS
        else:
            print(f"Warning: No LEAGUE_CONFIGS found in {file_path}")
            return None
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def save_config_to_py_file(file_path, league_configs, league_name):
    """Save configuration to a Python file."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("LEAGUE_CONFIGS = {\n")
            f.write(f'    "{league_name}": {{\n')
            
            config = league_configs[league_name]
            
            # Write each section
            for key, value in config.items():
                if key == "TEAM_NAME_MAPPING":
                    f.write(f'        "{key}": {{\n')
                    for team_key, team_value in value.items():
                        f.write(f"            '{team_key}': '{team_value}',\n")
                    f.write("        },\n")
                elif isinstance(value, dict):
                    f.write(f'        "{key}": {{\n')
                    for sub_key, sub_value in value.items():
                        f.write(f"            '{sub_key}': '{sub_value}',\n")
                    f.write("        },\n")
                elif isinstance(value, str):
                    f.write(f'        "{key}": \'{value}\',\n')
                else:
                    f.write(f'        "{key}": {value},\n')
            
            f.write("    }\n")
            f.write("}\n\n")
            
            # Add the standard footer
            f.write(f'# Set the current league\n')
            f.write(f'CURRENT_LEAGUE = "{league_name}"\n')
            f.write(f'# Access the current league\'s configuration\n')
            f.write(f'CURRENT_CONFIG = LEAGUE_CONFIGS[CURRENT_LEAGUE]\n')
            f.write(f'CURRENT_CONFIG["CURRENT_LEAGUE"] = CURRENT_LEAGUE\n')
            f.write(f'LEAGUE_STATS_URL = CURRENT_CONFIG["LEAGUE_STATS_URL"]\n')
            f.write(f'LEAGUE_TABLE_URL = CURRENT_CONFIG["LEAGUE_TABLE_URL"]\n')
            f.write(f'TEAM_URLS = CURRENT_CONFIG["TEAM_URLS"]\n')
            f.write(f'HEAD_TO_HEAD_URLS = CURRENT_CONFIG["HEAD_TO_HEAD_URLS"]\n')
            f.write(f'TEAM_NAME_MAPPING = CURRENT_CONFIG["TEAM_NAME_MAPPING"]\n')
            
        return True
    except Exception as e:
        print(f"Error saving {file_path}: {e}")
        return False

def fix_team_mappings(league_name=None, dry_run=False):
    """
    Fix team name mappings for specified league or all leagues.
    
    Args:
        league_name: Specific league to fix, or None for all leagues
        dry_run: If True, only show what would be changed without making changes
    """
    
    # Define paths
    team_names_mapped_dir = Path("src/scrapers/team_names_mapped")
    league_configs_dir = Path("src/scrapers/league_configs")
    
    if not team_names_mapped_dir.exists():
        print(f"Error: {team_names_mapped_dir} directory not found")
        return False
    
    if not league_configs_dir.exists():
        print(f"Error: {league_configs_dir} directory not found")
        return False
    
    # Get list of leagues to process
    if league_name:
        leagues_to_process = [league_name]
    else:
        # Get all .py files from team_names_mapped directory
        leagues_to_process = []
        for file_path in team_names_mapped_dir.glob("*.py"):
            if not file_path.name.endswith('.bak'):
                leagues_to_process.append(file_path.stem)
    
    print(f"Processing {len(leagues_to_process)} leagues...")
    
    fixed_count = 0
    error_count = 0
    
    for league in leagues_to_process:
        source_file = team_names_mapped_dir / f"{league}.py"
        target_file = league_configs_dir / f"{league}.py"
        
        if not source_file.exists():
            print(f"⚠️  Source file not found: {source_file}")
            error_count += 1
            continue
        
        if not target_file.exists():
            print(f"⚠️  Target file not found: {target_file}")
            error_count += 1
            continue
        
        # Load source configuration (correct mappings)
        source_config = load_config_from_py_file(source_file)
        if not source_config or league not in source_config:
            print(f"⚠️  Could not load source config for {league}")
            error_count += 1
            continue
        
        # Load target configuration (current mappings)
        target_config = load_config_from_py_file(target_file)
        if not target_config or league not in target_config:
            print(f"⚠️  Could not load target config for {league}")
            error_count += 1
            continue
        
        # Compare team name mappings
        source_mapping = source_config[league].get("TEAM_NAME_MAPPING", {})
        target_mapping = target_config[league].get("TEAM_NAME_MAPPING", {})
        
        if source_mapping == target_mapping:
            print(f"✅ {league}: Team mappings already correct")
            continue
        
        print(f"🔧 {league}: Fixing team name mappings...")
        
        if dry_run:
            print(f"   Would update {len(source_mapping)} team mappings")
            # Show differences
            for team, correct_mapping in source_mapping.items():
                current_mapping = target_mapping.get(team, "NOT_FOUND")
                if current_mapping != correct_mapping:
                    print(f"     '{team}': '{current_mapping}' -> '{correct_mapping}'")
        else:
            # Update the target configuration with correct mappings
            target_config[league]["TEAM_NAME_MAPPING"] = source_mapping
            
            # Save the updated configuration
            if save_config_to_py_file(target_file, target_config, league):
                print(f"   ✅ Updated {len(source_mapping)} team mappings")
                fixed_count += 1
            else:
                print(f"   ❌ Failed to save updated config")
                error_count += 1
    
    print(f"\nSummary:")
    print(f"  Fixed: {fixed_count} leagues")
    print(f"  Errors: {error_count} leagues")
    
    return error_count == 0

def apply_team_mappings_to_data(league_name, dry_run=False):
    """
    Apply team name mappings to the actual data files (team_stats.csv).
    This fixes the issue where team_stats has different team names than results.
    """
    import pandas as pd

    # Load the correct team name mapping
    team_names_mapped_dir = Path("src/scrapers/team_names_mapped")
    source_file = team_names_mapped_dir / f"{league_name}.py"

    if not source_file.exists():
        print(f"⚠️  Source mapping file not found: {source_file}")
        return False

    # Load source configuration (correct mappings)
    source_config = load_config_from_py_file(source_file)
    if not source_config or league_name not in source_config:
        print(f"⚠️  Could not load source config for {league_name}")
        return False

    team_name_mapping = source_config[league_name].get("TEAM_NAME_MAPPING", {})
    if not team_name_mapping:
        print(f"⚠️  No team name mapping found for {league_name}")
        return False

    # Load and update team_stats file
    team_stats_file = Path(f"data/raw/{league_name}/{league_name}_team_stats.csv")
    if not team_stats_file.exists():
        print(f"⚠️  Team stats file not found: {team_stats_file}")
        return False

    try:
        team_stats = pd.read_csv(team_stats_file)
        if 'Team' not in team_stats.columns:
            print(f"⚠️  'Team' column not found in {team_stats_file}")
            return False

        # Apply team name mappings
        original_teams = team_stats['Team'].tolist()
        updated_teams = []
        changes_made = 0

        for team in original_teams:
            if team in team_name_mapping:
                mapped_team = team_name_mapping[team]
                updated_teams.append(mapped_team)
                if team != mapped_team:
                    changes_made += 1
                    if dry_run:
                        print(f"     Would map: '{team}' -> '{mapped_team}'")
            else:
                updated_teams.append(team)

        if changes_made == 0:
            print(f"✅ {league_name}: Team names in data files already correct")
            return True

        if dry_run:
            print(f"   Would update {changes_made} team names in data file")
            return True

        # Update the team names
        team_stats['Team'] = updated_teams
        team_stats.to_csv(team_stats_file, index=False)
        print(f"   ✅ Updated {changes_made} team names in data file")
        return True

    except Exception as e:
        print(f"❌ Error processing {team_stats_file}: {e}")
        return False

def main():
    """Main function to handle command line arguments."""
    import argparse

    parser = argparse.ArgumentParser(description="Fix team name mappings in league config files and data files")
    parser.add_argument("--league", help="Specific league to fix (e.g., ITALY_SERIE_A)")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be changed without making changes")
    parser.add_argument("--all", action="store_true", help="Process all leagues")
    parser.add_argument("--data-only", action="store_true", help="Only fix data files, not config files")
    parser.add_argument("--config-only", action="store_true", help="Only fix config files, not data files")

    args = parser.parse_args()

    if not args.league and not args.all:
        print("Error: Must specify either --league LEAGUE_NAME or --all")
        sys.exit(1)

    league_name = args.league if args.league else None

    if args.dry_run:
        print("DRY RUN MODE - No changes will be made")
        print("=" * 50)

    success = True

    # Fix config files unless --data-only is specified
    if not args.data_only:
        print("🔧 Fixing team name mappings in config files...")
        success = fix_team_mappings(league_name, args.dry_run) and success

    # Fix data files unless --config-only is specified
    if not args.config_only:
        print("🔧 Applying team name mappings to data files...")
        if league_name:
            success = apply_team_mappings_to_data(league_name, args.dry_run) and success
        else:
            # Process all leagues
            team_names_mapped_dir = Path("src/scrapers/team_names_mapped")
            leagues_to_process = []
            for file_path in team_names_mapped_dir.glob("*.py"):
                if not file_path.name.endswith('.bak'):
                    leagues_to_process.append(file_path.stem)

            for league in leagues_to_process:
                success = apply_team_mappings_to_data(league, args.dry_run) and success

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
