#!/usr/bin/env python3
"""
Simple Database Verification Script

Verifies that the SQLite database was created successfully and contains data.
"""

import sys
import os
sys.path.append('src')

from database.football_db import FootballDatabase, get_database
import pandas as pd

def main():
    print("🔍 Database Verification")
    print("=" * 30)
    
    # Check if database exists
    db_path = 'data/football_betting.db'
    if not os.path.exists(db_path):
        print("❌ Database file not found!")
        return
    
    print(f"✅ Database file found: {db_path}")
    print(f"📁 Database size: {os.path.getsize(db_path) / (1024*1024):.1f} MB")
    
    try:
        with get_database() as db:
            # Get database summary
            summary = db.get_database_summary()
            
            print(f"\n📊 Database Contents:")
            print(f"   • Leagues: {summary['leagues']:,}")
            print(f"   • Teams: {summary['teams']:,}")
            print(f"   • Team Statistics: {summary['team_stats']:,}")
            print(f"   • Match Results: {summary['match_results']:,}")
            print(f"   • Head-to-Head Records: {summary['head_to_head_stats']:,}")
            print(f"   • League Tables: {summary['league_table']:,}")
            print(f"   • League Statistics: {summary['league_stats']:,}")
            
            # Test a simple query
            leagues = db.get_leagues()
            print(f"\n🏈 Sample Leagues (first 5):")
            for i, league in enumerate(leagues.head()['league_name'], 1):
                print(f"   {i}. {league}")
            
            # Test league-specific data
            test_league = "ENGLAND_PREMIER_LEAGUE"
            teams = db.get_teams(test_league)
            if not teams.empty:
                print(f"\n⚽ {test_league} Teams ({len(teams)} total):")
                for team in teams.head()['team_name']:
                    print(f"   • {team}")
            
            # Test match results
            matches = db.get_match_results(test_league, limit=3)
            if not matches.empty:
                print(f"\n🏆 Recent {test_league} Matches:")
                for _, match in matches.iterrows():
                    home_score = match['home_score'] if pd.notna(match['home_score']) else '?'
                    away_score = match['away_score'] if pd.notna(match['away_score']) else '?'
                    print(f"   • {match['home_team']} {home_score}-{away_score} {match['away_team']}")
            
            print(f"\n✅ Database verification successful!")
            print(f"🎯 The database is ready to use and contains comprehensive football data.")
            
    except Exception as e:
        print(f"❌ Database verification failed: {e}")

if __name__ == "__main__":
    main()