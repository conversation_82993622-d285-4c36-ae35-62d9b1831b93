#!/usr/bin/env python3
"""
🔬 QUICK COMPONENT TESTS
=======================

Demonstrates fast component-level testing for the football prediction system.
This script shows how to test individual components without running the full pipeline.

Usage:
    python quick_component_tests.py --component data
    python quick_component_tests.py --component features
    python quick_component_tests.py --component models
    python quick_component_tests.py --component all
"""

import sys
import os
import time
import logging
from typing import Dict, List, Optional

# Add src to path
sys.path.append('src')

def setup_logging():
    """Setup basic logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_data_loading(league_name: str = "ENGLAND_PREMIER_LEAGUE") -> Dict:
    """Test data loading component only (~5 seconds)."""
    logger = logging.getLogger(__name__)
    start_time = time.time()

    try:
        logger.info(f"🔍 Testing data loading for {league_name}")

        from data_loading import load_data
        from scrapers.config import LEAGUE_CONFIGS

        # Get league config
        league_config = LEAGUE_CONFIGS.get(league_name, {})
        data, config = load_data(league_name, league_config)

        if data is None:
            return {"status": "failed", "error": "Data loading returned None", "duration": time.time() - start_time}

        results, team_stats, league_stats, h2h_stats, league_table = data

        # Quick validation
        issues = []
        if results is None or len(results) == 0:
            issues.append("No match results")
        if team_stats is None or len(team_stats) == 0:
            issues.append("No team stats")
        if league_stats is None or len(league_stats) == 0:
            issues.append("No league stats")

        return {
            "status": "success" if not issues else "warning",
            "duration": time.time() - start_time,
            "data_shapes": {
                "results": results.shape if results is not None else None,
                "team_stats": team_stats.shape if team_stats is not None else None,
                "league_stats": league_stats.shape if league_stats is not None else None,
                "h2h_stats": h2h_stats.shape if h2h_stats is not None else None,
                "league_table": league_table.shape if league_table is not None else None
            },
            "issues": issues
        }

    except Exception as e:
        return {
            "status": "failed",
            "error": str(e),
            "duration": time.time() - start_time
        }

def test_feature_engineering(league_name: str = "ENGLAND_PREMIER_LEAGUE", sample_size: int = 50) -> Dict:
    """Test feature engineering component only (~15 seconds)."""
    logger = logging.getLogger(__name__)
    start_time = time.time()

    try:
        logger.info(f"🔧 Testing feature engineering for {league_name} (sample: {sample_size})")

        from data_loading import load_data
        from feature_engineering import prepare_features
        from scrapers.config import LEAGUE_CONFIGS

        # Load data
        league_config = LEAGUE_CONFIGS.get(league_name, {})
        data, config = load_data(league_name, league_config)
        if data is None:
            return {"status": "failed", "error": "Data loading failed", "duration": time.time() - start_time}

        results, team_stats, league_stats, h2h_stats, league_table = data

        # Use sample for faster testing
        sample_results = results.head(sample_size) if len(results) > sample_size else results

        # Test feature preparation
        prepared_data = prepare_features(
            sample_results, team_stats, league_stats, h2h_stats, league_table,
            league_config.get('COLUMN_MAPPING', {}),
            league_config.get('TEAM_NAME_MAPPING', {})
        )

        if prepared_data is None:
            return {"status": "failed", "error": "Feature preparation failed", "duration": time.time() - start_time}

        # prepared_data is a DataFrame, not a tuple

        # Validate features
        issues = []
        nan_count = prepared_data.isnull().sum().sum()
        if nan_count > 0:
            issues.append(f"Found {nan_count} NaN values in features")

        # Check for target columns
        target_cols = ['three_way', 'over_under_2_5', 'btts']
        available_targets = [col for col in target_cols if col in prepared_data.columns]

        if len(available_targets) == 0:
            issues.append("No target variables found in prepared data")

        return {
            "status": "success" if not issues else "warning",
            "duration": time.time() - start_time,
            "feature_info": {
                "feature_shape": prepared_data.shape,
                "target_variables": available_targets,
                "nan_count": nan_count,
                "sample_size": len(sample_results)
            },
            "issues": issues
        }

    except Exception as e:
        return {
            "status": "failed",
            "error": str(e),
            "duration": time.time() - start_time
        }

def test_model_training(league_name: str = "ENGLAND_PREMIER_LEAGUE", sample_size: int = 100) -> Dict:
    """Test model training component only (~30 seconds)."""
    logger = logging.getLogger(__name__)
    start_time = time.time()

    try:
        logger.info(f"🤖 Testing model training for {league_name} (sample: {sample_size})")

        from data_loading import load_data
        from feature_engineering import prepare_features
        from model_training import train_model
        from scrapers.config import LEAGUE_CONFIGS

        # Load and prepare data
        league_config = LEAGUE_CONFIGS.get(league_name, {})
        data, config = load_data(league_name, league_config)
        if data is None:
            return {"status": "failed", "error": "Data loading failed", "duration": time.time() - start_time}

        results, team_stats, league_stats, h2h_stats, league_table = data
        sample_results = results.head(sample_size) if len(results) > sample_size else results

        prepared_data = prepare_features(
            sample_results, team_stats, league_stats, h2h_stats, league_table,
            league_config.get('COLUMN_MAPPING', {}),
            league_config.get('TEAM_NAME_MAPPING', {})
        )

        if prepared_data is None:
            return {"status": "failed", "error": "Feature preparation failed", "duration": time.time() - start_time}

        # Prepare data for model training (prepared_data is a DataFrame)
        target_cols = ['three_way', 'over_under_2_5', 'btts']
        available_targets = [col for col in target_cols if col in prepared_data.columns]

        if not available_targets:
            return {"status": "failed", "error": "No target variables found", "duration": time.time() - start_time}

        # Split features and targets - exclude all target-related columns
        all_target_cols = [
            'three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts',
            'three_way_encoded', 'over_under_1_5_encoded', 'over_under_2_5_encoded',
            'over_under_3_5_encoded', 'btts_encoded', 'result', 'home_goals', 'away_goals', 'total_goals'
        ]
        X = prepared_data.drop(all_target_cols, axis=1, errors='ignore')
        y_dict = {col: prepared_data[col] for col in available_targets}

        # Train models (train_model expects empty dict for label_encoders, it creates them internally)
        models = train_model(X, y_dict, {})

        if not models:
            return {"status": "failed", "error": "Model training failed", "duration": time.time() - start_time}

        return {
            "status": "success",
            "duration": time.time() - start_time,
            "model_info": {
                "models_trained": list(models.keys()),
                "training_samples": len(sample_results),
                "feature_count": X.shape[1]
            },
            "issues": []
        }

    except Exception as e:
        return {
            "status": "failed",
            "error": str(e),
            "duration": time.time() - start_time
        }

def run_component_tests(component: str = "all", league_name: str = "ENGLAND_PREMIER_LEAGUE"):
    """Run component tests based on selection."""
    logger = setup_logging()

    logger.info(f"🚀 Starting component tests for {league_name}")

    results = {}
    total_start = time.time()

    if component in ["all", "data"]:
        logger.info("=" * 50)
        results["data_loading"] = test_data_loading(league_name)

    if component in ["all", "features"]:
        logger.info("=" * 50)
        results["feature_engineering"] = test_feature_engineering(league_name)

    if component in ["all", "models"]:
        logger.info("=" * 50)
        results["model_training"] = test_model_training(league_name)

    total_duration = time.time() - total_start

    # Print summary
    logger.info("=" * 50)
    logger.info("🎉 COMPONENT TEST SUMMARY")
    logger.info("=" * 50)

    for component_name, result in results.items():
        status_emoji = "✅" if result["status"] == "success" else "⚠️" if result["status"] == "warning" else "❌"
        logger.info(f"{status_emoji} {component_name}: {result['status']} ({result['duration']:.1f}s)")

        if result.get("issues"):
            for issue in result["issues"]:
                logger.warning(f"  ⚠️ {issue}")

        if result.get("error"):
            logger.error(f"  ❌ {result['error']}")

    logger.info(f"\n⏱️ Total Duration: {total_duration:.1f} seconds")

    return results

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Quick Component Tests")
    parser.add_argument('--component', choices=['data', 'features', 'models', 'all'], default='all',
                       help='Component to test')
    parser.add_argument('--league', default='ENGLAND_PREMIER_LEAGUE',
                       help='League to test')

    args = parser.parse_args()

    run_component_tests(args.component, args.league)