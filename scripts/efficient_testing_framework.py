#!/usr/bin/env python3
"""
🚀 EFFICIENT TESTING FRAMEWORK FOR FOOTBALL PREDICTIONS
======================================================

This framework provides multiple testing levels to efficiently validate
the prediction system across 314 leagues without exhaustive testing.

Testing Levels:
1. SMOKE: Quick data validation (10 sec/league)
2. INTEGRATION: Feature + basic model test (1 min/league)
3. FULL: Complete pipeline test (4 min/league)

Usage:
    python efficient_testing_framework.py --level smoke --sample 50
    python efficient_testing_framework.py --level integration --tier 1
    python efficient_testing_framework.py --level full --leagues EPL,LALIGA
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import json
import argparse
from typing import Dict, List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
from dataclasses import dataclass
from enum import Enum

# Add src to path
sys.path.append('src')

class TestLevel(Enum):
    SMOKE = "smoke"
    INTEGRATION = "integration"
    FULL = "full"

class LeagueTier(Enum):
    TIER_1 = "tier_1"  # Major leagues
    TIER_2 = "tier_2"  # Regional representatives
    TIER_3 = "tier_3"  # Random sample
    ALL = "all"

@dataclass
class TestResult:
    league_name: str
    test_level: TestLevel
    status: str  # success, failed, skipped
    duration: float
    issues: List[str]
    details: Dict

class EfficientTester:
    """Efficient testing framework for football prediction system."""

    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.setup_logging()
        self.league_tiers = self.define_league_tiers()

    def setup_logging(self):
        """Setup logging configuration."""
        log_filename = f"efficient_testing_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"🚀 Efficient Testing Framework Started")
        self.logger.info(f"📄 Log file: {log_filename}")

    def define_league_tiers(self) -> Dict[LeagueTier, List[str]]:
        """Define league tiers for sampling strategy."""
        return {
            LeagueTier.TIER_1: [
                "ENGLAND_PREMIER_LEAGUE",
                "SPAIN_LA_LIGA",
                "GERMANY_BUNDESLIGA",
                "ITALY_SERIE_A",
                "FRANCE_LIGUE_1",
                "NETHERLANDS_EREDIVISIE",
                "PORTUGAL_LIGA_NOS",
                "BRAZIL_SERIE_A",
                "ARGENTINA_PRIMERA_DIVISION",
                "USA_MAJOR_LEAGUE_SOCCER"
            ],
            LeagueTier.TIER_2: [
                "ENGLAND_CHAMPIONSHIP",
                "SPAIN_SEGUNDA_DIVISION",
                "GERMANY_BUNDESLIGA_2",
                "ITALY_SERIE_B",
                "FRANCE_LIGUE_2",
                "SCOTLAND_PREMIERSHIP",
                "BELGIUM_FIRST_DIVISION_A",
                "AUSTRIA_BUNDESLIGA",
                "SWITZERLAND_SUPER_LEAGUE",
                "TURKEY_SUPER_LIG",
                "RUSSIA_PREMIER_LEAGUE",
                "POLAND_EKSTRAKLASA",
                "CZECH_REPUBLIC_FIRST_LEAGUE",
                "CROATIA_PRVA_HNL",
                "SERBIA_SUPERLIGA",
                "JAPAN_J1_LEAGUE",
                "SOUTH_KOREA_K_LEAGUE_1",
                "AUSTRALIA_A_LEAGUE",
                "MEXICO_LIGA_MX",
                "COLOMBIA_PRIMERA_A"
            ]
        }

    def get_available_leagues(self) -> List[str]:
        """Get list of available leagues."""
        try:
            from data_loading import get_available_leagues
            return get_available_leagues()
        except Exception as e:
            self.logger.error(f"Error getting available leagues: {e}")
            return []

    def sample_leagues(self, tier: LeagueTier, sample_size: Optional[int] = None) -> List[str]:
        """Sample leagues based on tier and size."""
        available_leagues = self.get_available_leagues()

        if tier == LeagueTier.ALL:
            leagues = available_leagues
        elif tier in self.league_tiers:
            # Filter available leagues by tier
            tier_leagues = self.league_tiers[tier]
            leagues = [l for l in tier_leagues if l in available_leagues]
        else:
            # Tier 3: Random sample from remaining leagues
            tier_1_2 = set(self.league_tiers[LeagueTier.TIER_1] + self.league_tiers[LeagueTier.TIER_2])
            remaining = [l for l in available_leagues if l not in tier_1_2]
            leagues = remaining

        if sample_size and len(leagues) > sample_size:
            import random
            random.seed(42)  # Reproducible sampling
            leagues = random.sample(leagues, sample_size)

        self.logger.info(f"Selected {len(leagues)} leagues for {tier.value}")
        return leagues

    def smoke_test_league(self, league_name: str) -> TestResult:
        """Quick data validation test (~10 seconds)."""
        start_time = time.time()
        result = TestResult(
            league_name=league_name,
            test_level=TestLevel.SMOKE,
            status="success",
            duration=0,
            issues=[],
            details={}
        )

        try:
            self.logger.info(f"🔍 Smoke testing {league_name}")

            # Test 1: Check if data files exist
            from data_loading.utils import RAW_DATA_DIR
            league_dir = os.path.join(RAW_DATA_DIR, league_name)

            if not os.path.exists(league_dir):
                result.status = "failed"
                result.issues.append("League directory not found")
                return result

            required_files = [
                f"{league_name}_results.csv",
                f"{league_name}_team_stats.csv",
                f"{league_name}_league_stats.csv",
                f"{league_name}_head_to_head_stats.csv",
                f"{league_name}_league_table.csv"
            ]

            missing_files = []
            for file in required_files:
                if not os.path.exists(os.path.join(league_dir, file)):
                    missing_files.append(file)

            if missing_files:
                result.status = "failed"
                result.issues.append(f"Missing files: {missing_files}")
                return result

            # Test 2: Basic data validation
            try:
                from data_loading import load_data
                from scrapers.config import LEAGUE_CONFIGS

                league_config = LEAGUE_CONFIGS.get(league_name, {})
                data, config = load_data(league_name, league_config)

                if data is None:
                    result.status = "failed"
                    result.issues.append("Data loading failed")
                    return result

                results, team_stats, league_stats, h2h_stats, league_table = data

                # Check data shapes
                result.details = {
                    "results_shape": results.shape if results is not None else None,
                    "team_stats_shape": team_stats.shape if team_stats is not None else None,
                    "league_stats_shape": league_stats.shape if league_stats is not None else None,
                    "h2h_stats_shape": h2h_stats.shape if h2h_stats is not None else None,
                    "league_table_shape": league_table.shape if league_table is not None else None
                }

                # Basic validation
                if results is None or len(results) == 0:
                    result.issues.append("No match results data")
                if team_stats is None or len(team_stats) == 0:
                    result.issues.append("No team stats data")

            except Exception as e:
                result.status = "failed"
                result.issues.append(f"Data loading error: {str(e)}")

        except Exception as e:
            result.status = "failed"
            result.issues.append(f"Smoke test error: {str(e)}")

        result.duration = time.time() - start_time
        return result

    def integration_test_league(self, league_name: str) -> TestResult:
        """Integration test with feature engineering (~1 minute)."""
        start_time = time.time()
        result = TestResult(
            league_name=league_name,
            test_level=TestLevel.INTEGRATION,
            status="success",
            duration=0,
            issues=[],
            details={}
        )

        try:
            self.logger.info(f"🔧 Integration testing {league_name}")

            # First run smoke test
            smoke_result = self.smoke_test_league(league_name)
            if smoke_result.status != "success":
                result.status = "failed"
                result.issues.extend(smoke_result.issues)
                return result

            # Test feature engineering
            from data_loading import load_data
            from feature_engineering import prepare_features
            from scrapers.config import LEAGUE_CONFIGS

            league_config = LEAGUE_CONFIGS.get(league_name, {})
            data, config = load_data(league_name, league_config)
            results, team_stats, league_stats, h2h_stats, league_table = data

            # Prepare features on sample data (first 100 matches)
            sample_results = results.head(100) if len(results) > 100 else results

            prepared_data = prepare_features(
                sample_results, team_stats, league_stats, h2h_stats, league_table,
                league_config.get('COLUMN_MAPPING', {}),
                league_config.get('TEAM_NAME_MAPPING', {})
            )

            if prepared_data is None:
                result.status = "failed"
                result.issues.append("Feature preparation failed")
                return result

            # Validate features (prepared_data is a DataFrame)
            target_cols = ['three_way', 'over_under_2_5', 'btts']
            available_targets = [col for col in target_cols if col in prepared_data.columns]

            result.details = {
                "feature_shape": prepared_data.shape,
                "target_variables": available_targets,
                "nan_features": prepared_data.isnull().sum().sum(),
                "sample_size": len(sample_results)
            }

            # Check for issues
            if prepared_data.isnull().sum().sum() > 0:
                result.issues.append("NaN values in features")

            if len(available_targets) == 0:
                result.status = "failed"
                result.issues.append("No target variables created")

        except Exception as e:
            result.status = "failed"
            result.issues.append(f"Integration test error: {str(e)}")

        result.duration = time.time() - start_time
        return result

    def full_test_league(self, league_name: str) -> TestResult:
        """Full pipeline test (~4 minutes)."""
        start_time = time.time()
        result = TestResult(
            league_name=league_name,
            test_level=TestLevel.FULL,
            status="success",
            duration=0,
            issues=[],
            details={}
        )

        try:
            self.logger.info(f"🎯 Full testing {league_name}")

            # Run integration test first
            integration_result = self.integration_test_league(league_name)
            if integration_result.status != "success":
                result.status = "failed"
                result.issues.extend(integration_result.issues)
                return result

            # Test full pipeline with sample matches
            from data_loading import load_data
            from feature_engineering import prepare_features
            from model_training import train_model
            from prediction import predict_match
            from scrapers.config import LEAGUE_CONFIGS

            league_config = LEAGUE_CONFIGS.get(league_name, {})
            data, config = load_data(league_name, league_config)
            results, team_stats, league_stats, h2h_stats, league_table = data

            # Use sample for training (first 200 matches)
            sample_results = results.head(200) if len(results) > 200 else results

            prepared_data = prepare_features(
                sample_results, team_stats, league_stats, h2h_stats, league_table,
                league_config.get('COLUMN_MAPPING', {}),
                league_config.get('TEAM_NAME_MAPPING', {})
            )

            # Prepare data for model training
            target_cols = ['three_way', 'over_under_2_5', 'btts']
            encoded_target_cols = [f'{col}_encoded' for col in target_cols]

            # Check for encoded target columns
            available_encoded_targets = [col for col in encoded_target_cols if col in prepared_data.columns]

            if not available_encoded_targets:
                result.status = "failed"
                result.issues.append("No encoded target variables found")
                return result

            # Drop ALL target-related columns from features (including all over_under variants)
            all_target_cols = [
                'three_way', 'over_under_1_5', 'over_under_2_5', 'over_under_3_5', 'btts',
                'three_way_encoded', 'over_under_1_5_encoded', 'over_under_2_5_encoded',
                'over_under_3_5_encoded', 'btts_encoded'
            ]
            X = prepared_data.drop(all_target_cols, axis=1, errors='ignore')

            # Use encoded targets for training
            y_dict = {}
            for encoded_col in available_encoded_targets:
                original_col = encoded_col.replace('_encoded', '')
                y_dict[original_col] = prepared_data[encoded_col]

            # Extract label encoders from prepared data
            label_encoders = prepared_data.attrs.get('label_encoders', {})

            # Train models
            models = train_model(X, y_dict, label_encoders)

            if not models:
                result.status = "failed"
                result.issues.append("Model training failed")
                return result

            # Test prediction on one match
            if len(team_stats) >= 2:
                home_team = team_stats.iloc[0]["Team"]
                away_team = team_stats.iloc[1]["Team"]

                # Test prediction
                pred_results, error_message, correct_scores = predict_match(
                    models, home_team, away_team, team_stats, league_stats,
                    h2h_stats, league_table, config.get("TEAM_NAME_MAPPING", {}),
                    models["three_way"]["feature_names"], 2.5, label_encoders
                )

                if error_message:
                    result.issues.append(f"Prediction error: {error_message}")
                else:
                    result.details["prediction_success"] = True
                    result.details["predicted_match"] = f"{home_team} vs {away_team}"

            result.details.update({
                "models_trained": list(models.keys()),
                "training_samples": len(sample_results)
            })

        except Exception as e:
            result.status = "failed"
            result.issues.append(f"Full test error: {str(e)}")

        result.duration = time.time() - start_time
        return result

    def run_test_batch(self, leagues: List[str], test_level: TestLevel) -> List[TestResult]:
        """Run tests on a batch of leagues with parallel execution."""
        results = []

        # Determine max workers based on test level
        if test_level == TestLevel.SMOKE:
            max_workers = min(self.max_workers * 2, 8)  # More parallel for quick tests
        elif test_level == TestLevel.INTEGRATION:
            max_workers = self.max_workers
        else:  # FULL
            max_workers = max(min(self.max_workers // 2, 2), 1)  # Less parallel for heavy tests, but at least 1

        self.logger.info(f"Running {test_level.value} tests on {len(leagues)} leagues with {max_workers} workers")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_league = {}
            for league in leagues:
                if test_level == TestLevel.SMOKE:
                    future = executor.submit(self.smoke_test_league, league)
                elif test_level == TestLevel.INTEGRATION:
                    future = executor.submit(self.integration_test_league, league)
                else:  # FULL
                    future = executor.submit(self.full_test_league, league)
                future_to_league[future] = league

            # Collect results as they complete
            for future in as_completed(future_to_league):
                league = future_to_league[future]
                try:
                    result = future.result()
                    results.append(result)

                    status_emoji = "✅" if result.status == "success" else "❌"
                    self.logger.info(f"{status_emoji} {league}: {result.status} ({result.duration:.1f}s)")

                    if result.issues:
                        for issue in result.issues:
                            self.logger.warning(f"  ⚠️ {issue}")

                except Exception as e:
                    self.logger.error(f"❌ {league}: Exception during test - {str(e)}")
                    results.append(TestResult(
                        league_name=league,
                        test_level=test_level,
                        status="failed",
                        duration=0,
                        issues=[f"Exception: {str(e)}"],
                        details={}
                    ))

        return results

    def generate_report(self, results: List[TestResult]) -> Dict:
        """Generate comprehensive test report."""
        total_tests = len(results)
        successful_tests = len([r for r in results if r.status == "success"])
        failed_tests = len([r for r in results if r.status == "failed"])

        total_duration = sum(r.duration for r in results)
        avg_duration = total_duration / total_tests if total_tests > 0 else 0

        # Group by test level
        by_level = {}
        for result in results:
            level = result.test_level.value
            if level not in by_level:
                by_level[level] = {"success": 0, "failed": 0, "total_duration": 0}

            by_level[level][result.status] += 1
            by_level[level]["total_duration"] += result.duration

        # Common issues
        all_issues = []
        for result in results:
            all_issues.extend(result.issues)

        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1

        common_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:10]

        report = {
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
                "total_duration": total_duration,
                "average_duration": avg_duration
            },
            "by_test_level": by_level,
            "common_issues": common_issues,
            "failed_leagues": [r.league_name for r in results if r.status == "failed"],
            "timestamp": datetime.now().isoformat()
        }

        return report

    def save_results(self, results: List[TestResult], report: Dict):
        """Save test results and report to files."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save detailed results
        results_file = f"test_results_{timestamp}.json"
        results_data = []
        for result in results:
            # Convert numpy types to native Python types for JSON serialization
            details = {}
            for key, value in result.details.items():
                if hasattr(value, 'item'):  # numpy scalar
                    details[key] = value.item()
                elif hasattr(value, 'tolist'):  # numpy array
                    details[key] = value.tolist()
                else:
                    details[key] = value

            results_data.append({
                "league_name": result.league_name,
                "test_level": result.test_level.value,
                "status": result.status,
                "duration": float(result.duration),
                "issues": result.issues,
                "details": details
            })

        with open(results_file, 'w') as f:
            json.dump(results_data, f, indent=2)

        # Save report
        report_file = f"test_report_{timestamp}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        # Save summary to markdown
        summary_file = f"test_summary_{timestamp}.md"
        with open(summary_file, 'w') as f:
            f.write(f"# Testing Report - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"## Summary\n")
            f.write(f"- **Total Tests**: {report['summary']['total_tests']}\n")
            f.write(f"- **Success Rate**: {report['summary']['success_rate']:.1%}\n")
            f.write(f"- **Total Duration**: {report['summary']['total_duration']:.1f} seconds\n")
            f.write(f"- **Average Duration**: {report['summary']['average_duration']:.1f} seconds\n\n")

            if report['common_issues']:
                f.write(f"## Common Issues\n")
                for issue, count in report['common_issues']:
                    f.write(f"- **{issue}**: {count} occurrences\n")
                f.write("\n")

            if report['failed_leagues']:
                f.write(f"## Failed Leagues\n")
                for league in report['failed_leagues']:
                    f.write(f"- {league}\n")

        self.logger.info(f"📄 Results saved to: {results_file}")
        self.logger.info(f"📊 Report saved to: {report_file}")
        self.logger.info(f"📝 Summary saved to: {summary_file}")


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description="Efficient Testing Framework for Football Predictions")
    parser.add_argument('--level', choices=['smoke', 'integration', 'full'], default='smoke',
                       help='Test level to run')
    parser.add_argument('--tier', choices=['tier_1', 'tier_2', 'tier_3', 'all'], default='tier_1',
                       help='League tier to test')
    parser.add_argument('--sample', type=int, help='Sample size (random selection from tier)')
    parser.add_argument('--leagues', type=str, help='Specific leagues to test (comma-separated)')
    parser.add_argument('--workers', type=int, default=4, help='Number of parallel workers')

    args = parser.parse_args()

    # Create tester
    tester = EfficientTester(max_workers=args.workers)

    # Determine leagues to test
    if args.leagues:
        leagues = [l.strip() for l in args.leagues.split(',')]
        tester.logger.info(f"Testing specific leagues: {leagues}")
    else:
        tier = LeagueTier(args.tier)
        leagues = tester.sample_leagues(tier, args.sample)

    if not leagues:
        tester.logger.error("No leagues to test!")
        return

    # Run tests
    test_level = TestLevel(args.level)
    tester.logger.info(f"🚀 Starting {test_level.value} tests on {len(leagues)} leagues")

    start_time = time.time()
    results = tester.run_test_batch(leagues, test_level)
    total_time = time.time() - start_time

    # Generate and save report
    report = tester.generate_report(results)
    tester.save_results(results, report)

    # Print summary
    print(f"\n🎉 Testing Complete!")
    print(f"📊 Results: {report['summary']['successful_tests']}/{report['summary']['total_tests']} successful")
    print(f"⏱️ Total Time: {total_time:.1f} seconds")
    print(f"📈 Success Rate: {report['summary']['success_rate']:.1%}")

    if report['failed_leagues']:
        print(f"\n❌ Failed Leagues ({len(report['failed_leagues'])}):")
        for league in report['failed_leagues'][:10]:  # Show first 10
            print(f"  - {league}")
        if len(report['failed_leagues']) > 10:
            print(f"  ... and {len(report['failed_leagues']) - 10} more")


if __name__ == "__main__":
    main()