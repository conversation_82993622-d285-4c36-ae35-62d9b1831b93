#!/usr/bin/env python3
"""
Root Repository Cleanup Script

This script removes redundant, obsolete, and temporary files from the root directory
while preserving essential files and reusable utilities.
"""

import os
import sys
from pathlib import Path

# Files to delete (confirmed as safe to remove)
FILES_TO_DELETE = [
    # One-time utility scripts (completed/obsolete)
    "backup_h2h_files.py",
    "check_h2h_leagues.py", 
    "delete_h2h_files.py",
    "fix_h2h_migration.py",
    "migrate_data_to_db.py",
    "process_missing_urls.py",
    "refactor_h2h_url_keys.py",
    "remove_duplicate_configs.py",
    "restore_team_mappings.py",
    "team_url_extractor.py",
    "test_database.py",
    
    # Backup files
    "web_scraping.py.bak",
    
    # Temporary files
    "tmp_code_1de700c29687cae34561545f50d3c8b3d9afe88e04cc11069f8a6dc6e4ce9464.bash",
    
    # Large log files
    "automation.log",  # 665MB!
    "h2h_automation.log",
    "h2h_automation_runner.log", 
    "h2h_delete.log",
    "pytest_output.log",
    
    # Outdated documentation
    "crawl_docs.md",
    "custom-instructions.md",
]

# Files to KEEP (for reference - these won't be deleted)
FILES_TO_KEEP = [
    # Essential files
    ".gitignore",
    "README.md", 
    "requirements.txt",
    "run_h2h_automation.sh",
    "web_scraping.py",
    
    # Reusable config utilities (as requested)
    "format_config_dictionaries.py",
    "overwrite_config.py", 
    "remove_match_ids.py",
    "team_name_mapper.py",
    
    # Important documentation
    "H2H_AUTOMATION_FIXES.md",
    "H2H_BATCH_SCRAPING.md", 
    "H2H_ERROR_HANDLING.md",
    "H2H_IMPROVEMENT_PLAN.md",
    "IMPLEMENTATION_SUMMARY.md",
    "how_to_use.md",
    "scraping_features.md",
]

def get_file_size(filepath):
    """Get human-readable file size."""
    try:
        size = os.path.getsize(filepath)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    except OSError:
        return "Unknown"

def list_files_to_delete():
    """List all files that will be deleted with their sizes."""
    print("📋 Files scheduled for deletion:")
    print("=" * 60)
    
    total_size = 0
    existing_files = []
    
    for filename in FILES_TO_DELETE:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            total_size += size
            size_str = get_file_size(filename)
            print(f"  ✓ {filename:<35} ({size_str})")
            existing_files.append(filename)
        else:
            print(f"  ⚠ {filename:<35} (not found)")
    
    print("=" * 60)
    print(f"📊 Total files to delete: {len(existing_files)}")
    print(f"💾 Total space to free: {get_file_size('dummy') if total_size == 0 else get_file_size('dummy').replace(str(os.path.getsize('dummy') if os.path.exists('dummy') else 0), str(total_size))}")
    
    # Calculate total size properly
    total_size_str = "0 B"
    if total_size > 0:
        for unit in ['B', 'KB', 'MB', 'GB']:
            if total_size < 1024.0:
                total_size_str = f"{total_size:.1f} {unit}"
                break
            total_size /= 1024.0
        else:
            total_size_str = f"{total_size:.1f} TB"
    
    print(f"💾 Total space to free: {total_size_str}")
    return existing_files

def delete_files(files_to_delete, dry_run=False):
    """Delete the specified files."""
    deleted_count = 0
    failed_count = 0
    
    for filename in files_to_delete:
        try:
            if dry_run:
                print(f"  [DRY RUN] Would delete: {filename}")
            else:
                os.remove(filename)
                print(f"  ✓ Deleted: {filename}")
            deleted_count += 1
        except OSError as e:
            print(f"  ✗ Failed to delete {filename}: {e}")
            failed_count += 1
    
    return deleted_count, failed_count

def verify_safety():
    """Verify that essential files won't be accidentally deleted."""
    print("🔍 Safety check - verifying essential files are protected:")
    print("-" * 50)
    
    all_safe = True
    for essential_file in FILES_TO_KEEP:
        if essential_file in FILES_TO_DELETE:
            print(f"  ⚠️  WARNING: {essential_file} is marked for deletion but should be kept!")
            all_safe = False
        else:
            if os.path.exists(essential_file):
                print(f"  ✓ Protected: {essential_file}")
            else:
                print(f"  ⚠ Protected (not found): {essential_file}")
    
    return all_safe

def main():
    """Main cleanup function."""
    print("🧹 Root Repository Cleanup Script")
    print("=" * 60)
    print()
    
    # Safety check
    if not verify_safety():
        print("\n❌ Safety check failed! Please review the file lists.")
        return 1
    
    print("\n✅ Safety check passed!")
    print()
    
    # List files to delete
    existing_files = list_files_to_delete()
    
    if not existing_files:
        print("\n✅ No files need to be deleted!")
        return 0
    
    print()
    print("🔄 This will permanently delete the files listed above.")
    print("💡 Essential files and reusable utilities are protected.")
    print()
    
    # Confirm deletion
    response = input("Proceed with deletion? (y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        print("\n🗑️  Deleting files...")
        print("-" * 30)
        
        deleted_count, failed_count = delete_files(existing_files)
        
        print("-" * 30)
        print(f"✅ Deleted: {deleted_count} files")
        if failed_count > 0:
            print(f"❌ Failed: {failed_count} files")
        
        print(f"\n🎉 Cleanup completed!")
        print("📁 Root directory is now cleaner and more organized.")
        
    else:
        print("\n❌ Cleanup cancelled. No files were deleted.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())