import ast
import os
import argparse

def split_config_file(input_file, output_dir, target_leagues=None):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    with open(input_file, 'r') as file:
        content = file.read()

    # Parse the content as a Python abstract syntax tree
    tree = ast.parse(content)

    # Find the LEAGUE_CONFIGS dictionary
    league_configs = None
    for node in ast.walk(tree):
        if isinstance(node, ast.Assign):
            for target in node.targets:
                if isinstance(target, ast.Name) and target.id == 'LEAGUE_CONFIGS':
                    league_configs = ast.literal_eval(node.value)
                    break

    if league_configs is None:
        print("Error: Could not find LEAGUE_CONFIGS in the input file")
        return

    # Filter leagues if target_leagues specified
    if target_leagues:
        filtered_configs = {k: v for k, v in league_configs.items() if k in target_leagues}
        league_configs = filtered_configs
        print(f"Filtering to {len(league_configs)} target leagues: {target_leagues}")

    # Process each league
    for league_name, league_config in league_configs.items():
        output_file = os.path.join(output_dir, f"{league_name}.py")
        print(f"Creating {output_file}")

        with open(output_file, 'w') as out_file:
            out_file.write(f"LEAGUE_CONFIGS = {{\n")
            out_file.write(f"    \"{league_name}\": {{\n")
            for key, value in league_config.items():
                out_file.write(f"        \"{key}\": {repr(value)},\n")
            out_file.write("    }\n")
            out_file.write("}\n\n")

            # Add the configuration variables
            out_file.write("# Set the current league\n")
            out_file.write(f"CURRENT_LEAGUE = \"{league_name}\"\n")
            out_file.write("# Access the current league's configuration\n")
            out_file.write("CURRENT_CONFIG = LEAGUE_CONFIGS[CURRENT_LEAGUE]\n")
            out_file.write("CURRENT_CONFIG[\"CURRENT_LEAGUE\"] = CURRENT_LEAGUE\n")
            out_file.write("LEAGUE_STATS_URL = CURRENT_CONFIG[\"LEAGUE_STATS_URL\"]\n")
            out_file.write("LEAGUE_TABLE_URL = CURRENT_CONFIG[\"LEAGUE_TABLE_URL\"]\n")
            out_file.write("TEAM_URLS = CURRENT_CONFIG[\"TEAM_URLS\"]\n")
            out_file.write("HEAD_TO_HEAD_URLS = CURRENT_CONFIG[\"HEAD_TO_HEAD_URLS\"]\n")
            out_file.write("TEAM_NAME_MAPPING = CURRENT_CONFIG[\"TEAM_NAME_MAPPING\"]\n")

    print(f"Extracted {len(league_configs)} leagues.")

# Usage
if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Split config file into individual league files")
    parser.add_argument("--league", type=str, help="Process a specific league only")
    parser.add_argument("--leagues", type=str, help="Process specific leagues (comma-separated)")
    args = parser.parse_args()

    # Determine target leagues
    target_leagues = None
    if args.league:
        target_leagues = [args.league]
    elif args.leagues:
        target_leagues = [league.strip() for league in args.leagues.split(",")]

    # Get the directory of the current script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # Use paths relative to the script directory
    input_file = os.path.join(script_dir, 'generated_league_data.py')
    output_dir = os.path.join(script_dir, 'league_configs')
    split_config_file(input_file, output_dir, target_leagues)
