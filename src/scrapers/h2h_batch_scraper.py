#!/usr/bin/env python3
"""
Batch H2H scraper that processes URLs in batches of 4 with automatic restart capability.
Implements checkpoint system to track progress and resume from interruptions.
"""

import os
import json
import time
import logging
from typing import Dict, List, Set, Optional
from datetime import datetime, timedelta
from head_to_head import get_head_to_head_stats, RateLimitedScraper, USER_AGENTS
from utils import save_head_to_head_stats_to_csv, get_data_directory

# Configuration
BATCH_SIZE = 4
# Use absolute path to ensure consistency regardless of working directory
import os
CHECKPOINT_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "checkpoints")
PROGRESS_FILE = "h2h_progress.json"

class H2HBatchScraper:
    """Batch scraper for H2H data with checkpoint and resume capabilities."""

    def __init__(self, league_name: str, h2h_urls: Dict[str, str], scraper: RateLimitedScraper):
        self.league_name = league_name
        self.h2h_urls = h2h_urls
        self.scraper = scraper
        self.checkpoint_file = os.path.join(CHECKPOINT_DIR, f"{league_name}_h2h_checkpoint.json")
        self.progress_file = os.path.join(CHECKPOINT_DIR, f"{league_name}_h2h_progress.json")

        # Ensure checkpoint directory exists
        os.makedirs(CHECKPOINT_DIR, exist_ok=True)

        # Initialize progress tracking
        self.completed_urls: Set[str] = set()
        self.failed_urls: Set[str] = set()
        self.retry_data: Dict[str, Dict] = {}  # Track retry count and last attempt time
        self.permanently_failed_urls: Set[str] = set()  # URLs that exceeded max retries
        self.current_position = 0
        self.total_urls = len(h2h_urls)
        self.processed_since_last_retry = 0  # Counter for cooldown logic

        # Load existing progress
        self._load_progress()

    def _load_progress(self) -> None:
        """Load progress from checkpoint files."""
        try:
            if os.path.exists(self.checkpoint_file):
                with open(self.checkpoint_file, 'r') as f:
                    checkpoint_data = json.load(f)
                    self.completed_urls = set(checkpoint_data.get('completed_urls', []))

                    # Handle legacy failed_urls format and new retry system
                    legacy_failed = checkpoint_data.get('failed_urls', [])
                    self.retry_data = checkpoint_data.get('retry_data', {})
                    self.permanently_failed_urls = set(checkpoint_data.get('permanently_failed_urls', []))
                    self.processed_since_last_retry = checkpoint_data.get('processed_since_last_retry', 0)

                    # Migrate legacy failed URLs to retry system
                    if legacy_failed and not self.retry_data:
                        current_time = datetime.now().isoformat()
                        for url in legacy_failed:
                            self.retry_data[url] = {
                                'retry_count': 1,
                                'last_attempt_time': current_time
                            }
                        logging.info(f"Migrated {len(legacy_failed)} legacy failed URLs to retry system")

                    # Update failed_urls set from retry_data for backward compatibility
                    max_retries = checkpoint_data.get('max_retries', 3)  # Use saved value or default
                    self.failed_urls = set(url for url, data in self.retry_data.items()
                                         if data['retry_count'] < max_retries)

                    self.current_position = checkpoint_data.get('current_position', 0)

                    logging.info(f"Loaded checkpoint: {len(self.completed_urls)} completed, "
                               f"{len(self.failed_urls)} failed (retryable), "
                               f"{len(self.permanently_failed_urls)} permanently failed, "
                               f"position {self.current_position}")

                    # Check if any failed URLs are ready for retry
                    self._check_retry_eligibility()
            else:
                # No checkpoint file exists, check existing CSV data
                logging.info("No checkpoint file found. Checking existing CSV data.")
                self.completed_urls = set()
                self.failed_urls = set()
                self.retry_data = {}
                self.permanently_failed_urls = set()
                self.current_position = 0
                self.processed_since_last_retry = 0

                # Check existing CSV file for completed URLs
                self._load_existing_csv_data()
                
                # Log the results of CSV loading
                if self.completed_urls:
                    logging.info(f"CSV loading complete: {len(self.completed_urls)} URLs marked as completed, position set to {self.current_position}")

        except Exception as e:
            logging.warning(f"Could not load checkpoint: {e}. Checking existing CSV data.")
            self.completed_urls = set()
            self.failed_urls = set()
            self.retry_data = {}
            self.permanently_failed_urls = set()
            self.current_position = 0
            self.processed_since_last_retry = 0

            # Check existing CSV file for completed URLs
            self._load_existing_csv_data()

    def _load_existing_csv_data(self) -> None:
        """Load existing CSV data to determine what URLs have already been completed."""
        # Try multiple possible paths for the CSV file
        possible_paths = [
            f"../../data/raw/{self.league_name}/{self.league_name}_head_to_head_stats.csv",
            f"data/raw/{self.league_name}/{self.league_name}_head_to_head_stats.csv",
            f"../data/raw/{self.league_name}/{self.league_name}_head_to_head_stats.csv"
        ]
        
        csv_file = None
        for path in possible_paths:
            if os.path.exists(path):
                csv_file = path
                break
        
        if not csv_file:
            logging.info("No existing CSV file found. Starting fresh.")
            logging.debug(f"Tried paths: {possible_paths}")
            return
        
        logging.info(f"Found CSV file at: {csv_file}")

        try:
            import pandas as pd
            df = pd.read_csv(csv_file)

            # Extract matchups from the CSV
            existing_matchups = set(df['Matchup'].tolist())

            # Find which URLs from our config correspond to these matchups
            for matchup, url in self.h2h_urls.items():
                if matchup in existing_matchups:
                    self.completed_urls.add(matchup)

            # Set current position to skip completed URLs when iterating through h2h_urls
            # Find the highest index of completed URLs to set proper position
            url_items = list(self.h2h_urls.items())
            max_completed_index = -1
            for i, (matchup, url) in enumerate(url_items):
                if matchup in self.completed_urls:
                    max_completed_index = max(max_completed_index, i)
            
            # Set position to continue after the last completed URL
            self.current_position = max_completed_index + 1 if max_completed_index >= 0 else 0

            logging.info(f"Loaded {len(self.completed_urls)} completed URLs from existing CSV file")
            logging.info(f"Set current position to {self.current_position} to continue processing")

        except Exception as e:
            logging.warning(f"Could not load existing CSV data: {e}. Starting fresh.")

    def _save_progress(self) -> None:
        """Save current progress to checkpoint file."""
        try:
            checkpoint_data = {
                'league_name': self.league_name,
                'completed_urls': list(self.completed_urls),
                'failed_urls': list(self.failed_urls),  # Keep for backward compatibility
                'retry_data': self.retry_data,
                'permanently_failed_urls': list(self.permanently_failed_urls),
                'processed_since_last_retry': self.processed_since_last_retry,
                'current_position': self.current_position,
                'total_urls': self.total_urls,
                'last_updated': datetime.now().isoformat(),
                'batch_size': BATCH_SIZE,
                'max_retries': 3,  # Use constant value
                'retry_cooldown_urls': 20  # Use constant value
            }

            with open(self.checkpoint_file, 'w') as f:
                json.dump(checkpoint_data, f, indent=2)

            # Also save human-readable progress
            progress_data = {
                'league': self.league_name,
                'progress': f"{len(self.completed_urls)}/{self.total_urls}",
                'percentage': round((len(self.completed_urls) / self.total_urls) * 100, 2) if self.total_urls > 0 else 0,
                'completed': len(self.completed_urls),
                'failed': len(self.failed_urls),
                'permanently_failed': len(self.permanently_failed_urls),
                'remaining': self.total_urls - len(self.completed_urls) - len(self.failed_urls) - len(self.permanently_failed_urls),
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            with open(self.progress_file, 'w') as f:
                json.dump(progress_data, f, indent=2)

            logging.debug(f"Progress saved: {progress_data['progress']} ({progress_data['percentage']}%)")

        except Exception as e:
            logging.error(f"Failed to save progress: {e}")

    def _check_retry_eligibility(self) -> None:
        """Check if any failed URLs are eligible for retry based on cooldown."""
        if not self.retry_data:
            return

        # Use default values if constants not available
        retry_cooldown = getattr(self, 'retry_cooldown_urls', RETRY_COOLDOWN_URLS if 'RETRY_COOLDOWN_URLS' in globals() else 20)
        max_retries = getattr(self, 'max_retries', MAX_RETRIES if 'MAX_RETRIES' in globals() else 3)

        cooldown_met = self.processed_since_last_retry >= retry_cooldown
        
        # Condition for forcing retries:
        # 1. Cooldown is met (processed enough *other* successful URLs).
        # OR
        # 2. All URLs in the league have had at least one processing attempt (i.e., they are all categorized as
        #    completed, currently failed (retryable), or permanently failed), AND there are URLs currently in the
        #    failed_urls set (meaning they are awaiting retry and there's no "new" work to do).
        all_urls_categorized = (len(self.completed_urls) + len(self.failed_urls) + len(self.permanently_failed_urls)) >= self.total_urls
        trigger_retry_cycle = all_urls_categorized and len(self.failed_urls) > 0
        
        reason_for_retry_check = ""
        if cooldown_met:
            reason_for_retry_check = "cooldown met"
        elif trigger_retry_cycle:
            reason_for_retry_check = "all initial attempts done and failures exist"

        if cooldown_met or trigger_retry_cycle:
            logging.debug(f"Triggering retries: cooldown_met={cooldown_met}, all_urls_categorized={all_urls_categorized}, len(self.failed_urls)={len(self.failed_urls)}")
            # Find URLs that can be retried (haven't exceeded max retries)
            retryable_urls = []
            for url, data in self.retry_data.items():
                if data['retry_count'] < max_retries and url not in self.completed_urls:
                    retryable_urls.append(url)

            if retryable_urls:
                # Move retryable URLs back to pending (remove from failed_urls)
                for url in retryable_urls:
                    if url in self.failed_urls:
                        self.failed_urls.remove(url)

                self.processed_since_last_retry = 0  # Reset counter
                # Use the more descriptive reason_for_retry_check variable
                logging.info(f"Moved {len(retryable_urls)} failed URLs back to pending for retry ({reason_for_retry_check}): {retryable_urls}")

    def _handle_failed_url(self, matchup: str) -> None:
        """Handle a failed URL by updating retry data and determining if it should be permanently failed."""
        current_time = datetime.now().isoformat()

        if matchup in self.retry_data:
            # Increment retry count
            self.retry_data[matchup]['retry_count'] += 1
            self.retry_data[matchup]['last_attempt_time'] = current_time
        else:
            # First failure
            self.retry_data[matchup] = {
                'retry_count': 1,
                'last_attempt_time': current_time
            }

        retry_count = self.retry_data[matchup]['retry_count']
        max_retries = getattr(self, 'max_retries', MAX_RETRIES if 'MAX_RETRIES' in globals() else 3)

        if retry_count >= max_retries:
            # Move to permanently failed
            self.permanently_failed_urls.add(matchup)
            if matchup in self.failed_urls:
                self.failed_urls.remove(matchup)
            logging.warning(f"✗ URL permanently failed after {retry_count} attempts: {matchup}")
        else:
            # Add to failed URLs for potential retry
            self.failed_urls.add(matchup)
            logging.warning(f"✗ URL failed (attempt {retry_count}/{max_retries}): {matchup}")

    def _get_next_batch(self) -> List[tuple]:
        """Get the next batch of URLs to process."""
        # Check if any failed URLs are ready for retry
        self._check_retry_eligibility()

        url_items = list(self.h2h_urls.items())
        batch = []

        # First, prioritize retry URLs (those in retry_data but not in failed_urls anymore)
        retry_urls = []
        for matchup, url in url_items:
            if (matchup in self.retry_data and
                matchup not in self.failed_urls and
                matchup not in self.completed_urls and
                matchup not in self.permanently_failed_urls):
                retry_urls.append((matchup, url))

        # Add retry URLs to batch first
        for matchup, url in retry_urls:
            batch.append((matchup, url))
            if len(batch) >= BATCH_SIZE:
                return batch

        # Then add unprocessed URLs (regardless of position if we have completed URLs from CSV)
        for i, (matchup, url) in enumerate(url_items):
            # Skip already processed URLs
            if (matchup in self.completed_urls or
                matchup in self.failed_urls or
                matchup in self.permanently_failed_urls):
                continue

            # Skip retry URLs that were already handled above (only those eligible for retry)
            if (matchup in self.retry_data and
                matchup not in self.failed_urls and
                matchup not in self.permanently_failed_urls):
                continue

            # If we have completed URLs from CSV, process any remaining unprocessed URLs
            # Otherwise, respect the current position
            if len(self.completed_urls) > 0:
                # Process any unprocessed URL regardless of position
                batch.append((matchup, url))
            else:
                # Skip URLs before current position (normal sequential processing)
                if i < self.current_position:
                    continue
                batch.append((matchup, url))

            # Stop when we have a full batch
            if len(batch) >= BATCH_SIZE:
                break

        return batch

    def _process_batch(self, batch: List[tuple]) -> List[Dict]:
        """Process a batch of H2H URLs."""
        batch_results = []

        for matchup, url in batch:
            try:
                logging.info(f"Processing H2H: {matchup}")
                h2h_stats = get_head_to_head_stats(url, self.scraper)

                if h2h_stats:  # Successfully scraped
                    # Create the data structure expected by CSV with proper column order
                    current_match_data = {
                        "Matchup": matchup,  # Use original config key
                        "home_team_name": h2h_stats.get("home_team_name", ""),
                        "away_team_name": h2h_stats.get("away_team_name", ""),
                        "total_matches": h2h_stats.get("total_matches", 0),
                        "home_win_percentage": h2h_stats.get("home_win_percentage", 0.0),
                        "away_win_percentage": h2h_stats.get("away_win_percentage", 0.0),
                        "draw_percentage": h2h_stats.get("draw_percentage", 0.0),
                        "home_wins": h2h_stats.get("home_wins", 0),
                        "away_wins": h2h_stats.get("away_wins", 0),
                        "draws": h2h_stats.get("draws", 0),
                        "home_goals": h2h_stats.get("home_goals", 0),
                        "away_goals": h2h_stats.get("away_goals", 0),
                        "over_1_5_percentage": h2h_stats.get("over_1_5_percentage", 0.0),
                        "over_2_5_percentage": h2h_stats.get("over_2_5_percentage", 0.0),
                        "over_3_5_percentage": h2h_stats.get("over_3_5_percentage", 0.0),
                        "btts_percentage": h2h_stats.get("btts_percentage", 0.0),
                        "home_clean_sheet_percentage": h2h_stats.get("home_clean_sheet_percentage", 0.0),
                        "away_clean_sheet_percentage": h2h_stats.get("away_clean_sheet_percentage", 0.0),
                        "recent_results": h2h_stats.get("recent_results", "")
                    }
                    batch_results.append(current_match_data)
                    self.completed_urls.add(matchup)
                    # Remove from failed/retry states as it's now complete
                    if matchup in self.failed_urls:
                        self.failed_urls.remove(matchup)
                    if matchup in self.retry_data:
                        del self.retry_data[matchup]
                    
                    self.processed_since_last_retry += 1  # Increment counter for successful processing
                    logging.info(f"✓ Successfully scraped: {matchup}")
                else:
                    logging.warning(f"✗ Failed to scrape (empty result): {matchup}")
                    self._handle_failed_url(matchup)

            except Exception as e:
                logging.error(f"✗ Error scraping {matchup}: {e}")
                self._handle_failed_url(matchup)

            # Update position after each URL
            url_items = list(self.h2h_urls.items())
            for i, (m, u) in enumerate(url_items):
                if m == matchup:
                    self.current_position = i + 1
                    break

        return batch_results

    def run_batch(self) -> bool:
        """Run a single batch of H2H scraping. Returns True if more work remains for the league."""
        batch = self._get_next_batch()

        if batch:
            logging.info(f"Processing batch of {len(batch)} URLs for {self.league_name}")
            batch_results = self._process_batch(batch)

            if batch_results:
                try:
                    save_head_to_head_stats_to_csv(
                        batch_results,
                        self.league_name,
                        f"{self.league_name}_head_to_head_stats.csv",
                        append=True
                    )
                    logging.info(f"Saved {len(batch_results)} H2H results to CSV")
                except Exception as e:
                    logging.error(f"Failed to save batch results: {e}")
            
            # Save progress after processing a batch, as _process_batch updates state
            self._save_progress()
        else:
            logging.info(f"No batch of URLs obtained for {self.league_name} in this iteration (might be due to retry cooldowns or no new URLs).")
            # Still need to save progress in case _check_retry_eligibility (called by _get_next_batch) 
            # made changes (e.g. to self.failed_urls or self.processed_since_last_retry) that need persisting.
            self._save_progress()

        # Determine return value based on overall league status, regardless of whether this specific batch was empty
        if len(self.completed_urls) + len(self.permanently_failed_urls) < self.total_urls:
            progress_percent = round((len(self.completed_urls) / self.total_urls) * 100, 1) if self.total_urls > 0 else 0
            actual_remaining_to_attempt = self.total_urls - (len(self.completed_urls) + len(self.permanently_failed_urls))
            logging.info(
                f"League {self.league_name} status: Progress {len(self.completed_urls)}/{self.total_urls} ({progress_percent}%). "
                f"{actual_remaining_to_attempt} URLs still need to be completed or permanently fail. More work remains."
            )
            return True # More work remains for this league
        else:
            logging.info(
                f"All URLs processed for {self.league_name}! "
                f"Completed: {len(self.completed_urls)}, Permanently Failed: {len(self.permanently_failed_urls)}"
            )
            return False # League is fully processed

    def _cleanup_checkpoint(self) -> None:
        """Clean up checkpoint files after completion."""
        try:
            if os.path.exists(self.checkpoint_file):
                os.remove(self.checkpoint_file)
                logging.info(f"Removed checkpoint file: {self.checkpoint_file}")
        except Exception as e:
            logging.warning(f"Could not remove checkpoint file: {e}")

    def get_progress_summary(self) -> Dict:
        """Get current progress summary."""
        return {
            'league': self.league_name,
            'total_urls': self.total_urls,
            'completed': len(self.completed_urls),
            'failed': len(self.failed_urls),
            'permanently_failed': len(self.permanently_failed_urls),
            'remaining': self.total_urls - len(self.completed_urls) - len(self.failed_urls) - len(self.permanently_failed_urls),
            'percentage': round((len(self.completed_urls) / self.total_urls) * 100, 2) if self.total_urls > 0 else 0,
            'current_position': self.current_position
        }


def load_checkpoint(league_name: str, configured_h2h_urls: Optional[Dict[str, str]] = None) -> Dict:
    """
    Load checkpoint data for a specific league.
    Prioritizes a 'complete' CSV (all configured URLs present) over a partial JSON checkpoint.
    """
    checkpoint_file = os.path.join(CHECKPOINT_DIR, f"{league_name}_h2h_checkpoint.json")
    
    # Try multiple possible paths for the CSV file
    possible_csv_paths = [
        os.path.join(get_data_directory(league_name, raw=True), f"{league_name}_head_to_head_stats.csv"),
        f"../../data/raw/{league_name}/{league_name}_head_to_head_stats.csv",
        f"data/raw/{league_name}/{league_name}_head_to_head_stats.csv",
        f"../data/raw/{league_name}/{league_name}_head_to_head_stats.csv"
    ]
    
    csv_file_path = None
    for path in possible_csv_paths:
        if os.path.exists(path):
            csv_file_path = path
            break

    total_config_urls = 0
    config_url_keys = set()

    if configured_h2h_urls:
        total_config_urls = len(configured_h2h_urls)
        config_url_keys = set(configured_h2h_urls.keys())

    # Priority 1: "Complete" CSV Check (only if config URLs are provided)
    if total_config_urls > 0 and csv_file_path and os.path.exists(csv_file_path):
        try:
            import pandas as pd
            df = pd.read_csv(csv_file_path)
            if 'Matchup' in df.columns:
                csv_matchups = set(df['Matchup'].tolist())
                # Find which configured URLs are present in CSV
                csv_relevant_matchups = csv_matchups.intersection(config_url_keys)
                missing_count = total_config_urls - len(csv_relevant_matchups)

                # Check if ALL configured URLs are present in CSV
                if missing_count == 0:
                    logging.info(f"CSV for {league_name} contains all {total_config_urls} configured URLs. Marking as complete based on CSV.")
                    return {
                        'completed_urls': list(config_url_keys),
                        'failed_urls': [],
                        'permanently_failed_urls': [],
                        'retry_data': {},
                        'current_position': total_config_urls,
                        'total_urls': total_config_urls,
                        'is_complete_from_csv': True
                    }
                else:
                    # CSV exists but doesn't contain all configured URLs
                    # Check if we have a high completion rate (e.g., 95%+) and should consider it effectively complete
                    completion_rate = len(csv_relevant_matchups) / total_config_urls
                    if completion_rate >= 0.95:  # 95% or higher completion rate
                        missing_urls = config_url_keys - csv_relevant_matchups
                        logging.info(f"CSV for {league_name} contains {len(csv_relevant_matchups)}/{total_config_urls} configured URLs ({completion_rate:.1%} completion rate).")
                        logging.info(f"Missing URLs appear to be problematic: {sorted(missing_urls)}")
                        logging.info(f"Marking as effectively complete due to high completion rate.")
                        return {
                            'completed_urls': list(csv_relevant_matchups),
                            'failed_urls': [],
                            'permanently_failed_urls': list(missing_urls),
                            'retry_data': {},
                            'current_position': total_config_urls,
                            'total_urls': total_config_urls,
                            'is_complete_from_csv': True
                        }
                    else:
                        logging.info(f"CSV for {league_name} contains {len(csv_relevant_matchups)}/{total_config_urls} configured URLs. {missing_count} URLs still need processing.")
            else:
                logging.warning(f"CSV for {league_name} ({csv_file_path}) lacks 'Matchup' column for complete check.")
        except ImportError:
            logging.warning("Pandas not installed. Cannot perform 'complete CSV' check in load_checkpoint.")
        except Exception as e:
            logging.warning(f"Error processing CSV {csv_file_path} for 'complete' check: {e}")

    # Priority 2: JSON Checkpoint
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r') as f:
                data = json.load(f)
                logging.info(f"Loaded JSON checkpoint for {league_name}.")
                if configured_h2h_urls and data.get('total_urls') != total_config_urls:
                    logging.warning(
                        f"Checkpoint total_urls ({data.get('total_urls')}) for {league_name} "
                        f"differs from config ({total_config_urls}). Updating checkpoint's total_urls."
                    )
                    data['total_urls'] = total_config_urls
                # Ensure essential keys exist if loading an old/minimal checkpoint
                data.setdefault('completed_urls', [])
                data.setdefault('failed_urls', [])
                data.setdefault('permanently_failed_urls', [])
                data.setdefault('retry_data', {})
                data.setdefault('current_position', 0)
                if 'total_urls' not in data and configured_h2h_urls:
                     data['total_urls'] = total_config_urls
                return data
        except (json.JSONDecodeError, IOError) as e:
            logging.warning(f"Error loading JSON checkpoint {checkpoint_file}: {e}. Will check for partial CSV.")

    # Priority 3: Partial CSV Fallback
    if csv_file_path and os.path.exists(csv_file_path):
        try:
            import pandas as pd
            df = pd.read_csv(csv_file_path)
            if 'Matchup' in df.columns:
                completed_matchups_from_csv = list(df['Matchup'].unique())

                # Filter these matchups to only those present in the current configuration if available
                if configured_h2h_urls:
                    actual_completed_in_config = [m for m in completed_matchups_from_csv if m in config_url_keys]
                    missing_count = total_config_urls - len(actual_completed_in_config)
                    
                    # Check if ALL configured URLs are present in CSV (same logic as Priority 1)
                    if missing_count == 0:
                        logging.info(f"CSV for {league_name} contains all {total_config_urls} configured URLs. Marking as complete based on CSV (no checkpoint).")
                        return {
                            'completed_urls': list(config_url_keys),
                            'failed_urls': [],
                            'permanently_failed_urls': [],
                            'retry_data': {},
                            'current_position': total_config_urls,
                            'total_urls': total_config_urls,
                            'is_complete_from_csv': True
                        }
                else:
                    actual_completed_in_config = completed_matchups_from_csv # Use as is if no config to filter by

                num_completed_for_checkpoint = len(actual_completed_in_config)
                logging.info(f"No JSON checkpoint for {league_name}. Using partial CSV with {num_completed_for_checkpoint} relevant entries as baseline.")

                return {
                    'completed_urls': actual_completed_in_config,
                    'failed_urls': [],
                    'permanently_failed_urls': [],
                    'retry_data': {},
                    'current_position': num_completed_for_checkpoint,
                    'total_urls': total_config_urls if configured_h2h_urls else num_completed_for_checkpoint # Best guess for total
                }
            else:
                logging.warning(f"CSV for {league_name} ({csv_file_path}) (partial check) lacks 'Matchup' column.")
        except ImportError:
            logging.warning("Pandas not installed. Cannot use partial CSV in load_checkpoint.")
        except Exception as e:
            logging.warning(f"Error loading partial CSV {csv_file_path}: {e}")

    # Priority 4: Start Fresh
    logging.info(f"No checkpoint or usable CSV found for {league_name}. Starting fresh.")
    return {
        'completed_urls': [],
        'failed_urls': [],
        'permanently_failed_urls': [],
        'retry_data': {},
        'current_position': 0,
        'total_urls': total_config_urls if configured_h2h_urls else 0
    }


def run_h2h_batch_scraping(league_name: str, h2h_urls: Dict[str, str], scraper: RateLimitedScraper) -> bool:
    """
    Run H2H batch scraping for a single league.
    Returns True if more work remains, False if complete.
    Raises exceptions for critical errors that should stop processing this league.
    """
    try:
        batch_scraper = H2HBatchScraper(league_name, h2h_urls, scraper)
        return batch_scraper.run_batch()
    except Exception as e:
        logging.error(f"Critical error in batch scraping for {league_name}: {e}")
        logging.exception("Full traceback:")
        # Re-raise the exception to be handled by the calling automation script
        raise


def get_all_progress() -> List[Dict]:
    """Get progress for all leagues with active checkpoints."""
    progress_list = []

    if not os.path.exists(CHECKPOINT_DIR):
        return progress_list

    for filename in os.listdir(CHECKPOINT_DIR):
        if filename.endswith('_h2h_progress.json'):
            try:
                filepath = os.path.join(CHECKPOINT_DIR, filename)
                with open(filepath, 'r') as f:
                    progress_data = json.load(f)
                    progress_list.append(progress_data)
            except Exception as e:
                logging.warning(f"Could not read progress file {filename}: {e}")

    return progress_list


def print_progress_summary():
    """Print a summary of all H2H scraping progress."""
    progress_list = get_all_progress()

    if not progress_list:
        print("No active H2H scraping progress found.")
        return

    print("\n" + "="*60)
    print("H2H SCRAPING PROGRESS SUMMARY")
    print("="*60)

    for progress in progress_list:
        print(f"League: {progress['league']}")
        print(f"Progress: {progress['progress']} ({progress['percentage']}%)")
        permanently_failed = progress.get('permanently_failed', 0)
        print(f"Completed: {progress['completed']}, Failed: {progress['failed']}, Permanently Failed: {permanently_failed}, Remaining: {progress['remaining']}")
        print(f"Last Updated: {progress['last_updated']}")
        print("-" * 40)

    total_completed = sum(p['completed'] for p in progress_list)
    total_remaining = sum(p['remaining'] for p in progress_list)
    total_failed = sum(p['failed'] for p in progress_list)
    total_permanently_failed = sum(p.get('permanently_failed', 0) for p in progress_list)

    print(f"TOTALS - Completed: {total_completed}, Failed: {total_failed}, Permanently Failed: {total_permanently_failed}, Remaining: {total_remaining}")
    print("="*60)
