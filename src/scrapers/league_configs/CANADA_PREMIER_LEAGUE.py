LEAGUE_CONFIGS = {
    "CANADA_PREMIER_LEAGUE": {
        "LEAGUE_STATS_URL": 'https://www.soccerstats.com/latest.asp?league=canada',
        "LEAGUE_TABLE_URL": 'https://footystats.org/canada/canadian-premier-league',
        "TEAM_URLS": {
            'A. Ottawa': 'https://www.soccerstats.com/teamstats.asp?league=canada&stats=u5838-a.-ottawa',
            'Cavalry': 'https://www.soccerstats.com/teamstats.asp?league=canada&stats=u5843-cavalry',
            'Forge': 'https://www.soccerstats.com/teamstats.asp?league=canada&stats=u5841-forge',
            'Hfx Wanderers': 'https://www.soccerstats.com/teamstats.asp?league=canada&stats=u5840-hfx-wanderers',
            'Pacific': 'https://www.soccerstats.com/teamstats.asp?league=canada&stats=u5839-pacific',
            'Valour': 'https://www.soccerstats.com/teamstats.asp?league=canada&stats=u5842-valour',
            'Vancouver Fc': 'https://www.soccerstats.com/teamstats.asp?league=canada&stats=u9964-vancouver-fc',
            'York Utd': 'https://www.soccerstats.com/teamstats.asp?league=canada&stats=u5844-york-utd',
        },
        "HEAD_TO_HEAD_URLS": {
            'Valour Fc vs Pacific Fc': 'https://footystats.org/canada/valour-fc-vs-pacific-fc-h2h-stats',
            'Cavalry Fc vs Vancouver Fc': 'https://footystats.org/canada/cavalry-fc-vs-vancouver-fc-h2h-stats',
            'Forge Fc vs York9 Fc': 'https://footystats.org/canada/forge-fc-vs-york9-fc-h2h-stats',
            'Hfx Wanderers Fc vs Atletico Ottawa': 'https://footystats.org/canada/hfx-wanderers-fc-vs-atletico-ottawa-h2h-stats',
            'Atletico Ottawa vs Vancouver Fc': 'https://footystats.org/canada/atletico-ottawa-vs-vancouver-fc-h2h-stats',
            'Valour Fc vs Hfx Wanderers Fc': 'https://footystats.org/canada/valour-fc-vs-hfx-wanderers-fc-h2h-stats',
            'Forge Fc vs Cavalry Fc': 'https://footystats.org/canada/forge-fc-vs-cavalry-fc-h2h-stats',
            'York9 Fc vs Pacific Fc': 'https://footystats.org/canada/york9-fc-vs-pacific-fc-h2h-stats',
            'Cavalry Fc vs Pacific Fc': 'https://footystats.org/canada/cavalry-fc-vs-pacific-fc-h2h-stats',
            'Valour Fc vs Atletico Ottawa': 'https://footystats.org/canada/valour-fc-vs-atletico-ottawa-h2h-stats',
            'Forge Fc vs Vancouver Fc': 'https://footystats.org/canada/forge-fc-vs-vancouver-fc-h2h-stats',
            'Hfx Wanderers Fc vs York9 Fc': 'https://footystats.org/canada/hfx-wanderers-fc-vs-york9-fc-h2h-stats',
            'Hfx Wanderers Fc vs Vancouver Fc': 'https://footystats.org/canada/hfx-wanderers-fc-vs-vancouver-fc-h2h-stats',
            'Valour Fc vs York9 Fc': 'https://footystats.org/canada/valour-fc-vs-york9-fc-h2h-stats',
            'Forge Fc vs Pacific Fc': 'https://footystats.org/canada/forge-fc-vs-pacific-fc-h2h-stats',
            'Cavalry Fc vs Atletico Ottawa': 'https://footystats.org/canada/cavalry-fc-vs-atletico-ottawa-h2h-stats',
            'York9 Fc vs Vancouver Fc': 'https://footystats.org/canada/york9-fc-vs-vancouver-fc-h2h-stats',
            'Forge Fc vs Atletico Ottawa': 'https://footystats.org/canada/forge-fc-vs-atletico-ottawa-h2h-stats',
            'Valour Fc vs Cavalry Fc': 'https://footystats.org/canada/valour-fc-vs-cavalry-fc-h2h-stats',
            'Hfx Wanderers Fc vs Pacific Fc': 'https://footystats.org/canada/hfx-wanderers-fc-vs-pacific-fc-h2h-stats',
            'York9 Fc vs Atletico Ottawa': 'https://footystats.org/canada/york9-fc-vs-atletico-ottawa-h2h-stats',
            'Pacific Fc vs Vancouver Fc': 'https://footystats.org/canada/pacific-fc-vs-vancouver-fc-h2h-stats',
            'Hfx Wanderers Fc vs Cavalry Fc': 'https://footystats.org/canada/hfx-wanderers-fc-vs-cavalry-fc-h2h-stats',
            'Valour Fc vs Forge Fc': 'https://footystats.org/canada/valour-fc-vs-forge-fc-h2h-stats',
            'Pacific Fc vs Atletico Ottawa': 'https://footystats.org/canada/pacific-fc-vs-atletico-ottawa-h2h-stats',
            'Forge Fc vs Hfx Wanderers Fc': 'https://footystats.org/canada/forge-fc-vs-hfx-wanderers-fc-h2h-stats',
            'Valour Fc vs Vancouver Fc': 'https://footystats.org/canada/valour-fc-vs-vancouver-fc-h2h-stats',
            'Cavalry Fc vs York9 Fc': 'https://footystats.org/canada/cavalry-fc-vs-york9-fc-h2h-stats',
        },
        "TEAM_NAME_MAPPING": {
            'A. Ottawa': 'Atletico Ottawa',
            'Cavalry': 'Cavalry Fc',
            'Forge': 'Forge Fc',
            'Hfx Wanderers': 'Hfx Wanderers Fc',
            'Pacific': 'Pacific Fc',
            'Valour': 'Valour Fc',
            'Vancouver Fc': 'Vancouver Fc',
            'York Utd': 'York9 Fc',
        },
    }
}

# Set the current league
CURRENT_LEAGUE = "CANADA_PREMIER_LEAGUE"
# Access the current league's configuration
CURRENT_CONFIG = LEAGUE_CONFIGS[CURRENT_LEAGUE]
CURRENT_CONFIG["CURRENT_LEAGUE"] = CURRENT_LEAGUE
LEAGUE_STATS_URL = CURRENT_CONFIG["LEAGUE_STATS_URL"]
LEAGUE_TABLE_URL = CURRENT_CONFIG["LEAGUE_TABLE_URL"]
TEAM_URLS = CURRENT_CONFIG["TEAM_URLS"]
HEAD_TO_HEAD_URLS = CURRENT_CONFIG["HEAD_TO_HEAD_URLS"]
TEAM_NAME_MAPPING = CURRENT_CONFIG["TEAM_NAME_MAPPING"]
