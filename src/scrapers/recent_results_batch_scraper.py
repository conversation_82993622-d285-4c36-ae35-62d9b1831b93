#!/usr/bin/env python3
"""
Recent Results Batch Scraper - Processes recent results in batches of 4 URLs
This follows the same pattern as h2h_batch_scraper.py to handle rate limiting.
"""

import os
import json
import csv
import logging
import time
import random
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from recent_results_scraper import scrape_recent_results_from_h2h_page
from head_to_head import RateLimitedScraper, USER_AGENTS

# Configuration
BATCH_SIZE = 4
# Use absolute path to ensure consistency regardless of working directory
CHECKPOINT_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "checkpoints")
PROGRESS_FILE = "recent_results_progress.json"


class RecentResultsBatchProcessor:
    """Handles batch processing of recent results with checkpointing."""
    
    def __init__(self, league_name: str, h2h_urls: Dict[str, str]):
        self.league_name = league_name
        self.h2h_urls = h2h_urls
        self.total_urls = len(h2h_urls)
        self.current_position = 0
        self.completed_urls = set()
        self.failed_urls = set()
        self.retry_urls = []
        
        # Ensure checkpoint directory exists
        os.makedirs(CHECKPOINT_DIR, exist_ok=True)
        self.checkpoint_file = os.path.join(CHECKPOINT_DIR, f"{league_name}_{PROGRESS_FILE}")
        
        # Load existing progress
        self._load_checkpoint()

        # Also check existing CSV file to avoid duplicates
        self._load_existing_csv_data()

        logging.info(f"Initialized RecentResultsBatchProcessor for {league_name}")
        logging.info(f"Total URLs: {self.total_urls}, Completed: {len(self.completed_urls)}")
    
    def _load_checkpoint(self):
        """Load progress from checkpoint file."""
        if os.path.exists(self.checkpoint_file):
            try:
                with open(self.checkpoint_file, 'r') as f:
                    data = json.load(f)
                
                self.current_position = data.get('current_position', 0)
                self.completed_urls = set(data.get('completed_urls', []))
                self.failed_urls = set(data.get('failed_urls', []))
                self.retry_urls = data.get('retry_urls', [])
                
                logging.info(f"Loaded checkpoint: position {self.current_position}, "
                           f"completed {len(self.completed_urls)}, failed {len(self.failed_urls)}")
            except Exception as e:
                logging.warning(f"Failed to load checkpoint: {e}")

    def _load_existing_csv_data(self):
        """Load existing CSV data to avoid duplicating work."""
        try:
            # Determine output directory
            output_dir = f"../../data/raw/{self.league_name}"
            if not os.path.exists(output_dir):
                output_dir = f"data/raw/{self.league_name}"
            if not os.path.exists(output_dir):
                output_dir = f"../data/raw/{self.league_name}"

            output_file = os.path.join(output_dir, f"{self.league_name}_recent_results.csv")

            if os.path.exists(output_file):
                import csv
                with open(output_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    existing_matchups = set()
                    for row in reader:
                        if 'matchup' in row and row['matchup']:
                            existing_matchups.add(row['matchup'])

                # Add existing matchups to completed_urls
                before_count = len(self.completed_urls)
                self.completed_urls.update(existing_matchups)
                after_count = len(self.completed_urls)

                if after_count > before_count:
                    logging.info(f"Loaded {after_count - before_count} completed matchups from existing CSV")

        except Exception as e:
            logging.warning(f"Could not load existing CSV data: {e}")

    def _save_checkpoint(self):
        """Save current progress to checkpoint file."""
        try:
            data = {
                'league_name': self.league_name,
                'current_position': self.current_position,
                'total_urls': self.total_urls,
                'completed_urls': list(self.completed_urls),
                'failed_urls': list(self.failed_urls),
                'retry_urls': self.retry_urls,
                'last_updated': datetime.now().isoformat(),
                'batch_size': BATCH_SIZE
            }
            
            with open(self.checkpoint_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logging.error(f"Failed to save checkpoint: {e}")
    
    def get_next_batch(self) -> List[Tuple[str, str]]:
        """Get the next batch of URLs to process."""
        batch = []
        url_items = list(self.h2h_urls.items())
        
        # Add retry URLs to batch first
        retry_urls = [(matchup, url) for matchup, url in self.retry_urls]
        for matchup, url in retry_urls:
            batch.append((matchup, url))
            if len(batch) >= BATCH_SIZE:
                return batch
        
        # Then add unprocessed URLs
        for i, (matchup, url) in enumerate(url_items):
            # Skip already processed URLs
            if matchup in self.completed_urls or matchup in self.failed_urls:
                continue
            
            # Skip URLs before current position (unless we have retries)
            if i < self.current_position and not retry_urls:
                continue
            
            batch.append((matchup, url))
            
            # Stop when we have a full batch
            if len(batch) >= BATCH_SIZE:
                break
        
        return batch
    
    def _process_batch(self, batch: List[Tuple[str, str]], scraper: RateLimitedScraper) -> List[List[str]]:
        """Process a batch of URLs and return recent results."""
        batch_results = []

        for matchup, url in batch:
            # Skip if already completed
            if matchup in self.completed_urls:
                logging.info(f"⏭️ Skipping already completed matchup: {matchup}")
                continue

            try:
                logging.info(f"Processing recent results for matchup: {matchup}")
                recent_results = scrape_recent_results_from_h2h_page(url, scraper)

                if recent_results:
                    # Add matchup info to each result
                    for result in recent_results:
                        if len(result) >= 5:
                            batch_results.append([matchup] + result)  # Add matchup as first column

                    self.completed_urls.add(matchup)
                    logging.info(f"✅ Successfully processed {len(recent_results)} recent results for {matchup}")

                    # Remove from failed/retry states as it's now complete
                    if matchup in self.failed_urls:
                        self.failed_urls.remove(matchup)
                    self.retry_urls = [(m, u) for m, u in self.retry_urls if m != matchup]
                else:
                    logging.warning(f"⚠️ No recent results found for {matchup}")
                    self.completed_urls.add(matchup)  # Mark as completed even if no results

            except Exception as e:
                logging.error(f"❌ Error processing {matchup}: {e}")
                self.failed_urls.add(matchup)

                # Add to retry list if not already there
                if not any(m == matchup for m, u in self.retry_urls):
                    self.retry_urls.append((matchup, url))

        return batch_results
    
    def process_single_batch(self, scraper: RateLimitedScraper) -> Tuple[bool, List[List[str]]]:
        """
        Process a single batch of recent results.
        Returns (has_more_work, batch_results)
        """
        batch = self.get_next_batch()
        
        if not batch:
            logging.info(f"No more URLs to process for {self.league_name}")
            return False, []
        
        logging.info(f"Processing batch of {len(batch)} URLs for {self.league_name}")
        
        # Process the batch
        batch_results = self._process_batch(batch, scraper)
        
        # Update position
        url_items = list(self.h2h_urls.items())
        for matchup, url in batch:
            for i, (m, u) in enumerate(url_items):
                if m == matchup:
                    self.current_position = max(self.current_position, i + 1)
                    break
        
        # Save progress
        self._save_checkpoint()
        
        # Check if more work remains
        has_more_work = (
            len(self.completed_urls) + len(self.failed_urls) < self.total_urls or
            len(self.retry_urls) > 0
        )
        
        logging.info(f"Batch complete. Progress: {len(self.completed_urls)}/{self.total_urls} completed, "
                    f"{len(self.failed_urls)} failed, {len(self.retry_urls)} retries pending")
        
        return has_more_work, batch_results


def run_recent_results_batch_scraping(league_name: str, h2h_urls: Dict[str, str], scraper: RateLimitedScraper) -> bool:
    """
    Run recent results batch scraping for a single league.
    Returns True if more work remains, False if complete.
    """
    processor = RecentResultsBatchProcessor(league_name, h2h_urls)
    
    # Process one batch
    has_more_work, batch_results = processor.process_single_batch(scraper)
    
    # Save results to CSV if we have any
    if batch_results:
        save_recent_results_to_csv(league_name, batch_results)
    
    return has_more_work


def save_recent_results_to_csv(league_name: str, recent_results: List[List[str]]):
    """Save recent results to CSV file."""
    try:
        # Determine output directory
        output_dir = f"../../data/raw/{league_name}"
        if not os.path.exists(output_dir):
            output_dir = f"data/raw/{league_name}"
        if not os.path.exists(output_dir):
            output_dir = f"../data/raw/{league_name}"
        
        if not os.path.exists(output_dir):
            logging.error(f"Output directory not found: {output_dir}")
            return
        
        output_file = os.path.join(output_dir, f"{league_name}_recent_results.csv")
        
        # Check if file exists to determine if we need to write header
        file_exists = os.path.exists(output_file)
        
        # Write CSV file (append mode)
        with open(output_file, 'a' if file_exists else 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['matchup', 'date', 'home_team', 'home_score', 'away_team', 'away_score']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            # Write header only if file is new
            if not file_exists:
                writer.writeheader()
            
            # Write results
            for result in recent_results:
                if len(result) >= 6:  # matchup + 5 result fields
                    writer.writerow({
                        'matchup': result[0],
                        'date': result[1],
                        'home_team': result[2],
                        'home_score': result[3],
                        'away_team': result[4],
                        'away_score': result[5]
                    })
        
        logging.info(f"✅ Saved {len(recent_results)} recent results to {output_file}")
        
    except Exception as e:
        logging.error(f"Error saving recent results for {league_name}: {e}")


def get_recent_results_progress() -> Dict[str, Dict]:
    """Get progress for all leagues with recent results processing."""
    progress = {}
    
    if not os.path.exists(CHECKPOINT_DIR):
        return progress
    
    for filename in os.listdir(CHECKPOINT_DIR):
        if filename.endswith(f"_{PROGRESS_FILE}"):
            league_name = filename.replace(f"_{PROGRESS_FILE}", "")
            checkpoint_file = os.path.join(CHECKPOINT_DIR, filename)
            
            try:
                with open(checkpoint_file, 'r') as f:
                    data = json.load(f)
                    progress[league_name] = data
            except Exception as e:
                logging.warning(f"Failed to read progress for {league_name}: {e}")
    
    return progress


def print_recent_results_progress_summary():
    """Print a summary of recent results processing progress."""
    progress = get_recent_results_progress()
    
    if not progress:
        print("No recent results processing in progress.")
        return
    
    print("\n🔍 Recent Results Processing Progress:")
    print("=" * 50)
    
    for league_name, data in progress.items():
        total = data.get('total_urls', 0)
        completed = len(data.get('completed_urls', []))
        failed = len(data.get('failed_urls', []))
        retries = len(data.get('retry_urls', []))
        
        percentage = (completed / total * 100) if total > 0 else 0
        
        print(f"\n📊 {league_name}:")
        print(f"  Progress: {completed}/{total} ({percentage:.1f}%)")
        print(f"  Failed: {failed}")
        print(f"  Retries: {retries}")
        print(f"  Last Updated: {data.get('last_updated', 'Unknown')}")
