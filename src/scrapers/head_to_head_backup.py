import time
import random
import logging
import requests
import re # Added for global use
from bs4 import BeautifulSoup
from collections import deque
from datetime import datetime, timedelta
import threading
from typing import Optional, Dict, Deque, <PERSON>ple # Added Tuple
from utils import safe_extract
from team_mappings import get_team_mapping, normalize_team_name, SERBIAN_TEAM_MAPPINGS
try:
    from config import CURRENT_CONFIG
except ImportError:
    # This except block might still be useful if you ever intend to run
    # head_to_head.py completely standalone for some reason,
    # though it's less likely if main.py is the entry point.
    CURRENT_CONFIG = {}

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
    "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:54.0) Gecko/20100101 Firefox/54.0",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (<PERSON>HT<PERSON>, like Gecko) Chrome/58.0.3029.81 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:94.0) Gecko/20100101 Firefox/94.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Edg/95.0.1020.53",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64; rv:94.0) Gecko/20100101 Firefox/94.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Vivaldi/4.3",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Vivaldi/4.3",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/18.17763 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15",
    "Mozilla/5.0 (Linux; Android 11; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Mobile Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:88.0) Gecko/20100101 Firefox/88.0",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; Trident/7.0; rv:11.0) like Gecko",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:95.0) Gecko/20100101 Firefox/95.0",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:95.0) Gecko/20100101 Firefox/95.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Safari/605.1.15",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Mobile/15E148 Safari/604.1",
]


class RateLimitedScraper:
    def __init__(
        self,
        requests_per_window: int = 10,
        time_window_seconds: int = 60,
        base_delay: float = 5.0,
        max_retries: int = 30,
        user_agents: list = None,
    ):
        """
        Initialize the rate-limited scraper with configurable parameters.

        Args:
            requests_per_window: Maximum number of requests allowed in the time window
            time_window_seconds: Time window in seconds for rate limiting
            base_delay: Base delay between requests in seconds
            max_retries: Maximum number of retry attempts
            user_agents: List of user agent strings to rotate through
        """
        self.requests_per_window = requests_per_window
        self.time_window_seconds = time_window_seconds
        self.base_delay = base_delay
        self.max_retries = max_retries

        # Request tracking
        self.request_timestamps: Deque[datetime] = deque(maxlen=requests_per_window)
        self.lock = threading.Lock()

        # Session management
        self.session = requests.Session()
        self.user_agents = user_agents or [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            # ... add more user agents as needed
        ]

        # Error tracking
        self.consecutive_429s = 0
        self.last_successful_request: Optional[datetime] = None

    def _wait_for_rate_limit(self) -> None:
        """Ensure we don't exceed the rate limit by waiting if necessary."""
        with self.lock:
            now = datetime.now()

            # Remove timestamps older than our time window
            while (
                self.request_timestamps
                and (now - self.request_timestamps[0]).total_seconds()
                > self.time_window_seconds
            ):
                self.request_timestamps.popleft()

            # If we've hit our request limit, wait until we can make another request
            if len(self.request_timestamps) >= self.requests_per_window:
                sleep_time = (
                    self.request_timestamps[0]
                    + timedelta(seconds=self.time_window_seconds)
                    - now
                ).total_seconds()
                if sleep_time > 0:
                    time.sleep(sleep_time + random.uniform(1, 3))  # Add jitter

    def _get_retry_delay(
        self, attempt: int, status_code: Optional[int] = None
    ) -> float:
        """Calculate delay before next retry with exponential backoff and jitter."""
        if status_code == 429:
            # Increase delay significantly on 429s
            self.consecutive_429s += 1
            base = min(
                300, self.base_delay * (3**self.consecutive_429s)
            )  # Cap at 5 minutes
        else:
            self.consecutive_429s = 0
            base = self.base_delay * (2**attempt)

        # Add randomization (30% variance)
        jitter = random.uniform(-0.3 * base, 0.3 * base)
        return base + jitter

    def _update_request_tracking(self) -> None:
        """Update request tracking after a successful request."""
        with self.lock:
            self.request_timestamps.append(datetime.now())
            self.last_successful_request = datetime.now()
            self.consecutive_429s = 0

    def get(self, url: str, headers: Optional[Dict] = None) -> requests.Response:
        """
        Make a GET request with rate limiting and retry logic.

        Args:
            url: The URL to request
            headers: Optional additional headers

        Returns:
            requests.Response object

        Raises:
            requests.exceptions.RequestException: If all retries fail
        """
        headers = headers or {}
        if "User-Agent" not in headers:
            headers["User-Agent"] = random.choice(self.user_agents)

        last_exception = None
        for attempt in range(self.max_retries):
            try:
                self._wait_for_rate_limit()

                response = self.session.get(url, headers=headers, timeout=30)
                response.raise_for_status()

                self._update_request_tracking()
                logging.debug(f"Successfully fetched URL: {url}")
                return response

            except requests.exceptions.HTTPError as e:
                last_exception = e
                if e.response.status_code == 429 and attempt < self.max_retries - 1:
                    delay = self._get_retry_delay(attempt, 429)
                    logging.warning(
                        f"Received 429 Too Many Requests for {url}. Retrying in {delay:.2f} seconds... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(delay)
                elif e.response.status_code in [404, 403] and attempt < self.max_retries - 1:
                    delay = self._get_retry_delay(attempt)
                    logging.warning(
                        f"Received {e.response.status_code} for {url}. Retrying in {delay:.2f} seconds... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(delay)
                else:
                    logging.error(f"HTTP error {e.response.status_code} for {url} after {attempt + 1} attempts")
                    raise

            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                last_exception = e
                if attempt < self.max_retries - 1:
                    delay = self._get_retry_delay(attempt)
                    logging.warning(
                        f"Connection/timeout error for {url}: {str(e)}. Retrying in {delay:.2f} seconds... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(delay)
                else:
                    logging.error(f"Connection/timeout error for {url} after {attempt + 1} attempts: {str(e)}")
                    raise

            except requests.exceptions.RequestException as e:
                last_exception = e
                if attempt < self.max_retries - 1:
                    delay = self._get_retry_delay(attempt)
                    logging.warning(
                        f"Request failed for {url}: {str(e)}. Retrying in {delay:.2f} seconds... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(delay)
                else:
                    logging.error(f"Request failed for {url} after {attempt + 1} attempts: {str(e)}")
                    raise

        # This should never be reached, but just in case
        if last_exception:
            raise last_exception
        else:
            raise requests.exceptions.RequestException(f"Failed to fetch {url} after {self.max_retries} attempts")


def _extract_team_names_from_url(url: str) -> Optional[Tuple[str, str]]:
    """
    Parses team names from a URL like .../team-one-slug-vs-team-two-slug-h2h-stats.
    """
    match = re.search(r"/([^/]+?)-vs-([^/]+?)-h2h-stats", url, re.IGNORECASE)
    if match:
        team1_slug = match.group(1)
        team2_slug = match.group(2)

        # Convert slugs to title-cased names
        team1_name = " ".join(word.capitalize() for word in team1_slug.split("-"))
        team2_name = " ".join(word.capitalize() for word in team2_slug.split("-"))
        return team1_name, team2_name
    return None


def get_head_to_head_stats(url: str, scraper: RateLimitedScraper) -> dict:
    """Get head-to-head statistics using the provided rate-limited scraper."""
    try:
        # Make the request using the provided rate-limited scraper
        logging.info(f"Fetching H2H stats from: {url}")
        response = scraper.get(url)

        # Validate response content
        if not response.content:
            logging.error(f"Empty response content for URL: {url}")
            return {}

        # Parse the response
        soup = BeautifulSoup(response.content, "html.parser")
        
        # Basic validation that we have a valid H2H page
        if not soup.find("title"):
            logging.error(f"No title tag found in response for URL: {url} - may not be a valid page")
            return {}
            
    except Exception as e:
        logging.error(f"Failed to fetch or parse H2H page {url}: {str(e)}")
        return {}

    team1_name = "team_a"
    team2_name = "team_b"

    # 1. Try extracting from URL
    url_teams = _extract_team_names_from_url(url)
    if url_teams:
        team1_name, team2_name = url_teams
    else:
        # 2. Try extracting from page title
        title_tag = soup.find("title")
        if title_tag and title_tag.string:
            title_match = re.search(r"(.+?)\s+vs\s+(.+?)\s+H2H Stats", title_tag.string, re.IGNORECASE)
            if title_match:
                team1_name = title_match.group(1).strip()
                team2_name = title_match.group(2).strip()
            else:
                logging.warning(f"Could not derive team names from title for {url}. Using default keys 'team_a', 'team_b'.")
        else:
            logging.warning(f"Could not derive team names from URL or title for {url}. Using default keys 'team_a', 'team_b'.")

    # Sanitize team names for keys
    def _sanitize_internal(name: Optional[str]) -> str:
        if not name: return "unknown"
        
        original_name = name
        
        # Normalize common diacritics (Serbian specific)
        name = name.replace('č', 'c').replace('ć', 'c').replace('š', 's').replace('đ', 'dj').replace('ž', 'z')
        name = name.replace('Č', 'C').replace('Ć', 'C').replace('Š', 'S').replace('Đ', 'Dj').replace('Ž', 'Z')

        name = name.lower()
        
        # Remove common prefixes and suffixes (whole word or with space)
        # Iteratively remove prefixes from the start
        for item in ["fk ", "fc ", "sc ", "ofk "]: # Prefixes with space
            if name.startswith(item):
                name = name[len(item):]
        
        # Iteratively remove suffixes from the end
        for item in [" fc", " sc", " united", " city", " town"]: # Suffixes with space
            if name.endswith(item):
                name = name[:-len(item)]

        # General replacements
        name = name.replace(" ", "_").replace(".", "").replace("-", "_").replace("'", "")
        
        # Remove "fk_", "fc_", "sc_", "ofk_" prefixes if they formed after underscoring
        for item in ["fk_", "fc_", "sc_", "ofk_"]:
            if name.startswith(item):
                name = name[len(item):]
        # Remove "_fc", "_sc", etc. suffixes if they formed after underscoring
        for item in ["_fc", "_sc", "_united", "_city", "_town"]:
            if name.endswith(item):
                name = name[:-len(item)]
        
        # Clean up any leading/trailing underscores from removals
        name = name.strip("_")
        
        # Try to determine league from URL for better mapping
        league_name = None
        if 'serbia' in url.lower():
            if 'prva-liga' in url.lower() or 'serbia2' in url.lower():
                league_name = 'SERBIA_PRVA_LIGA'
            elif 'superliga' in url.lower():
                league_name = 'SERBIA_SUPERLIGA'
        
        # Use the team mappings module for normalization
        normalized_name = normalize_team_name(name, league_name)
        
        if normalized_name != name:
            logging.debug(f"Mapped team '{original_name}' -> '{name}' -> '{normalized_name}' (league: {league_name})")
        
        # Handle common Serbian city/regional identifiers as fallback
        if normalized_name == name:  # No mapping found, try patterns
            serbian_city_patterns = [
                # Pattern: team_name_city -> team_name
                (r'(.+)_(beograd|belgrade|novi_sad|kragujevac|nis|leskovac|cacak|uzice|sabac|ivanjica|ruma|surdulica|mitrovica|krusevac)$', r'\1'),
                # Pattern: team_name_sremska_mitrovica -> team_name_mitrovica
                (r'(.+)_sremska_mitrovica$', r'\1_mitrovica'),
                # Pattern: radnicki_1923_kragujevac -> radnicki_1923
                (r'(.+)_1923_kragujevac$', r'\1_1923'),
                # Pattern: remove numeric years from team names
                (r'(.+)_19\d{2}$', r'\1'),
                (r'(.+)_20\d{2}$', r'\1'),
            ]
            
            for pattern, replacement in serbian_city_patterns:
                if re.match(pattern, normalized_name):
                    new_name = re.sub(pattern, replacement, normalized_name)
                    if new_name != normalized_name:
                        logging.debug(f"Applied Serbian pattern to '{original_name}': '{normalized_name}' -> '{new_name}'")
                        normalized_name = new_name
                        break
                
        return normalized_name

    s_team1_name = _sanitize_internal(team1_name)
    s_team2_name = _sanitize_internal(team2_name)

    # Determine correct dictionary keys for stats based on on-page display vs. URL-derived names
    key_for_page_left_stats = s_team1_name  # Default: page left stats belong to team1 from URL
    key_for_page_right_stats = s_team2_name # Default: page right stats belong to team2 from URL

    # Try to extract team names as displayed on the page in the H2H comparison header
    # These selectors target the team names typically on the left and right of the "VS"
    # Use multiple fallback selectors for better resilience
    TEAM_NAME_SELECTORS = [
        "section.stat-group.h2h-widget-neo div.fl.ac.teamA p.semi-bold a.black",
        "div.compare.h2h-two-teams div.fl.w40.ac a",
        "div.teamA a",
        "div.team-name-left a",
        ".h2h-team-left a",
        ".team-left .team-name",
        "h2 .team-name:first-child",
        ".fixture-header .team:first-child",
    ]
    
    on_page_left_name_raw = None
    used_selector = None
    
    for selector in TEAM_NAME_SELECTORS:
        on_page_left_name_raw = safe_extract(soup, selector, value_type=str)
        if on_page_left_name_raw and on_page_left_name_raw.strip():
            used_selector = selector
            logging.debug(f"Successfully extracted team name using selector: {selector}")
            break
    
    if on_page_left_name_raw is None or not on_page_left_name_raw.strip():
        logging.warning(
            f"Failed to find on-page left team name element using any of the fallback selectors for URL: {url}. "
            f"Tried selectors: {TEAM_NAME_SELECTORS}. Cannot reliably map H2H stats. Returning empty stats."
        )
        return {}
    # If we reach here, on_page_left_name_raw is a non-empty string.

    if on_page_left_name_raw: # This condition is now effectively 'if True:' if not returned early, kept for clarity of original structure
        s_on_page_left_name = _sanitize_internal(on_page_left_name_raw)
        logging.debug(f"Derived URL/Title teams: '{team1_name}' (s: {s_team1_name}), '{team2_name}' (s: {s_team2_name})")
        logging.debug(f"On-page Left team found: '{on_page_left_name_raw}' (s: {s_on_page_left_name})")

        if s_on_page_left_name == s_team1_name:
            logging.info(f"Mapping: Page Left ('{on_page_left_name_raw}') is URL Team1 ('{team1_name}'). Default keys correct.")
            # key_for_page_left_stats = s_team1_name (already default)
            # key_for_page_right_stats = s_team2_name (already default)
        elif s_on_page_left_name == s_team2_name:
            logging.info(f"Mapping: Page Left ('{on_page_left_name_raw}') is URL Team2 ('{team2_name}'). Swapping stat keys.")
            key_for_page_left_stats = s_team2_name
            key_for_page_right_stats = s_team1_name
        else:
            # Attempt partial match: if sanitized derived name is part of sanitized on-page name or vice-versa
            # This helps with "Team Name FC" vs "Team Name"
            if (s_team1_name in s_on_page_left_name or s_on_page_left_name in s_team1_name):
                logging.info(f"Mapping (partial): Page Left ('{on_page_left_name_raw}') matches URL Team1 ('{team1_name}'). Default keys correct.")
            elif (s_team2_name in s_on_page_left_name or s_on_page_left_name in s_team2_name):
                logging.info(f"Mapping (partial): Page Left ('{on_page_left_name_raw}') matches URL Team2 ('{team2_name}'). Swapping stat keys.")
                key_for_page_left_stats = s_team2_name
                key_for_page_right_stats = s_team1_name
            else:
                logging.warning(
                    f"Could not definitively match on-page left team '{on_page_left_name_raw}' (s: '{s_on_page_left_name}') "
                    f"with derived names '{team1_name}' (s: '{s_team1_name}') or '{team2_name}' (s: '{s_team2_name}'). "
                    "Using default assignment (Page Left -> URL Team1). Stats might be misassigned."
                )
    else:
        logging.warning(
            "Could not extract on-page left team name for mapping. "
            "Using default stat assignment (Page Left -> URL Team1). Stats might be misassigned."
        )

    h2h_stats = {
        "total_matches": 0,
        # Initialize with the dynamic keys
        f"{key_for_page_left_stats}_win_percentage": 0.0,
        f"{key_for_page_right_stats}_win_percentage": 0.0,
        "draw_percentage": 0.0,
        f"{key_for_page_left_stats}_wins": 0,
        f"{key_for_page_right_stats}_wins": 0,
        "draws": 0,
        f"{key_for_page_left_stats}_goals": 0,
        f"{key_for_page_right_stats}_goals": 0,
        "over_1_5_percentage": 0.0,
        "over_2_5_percentage": 0.0,
        "over_3_5_percentage": 0.0,
        "btts_percentage": 0.0,
        f"{key_for_page_left_stats}_clean_sheet_percentage": 0.0,
        f"{key_for_page_right_stats}_clean_sheet_percentage": 0.0,
        "recent_results": [],
    }

    # Extract total matches with fallback selectors
    total_matches_selectors = [
        "p.ac.stat-human.dark-gray span.bold.fs12e.black",
        ".total-matches .number",
        ".h2h-total-matches",
        ".matches-count",
    ]
    
    total_matches = None
    for selector in total_matches_selectors:
        total_matches = safe_extract(soup, selector, value_type=int)
        if total_matches is not None:
            logging.debug(f"Found total matches using selector: {selector}")
            break
    
    h2h_stats["total_matches"] = total_matches if total_matches is not None else 0

    # Extract win percentages with fallback selectors
    team_a_win_pct_selectors = [
        "div.fl.ac.teamA.pr.w25 span.dark-gray.fs09e",
        ".teamA .win-percentage",
        ".team-left .percentage",
        ".h2h-team-a .win-pct",
    ]
    
    team_a_win_pct_str = None
    for selector in team_a_win_pct_selectors:
        team_a_win_pct_str = safe_extract(soup, selector, value_type=str)
        if team_a_win_pct_str:
            break
    
    try:
        h2h_stats[f"{key_for_page_left_stats}_win_percentage"] = (
            float(team_a_win_pct_str.strip("()%")) if team_a_win_pct_str else 0.0
        )
    except (ValueError, AttributeError):
        h2h_stats[f"{key_for_page_left_stats}_win_percentage"] = 0.0

    team_b_win_pct_selectors = [
        "div.fl.ac.teamB.pr.w25 span.dark-gray.fs09e",
        ".teamB .win-percentage",
        ".team-right .percentage",
        ".h2h-team-b .win-pct",
    ]
    
    team_b_win_pct_str = None
    for selector in team_b_win_pct_selectors:
        team_b_win_pct_str = safe_extract(soup, selector, value_type=str)
        if team_b_win_pct_str:
            break

    try:
        h2h_stats[f"{key_for_page_right_stats}_win_percentage"] = (
            float(team_b_win_pct_str.strip("()%")) if team_b_win_pct_str else 0.0
        )
    except (ValueError, AttributeError):
        h2h_stats[f"{key_for_page_right_stats}_win_percentage"] = 0.0

    # Extract draw percentage with fallback selectors
    draw_pct_selectors = [
        "p.pa.fs09e.semi-bold.draw-line.dark-gray.mt15e.w100 span.fs09e.dBlock",
        ".draw-percentage",
        ".draws .percentage",
        ".h2h-draws .pct",
    ]
    
    draw_pct_str = None
    for selector in draw_pct_selectors:
        draw_pct_str = safe_extract(soup, selector, value_type=str)
        if draw_pct_str:
            break
    
    try:
        h2h_stats["draw_percentage"] = float(draw_pct_str.strip("()%")) if draw_pct_str else 0.0
    except (ValueError, AttributeError):
        h2h_stats["draw_percentage"] = 0.0

    # Extract number of wins and draws with fallback selectors
    team_a_wins_selectors = [
        "p.w50.semi-bold.fl.fs09e.al.pl05.bbox.dark-gray",
        ".teamA .wins-count",
        ".team-left .wins",
        ".h2h-team-a .wins",
    ]
    
    team_a_wins = None
    for selector in team_a_wins_selectors:
        team_a_wins = safe_extract(soup, selector, value_type=int)
        if team_a_wins is not None:
            break
    
    h2h_stats[f"{key_for_page_left_stats}_wins"] = team_a_wins if team_a_wins is not None else 0

    team_b_wins_selectors = [
        "p.w50.semi-bold.fr.fs09e.ar.bbox.pr05.dark-gray",
        ".teamB .wins-count",
        ".team-right .wins",
        ".h2h-team-b .wins",
    ]
    
    team_b_wins = None
    for selector in team_b_wins_selectors:
        team_b_wins = safe_extract(soup, selector, value_type=int)
        if team_b_wins is not None:
            break
    
    h2h_stats[f"{key_for_page_right_stats}_wins"] = team_b_wins if team_b_wins is not None else 0

    draws_selectors = [
        "p.pa.fs09e.semi-bold.draw-line.dark-gray.mt15e.w100",
        ".draws-count",
        ".h2h-draws .count",
        ".draw-results",
    ]
    
    draws = None
    for selector in draws_selectors:
        draws = safe_extract(soup, selector, value_type=int)
        if draws is not None:
            break
    
    h2h_stats["draws"] = draws if draws is not None else 0

    # Extract goal statistics
    goals_text = safe_extract(soup, "p.h2h-trailing-text.w90.cf.m0Auto", value_type=str)
    if goals_text:
        # Assuming the first mentioned goals are for team_a (s_team1_name) and second for team_b (s_team2_name)
        # Example: "Team A scored 5 goals in 10 matches, while Team B scored 3 goals."
        # Or: "Team A has scored 10 goals in this head-to-head fixture, while Team B has scored 5 goals"
        # Regex needs to be robust enough or make assumptions based on order.
        # The original regex was: team_a_goals = re.search(r"(\d+) goals", goals_text)
        # team_b_goals = re.search(r"and .+ scored (\d+) goals", goals_text)
        # This implies team A is mentioned first.

        # Attempt to find goals for both teams.
        # The first "(\d+) goals" match is assumed to be for the team mentioned first in the text block.
        # This text block's first team should correspond to the page's left visual block.
        team_left_goals_match = re.search(r"(\d+)\s+goals", goals_text, re.IGNORECASE)
        
        # The second goals mention, often after "while" or "and", for the team mentioned second.
        # This text block's second team should correspond to the page's right visual block.
        team_right_goals_match_text_part = goals_text.split("while")[-1] if "while" in goals_text else \
                                          goals_text.split("and")[-1] if "and" in goals_text else \
                                          "" # Fallback to empty if no clear separator
        # If team_left_goals_match exists and its end is before the start of team_right_goals_match_text_part,
        # to avoid re-matching the same number if "while" or "and" is missing.
        if team_left_goals_match and team_right_goals_match_text_part and \
           goals_text.find(team_right_goals_match_text_part, team_left_goals_match.end()) != -1:
            team_right_goals_match = re.search(r"(?:scored|managed)\s+(\d+)\s+goals", team_right_goals_match_text_part, re.IGNORECASE)
        elif team_left_goals_match: # If only one goals figure clearly found, or no "while/and"
             # Try to find a second distinct goals figure in the remainder of the text
            remaining_text_after_first_goal = goals_text[team_left_goals_match.end():]
            team_right_goals_match = re.search(r"(?:scored|managed)\s+(\d+)\s+goals", remaining_text_after_first_goal, re.IGNORECASE)
        else:
            team_right_goals_match = None


        if team_left_goals_match:
            h2h_stats[f"{key_for_page_left_stats}_goals"] = int(team_left_goals_match.group(1))
        
        if team_right_goals_match:
             h2h_stats[f"{key_for_page_right_stats}_goals"] = int(team_right_goals_match.group(1))
        # Removed the complex elif for team_b_goals_match_alt as the logic above is slightly more generalized.
        # If only one goal count is found by team_left_goals_match, team_right_goals_match will likely be None,
        # and the corresponding team's goals will remain 0 (as initialized).


    # Extract over/under and BTTS statistics
    h2h_stats["over_1_5_percentage"] = safe_extract(
        soup,
        'div.grid-item:-soup-contains("Over 1.5") div.stat-strong',
        value_type=float,
    )
    h2h_stats["over_2_5_percentage"] = safe_extract(
        soup,
        'div.grid-item:-soup-contains("Over 2.5") div.stat-strong',
        value_type=float,
    )
    h2h_stats["over_3_5_percentage"] = safe_extract(
        soup,
        'div.grid-item:-soup-contains("Over 3.5") div.stat-strong',
        value_type=float,
    )
    h2h_stats["btts_percentage"] = safe_extract(
        soup, 'div.grid-item:-soup-contains("BTTS") div.stat-strong', value_type=float
    )
    # Assuming first "Clean Sheets" is for Team A / Home Team (s_team1_name)
    # The first 'div.grid-item:-soup-contains("Clean Sheets") div.stat-strong' corresponds to the page left team.
    first_cs_stat = safe_extract(
        soup,
        'div.grid-item:-soup-contains("Clean Sheets") div.stat-strong',
        value_type=float,
    )
    h2h_stats[f"{key_for_page_left_stats}_clean_sheet_percentage"] = first_cs_stat

    # The second "Clean Sheets" stat.
    second_cs_stat = None
    cs_elements = soup.select('div.grid-item:-soup-contains("Clean Sheets") div.stat-strong')
    if len(cs_elements) > 1:
         second_cs_stat = safe_extract(
            cs_elements[1],
            None,
            value_type=float,
            use_direct_element=True
        )
    # The :nth-of-type(6) selector was too specific and potentially fragile.
    # If cs_elements doesn't yield a second distinct element, second_cs_stat remains None,
    # and the corresponding team's clean sheet percentage remains 0.0.
    # A more robust way would be to ensure the two cs_elements are from different parent blocks if possible,
    # or rely on them being the first two distinct "Clean Sheets" stats in the relevant section.
    h2h_stats[f"{key_for_page_right_stats}_clean_sheet_percentage"] = second_cs_stat


    # Extract recent head-to-head results
    fixtures_container = soup.find("div", class_="sliding-fixtures pr")
    results = []
    if fixtures_container:  # Check if fixtures_container is not None
        for fixture in fixtures_container.find_all(
            "a", class_="fixture changeH2HDataButton_neo"
        ):
            date_tag = fixture.find("time", class_="timezone-convert-match-h2h-neo")
            date = date_tag.text.strip() if date_tag else "N/A"
            
            teams_elements = fixture.find_all(
                "div",
                class_=lambda x: x and ("team black" in x or "team winner black" in x),
            )
            if len(teams_elements) >= 2:
                home_team_full = teams_elements[0].text.strip()
                home_score_tag = teams_elements[0].find("span")
                home_score = home_score_tag.text.strip() if home_score_tag else "N/A"
                # Extract only the team name part, excluding score
                home_team = home_team_full.replace(home_score, "").strip()


                away_team_full = teams_elements[1].text.strip()
                away_score_tag = teams_elements[1].find("span")
                away_score = away_score_tag.text.strip() if away_score_tag else "N/A"
                away_team = away_team_full.replace(away_score, "").strip()
                
                results.append([date, home_team, home_score, away_team, away_score])
        h2h_stats["recent_results"] = results
    else:
        logging.warning("No fixtures container found for URL: %s", url)
        h2h_stats["recent_results"] = []

    # Validate and log the extracted data
    _validate_h2h_stats(h2h_stats, url, on_page_left_name_raw, key_for_page_left_stats, key_for_page_right_stats)

    return h2h_stats


def _validate_h2h_stats(stats: dict, url: str, team_name: str, left_key: str, right_key: str) -> None:
    """
    Validate extracted H2H statistics and log any issues.
    
    Args:
        stats: The extracted statistics dictionary
        url: The URL that was scraped
        team_name: The team name found on the page
        left_key: The key for left team stats
        right_key: The key for right team stats
    """
    issues = []
    
    # Check if we have basic data
    if stats.get("total_matches", 0) == 0:
        issues.append("No total matches found")
    
    # Check if wins add up logically
    left_wins = stats.get(f"{left_key}_wins", 0)
    right_wins = stats.get(f"{right_key}_wins", 0)
    draws = stats.get("draws", 0)
    total = stats.get("total_matches", 0)
    
    if total > 0 and (left_wins + right_wins + draws) != total:
        issues.append(f"Win/draw counts don't match total: {left_wins} + {right_wins} + {draws} != {total}")
    
    # Check percentages
    left_pct = stats.get(f"{left_key}_win_percentage", 0)
    right_pct = stats.get(f"{right_key}_win_percentage", 0)
    draw_pct = stats.get("draw_percentage", 0)
    
    total_pct = left_pct + right_pct + draw_pct
    if total_pct > 0 and abs(total_pct - 100.0) > 1.0:  # Allow 1% tolerance for rounding
        issues.append(f"Percentages don't add to 100%: {left_pct} + {right_pct} + {draw_pct} = {total_pct}")
    
    if issues:
        logging.warning(f"H2H data validation issues for {url} (team: {team_name}): {'; '.join(issues)}")
        logging.debug(f"Extracted stats: {stats}")
    else:
        logging.info(f"Successfully extracted H2H stats for {team_name}: {total} matches, {left_wins}-{draws}-{right_wins}")