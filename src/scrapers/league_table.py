from bs4 import BeautifulSoup
from typing import List
from team_stats import RateLimitedScraper
import logging
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By


def get_league_table(url: str, scraper: RateLimitedScraper) -> List[dict]:
    """
    Get league table data using rate-limited scraper.

    Args:
        url: URL of the league table page
        scraper: RateLimitedScraper instance for making requests

    Returns:
        List of dictionaries containing league table data
    """
    page_source = scraper.get(url)
    if page_source is None:
        return []

    try:
        # Wait for the table to be present on the page
        WebDriverWait(scraper.driver, 30).until(
            EC.presence_of_element_located((By.CLASS_NAME, "full-league-table"))
        )
    except Exception as e:
        logging.warning(f"Timeout waiting for league table on page {url}: {e}")
        return []

    # Re-parse the page source after waiting for the table
    page_source = scraper.driver.page_source
    soup = BeautifulSoup(page_source, "html.parser")

    league_table = []

    table = soup.find("table", class_="full-league-table")
    
    if not table:
        logging.warning(f"Could not find league table with class 'full-league-table' on page {url}.")
        return []

    league_table = []

    rows = table.find_all("tr")[1:]  # Skip the header row
    for row in rows:
        cells = row.find_all("td")
        if len(cells) < 11: # Ensure there are enough cells
            logging.warning(f"Skipping row due to insufficient cells: {len(cells)}")
            continue

        try:
            position = cells[0].text.strip()
            team = cells[2].text.strip()
            mp = int(cells[3].text.strip() or 0)
            win = int(cells[4].text.strip() or 0)
            draw = int(cells[5].text.strip() or 0)
            loss = int(cells[6].text.strip() or 0)
            gf = int(cells[7].text.strip() or 0)
            ga = int(cells[8].text.strip() or 0)
            gd = int(cells[9].text.strip() or 0)
            pts = int(cells[10].text.strip() or 0)
            
            league_table.append({
                'Position': position,
                'Team': team,
                'MP': mp,
                'W': win,
                'D': draw,
                'L': loss,
                'GF': gf,
                'GA': ga,
                'GD': gd,
                'Pts': pts
            })
        except ValueError as e:
            logging.warning(f"Error parsing cell data: {e}. Row content: {[c.text.strip() for c in cells]}")
            continue

    return league_table
