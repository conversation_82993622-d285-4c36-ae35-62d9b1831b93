"""
Team name mapping configurations for different leagues.
This module provides standardized team name mappings to handle variations
in team names across different data sources and websites.
"""

# Serbia Prva Liga team name mappings
SERBIA_PRVA_LIGA_MAPPINGS = {
    # Standard mappings from full names to simplified keys
    'fk_borac_cacak': 'borac_cacak',
    'borac_cacak': 'borac_cacak',
    'borac': 'borac_cacak',
    
    'fk_dubocica_leskovac': 'dubocica',
    'dubocica_leskovac': 'dubocica',
    'dubocica': 'dubocica',
    
    'fk_graficar_beograd': 'graficar',
    'graficar_beograd': 'graficar',
    'graficar': 'graficar',
    
    'fk_indija': 'indija',
    'indija': 'indija',
    
    'fk_javor_ivanjica': 'javor_ivanjica',
    'javor_ivanjica': 'javor_ivanjica',
    'javor': 'javor_ivanjica',
    
    'fk_macva_sabac': 'macva_sabac',
    'macva_sabac': 'macva_sabac',
    'macva': 'macva_sabac',
    
    'fk_mladost_novi_sad': 'mladost_novi_sad',
    'mladost_novi_sad': 'mladost_novi_sad',
    'mladost': 'mladost_novi_sad',
    
    'fk_radnicki_sremska_mitrovica': 'radnicki_mitrovica',
    'radnicki_sremska_mitrovica': 'radnicki_mitrovica',
    'radnicki_mitrovica': 'radnicki_mitrovica',
    's_mitrovica': 'radnicki_mitrovica',
    
    'fk_radnik_surdulica': 'radnik_surdulica',
    'radnik_surdulica': 'radnik_surdulica',
    'r_surdulica': 'radnik_surdulica',
    
    'fk_semendrija_1924': 'semendrija',
    'semendrija_1924': 'semendrija',
    'semendrija': 'semendrija',
    'smederevo': 'semendrija',
    
    'fk_sloboda_uzice': 'sloboda_uzice',
    'sloboda_uzice': 'sloboda_uzice',
    'sloboda': 'sloboda_uzice',
    
    'fk_sloven_ruma': 'sloven_ruma',
    'sloven_ruma': 'sloven_ruma',
    'sloven': 'sloven_ruma',
    
    'fk_vozdovac': 'vozdovac',
    'vozdovac': 'vozdovac',
    
    'fk_zemun': 'zemun',
    'zemun': 'zemun',
    
    'ofk_vrsac': 'vrsac',
    'vrsac': 'vrsac',
    
    'trajal_krusevac': 'trajal_krusevac',
    'trajal': 'trajal_krusevac',
}

# Serbia Superliga team name mappings
SERBIA_SUPERLIGA_MAPPINGS = {
    'fk_backa_topola': 'backa_topola',
    'backa_topola': 'backa_topola',
    
    'fk_cukaricki_stankom': 'cukaricki',
    'cukaricki_stankom': 'cukaricki',
    'cukaricki': 'cukaricki',
    
    'fk_imt_novi_beograd': 'imt_novi_beograd',
    'imt_novi_beograd': 'imt_novi_beograd',
    'imt_n_beograd': 'imt_novi_beograd',
    
    'fk_jedinstvo_ub': 'jedinstvo_ub',
    'jedinstvo_ub': 'jedinstvo_ub',
    'jedinstvo_u': 'jedinstvo_ub',
    
    'fk_mladost_lucani': 'mladost_lucani',
    'mladost_lucani': 'mladost_lucani',
    
    'fk_napredak_krusevac': 'napredak',
    'napredak_krusevac': 'napredak',
    'napredak': 'napredak',
    
    'fk_novi_pazar': 'novi_pazar',
    'novi_pazar': 'novi_pazar',
    
    'ofk_beograd': 'ofk_beograd',
    'beograd': 'ofk_beograd',
    
    'fk_partizan_beograd': 'partizan',
    'partizan_beograd': 'partizan',
    'partizan': 'partizan',
    
    'fk_radnicki_1923_kragujevac': 'radnicki_1923',
    'radnicki_1923_kragujevac': 'radnicki_1923',
    'radnicki_1923': 'radnicki_1923',
    
    'fk_radnicki_nis': 'radnicki_nis',
    'radnicki_nis': 'radnicki_nis',
    
    'red_star_belgrade': 'red_star',
    'red_star': 'red_star',
    
    'fk_spartak_subotica': 'spartak_subotica',
    'spartak_subotica': 'spartak_subotica',
    's_subotica': 'spartak_subotica',
    
    'fk_tekstilac_odzaci': 'tekstilac_odzaci',
    'tekstilac_odzaci': 'tekstilac_odzaci',
    'tekstilac_odzac': 'tekstilac_odzaci',
    
    'fk_vojvodina_novi_sad': 'vojvodina',
    'vojvodina_novi_sad': 'vojvodina',
    'vojvodina': 'vojvodina',
    
    'fk_zeleznicar_pancevo': 'zeleznicar_pancevo',
    'zeleznicar_pancevo': 'zeleznicar_pancevo',
    'z_pancevo': 'zeleznicar_pancevo',
}

# Combined mappings for all Serbian leagues
SERBIAN_TEAM_MAPPINGS = {
    **SERBIA_PRVA_LIGA_MAPPINGS,
    **SERBIA_SUPERLIGA_MAPPINGS,
}

def get_team_mapping(league_name: str) -> dict:
    """
    Get team name mappings for a specific league.
    
    Args:
        league_name: Name of the league (e.g., 'SERBIA_PRVA_LIGA', 'SERBIA_SUPERLIGA')
    
    Returns:
        Dictionary of team name mappings for the league
    """
    mappings = {
        'SERBIA_PRVA_LIGA': SERBIA_PRVA_LIGA_MAPPINGS,
        'SERBIA_SUPERLIGA': SERBIA_SUPERLIGA_MAPPINGS,
        'SERBIAN': SERBIAN_TEAM_MAPPINGS,  # Combined Serbian mappings
    }
    
    return mappings.get(league_name.upper(), {})

def normalize_team_name(team_name: str, league_name: str = None) -> str:
    """
    Normalize a team name using league-specific mappings.
    
    Args:
        team_name: The team name to normalize
        league_name: Optional league name for specific mappings
    
    Returns:
        Normalized team name
    """
    if not team_name:
        return "unknown"
    
    # Basic normalization
    normalized = team_name.lower().strip()
    normalized = normalized.replace(" ", "_").replace("-", "_").replace(".", "")
    
    # Apply league-specific mappings if available
    if league_name:
        mappings = get_team_mapping(league_name)
        if normalized in mappings:
            return mappings[normalized]
    
    # Apply general Serbian mappings as fallback
    if normalized in SERBIAN_TEAM_MAPPINGS:
        return SERBIAN_TEAM_MAPPINGS[normalized]
    
    return normalized
