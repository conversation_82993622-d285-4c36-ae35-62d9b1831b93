import time
import random
import logging
import requests
import re
from bs4 import BeautifulSoup
from collections import deque
from datetime import datetime, timedelta
import threading
from typing import Optional, Dict, Deque, Tuple
from utils import safe_extract
from team_mappings import get_team_mapping, normalize_team_name, SERBIAN_TEAM_MAPPINGS
try:
    from config import CURRENT_CONFIG
except ImportError:
    CURRENT_CONFIG = {}

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
    "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:54.0) Gecko/20100101 Firefox/54.0",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:94.0) Gecko/20100101 Firefox/94.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Edg/95.0.1020.53",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64; rv:94.0) Gecko/20100101 Firefox/94.0",
]


class RateLimitedScraper:
    def __init__(
        self,
        requests_per_window: int = 10,
        time_window_seconds: int = 60,
        base_delay: float = 5.0,
        max_retries: int = 30,
        user_agents: list = None,
    ):
        """
        Initialize the rate-limited scraper with configurable parameters.
        """
        self.requests_per_window = requests_per_window
        self.time_window_seconds = time_window_seconds
        self.base_delay = base_delay
        self.max_retries = max_retries

        # Request tracking
        self.request_timestamps: Deque[datetime] = deque(maxlen=requests_per_window)
        self.lock = threading.Lock()

        # Session management
        self.session = requests.Session()
        self.user_agents = user_agents or USER_AGENTS

        # Error tracking
        self.consecutive_429s = 0
        self.last_successful_request: Optional[datetime] = None

    def _wait_for_rate_limit(self) -> None:
        """Ensure we don't exceed the rate limit by waiting if necessary."""
        with self.lock:
            now = datetime.now()

            # Remove timestamps older than our time window
            while (
                self.request_timestamps
                and (now - self.request_timestamps[0]).total_seconds()
                > self.time_window_seconds
            ):
                self.request_timestamps.popleft()

            # If we've hit our request limit, wait until we can make another request
            if len(self.request_timestamps) >= self.requests_per_window:
                sleep_time = (
                    self.request_timestamps[0]
                    + timedelta(seconds=self.time_window_seconds)
                    - now
                ).total_seconds()
                if sleep_time > 0:
                    time.sleep(sleep_time + random.uniform(1, 3))  # Add jitter

    def _get_retry_delay(
        self, attempt: int, status_code: Optional[int] = None
    ) -> float:
        """Calculate delay before next retry with exponential backoff and jitter."""
        if status_code == 429:
            # Increase delay significantly on 429s
            self.consecutive_429s += 1
            base = min(
                300, self.base_delay * (3**self.consecutive_429s)
            )  # Cap at 5 minutes
        else:
            self.consecutive_429s = 0
            base = self.base_delay * (2**attempt)

        # Add randomization (30% variance)
        jitter = random.uniform(-0.3 * base, 0.3 * base)
        return base + jitter

    def _update_request_tracking(self) -> None:
        """Update request tracking after a successful request."""
        with self.lock:
            self.request_timestamps.append(datetime.now())
            self.last_successful_request = datetime.now()
            self.consecutive_429s = 0

    def get(self, url: str, headers: Optional[Dict] = None) -> requests.Response:
        """
        Make a GET request with rate limiting and retry logic.
        """
        headers = headers or {}
        if "User-Agent" not in headers:
            headers["User-Agent"] = random.choice(self.user_agents)

        last_exception = None
        for attempt in range(self.max_retries):
            try:
                self._wait_for_rate_limit()

                response = self.session.get(url, headers=headers, timeout=30)
                response.raise_for_status()

                self._update_request_tracking()
                logging.debug(f"Successfully fetched URL: {url}")
                return response

            except requests.exceptions.HTTPError as e:
                last_exception = e
                if e.response.status_code == 429 and attempt < self.max_retries - 1:
                    delay = self._get_retry_delay(attempt, 429)
                    logging.warning(
                        f"Received 429 Too Many Requests for {url}. Retrying in {delay:.2f} seconds... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(delay)
                elif e.response.status_code in [404, 403] and attempt < self.max_retries - 1:
                    delay = self._get_retry_delay(attempt)
                    logging.warning(
                        f"Received {e.response.status_code} for {url}. Retrying in {delay:.2f} seconds... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(delay)
                else:
                    logging.error(f"HTTP error {e.response.status_code} for {url} after {attempt + 1} attempts")
                    raise

            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                last_exception = e
                if attempt < self.max_retries - 1:
                    delay = self._get_retry_delay(attempt)
                    logging.warning(
                        f"Connection/timeout error for {url}: {str(e)}. Retrying in {delay:.2f} seconds... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(delay)
                else:
                    logging.error(f"Connection/timeout error for {url} after {attempt + 1} attempts: {str(e)}")
                    raise

            except requests.exceptions.RequestException as e:
                last_exception = e
                if attempt < self.max_retries - 1:
                    delay = self._get_retry_delay(attempt)
                    logging.warning(
                        f"Request failed for {url}: {str(e)}. Retrying in {delay:.2f} seconds... (attempt {attempt + 1}/{self.max_retries})"
                    )
                    time.sleep(delay)
                else:
                    logging.error(f"Request failed for {url} after {attempt + 1} attempts: {str(e)}")
                    raise

        # This should never be reached, but just in case
        if last_exception:
            raise last_exception
        else:
            raise requests.exceptions.RequestException(f"Failed to fetch {url} after {self.max_retries} attempts")


def safe_extract_text(element, default=None):
    """Safely extracts and strips text from a BeautifulSoup element."""
    return element.get_text(strip=True) if element else default


def safe_extract_number(text, default=None):
    """Safely extracts the first number (integer or float) from a string."""
    if text:
        match = re.search(r'(\d+\.?\d*)', text)
        if match:
            return match.group(1)
    return default


def get_head_to_head_stats(url: str, scraper: RateLimitedScraper) -> dict:
    """
    Get head-to-head statistics using improved extraction logic from web_scraping.py.
    This version fixes the team assignment issue and uses better selectors.
    """
    try:
        # Make the request using the provided rate-limited scraper
        logging.info(f"Fetching H2H stats from: {url}")
        response = scraper.get(url)

        # Validate response content
        if not response.content:
            logging.error(f"Empty response content for URL: {url}")
            return {}

        # Parse the response
        soup = BeautifulSoup(response.content, "html.parser")
        
        # Basic validation that we have a valid H2H page
        if not soup.find("title"):
            logging.error(f"No title tag found in response for URL: {url} - may not be a valid page")
            return {}
            
    except Exception as e:
        logging.error(f"Failed to fetch or parse H2H page {url}: {str(e)}")
        return {}

    # --- IMPROVED DATA EXTRACTION LOGIC FROM web_scraping.py ---
    
    # Find the main H2H widget container
    h2h_widget = soup.select_one('section.h2h-widget-neo')
    if not h2h_widget:
        logging.error(f"CRITICAL: Could not find the main H2H widget container for {url}. Aborting.")
        return {}

    # Step 1: DYNAMICALLY IDENTIFY HOME AND AWAY TEAMS FROM PAGE CONTENT
    home_team_container = h2h_widget.select_one('div.teamA')
    away_team_container = h2h_widget.select_one('div.teamB')
    
    home_team_name = safe_extract_text(home_team_container.select_one('a.black')) if home_team_container else None
    away_team_name = safe_extract_text(away_team_container.select_one('a.black')) if away_team_container else None
    
    logging.info(f"Dynamically Identified Home Team: {home_team_name} | Away Team: {away_team_name}")

    if not home_team_name or not away_team_name:
        logging.error(f"CRITICAL: Could not identify team names from the page for {url}. Aborting.")
        return {}

    # Step 2: Extract main H2H statistics with improved selectors
    home_win_percentage = None
    away_win_percentage = None
    if home_team_container and away_team_container:
        home_win_pct_elem = home_team_container.select_one('span.dark-gray')
        away_win_pct_elem = away_team_container.select_one('span.dark-gray')
        home_win_percentage = safe_extract_number(home_win_pct_elem.text) if home_win_pct_elem else None
        away_win_percentage = safe_extract_number(away_win_pct_elem.text) if away_win_pct_elem else None

    # Total matches
    total_matches_elem = h2h_widget.select_one('p.stat-human span.bold')
    total_matches = safe_extract_number(total_matches_elem.text) if total_matches_elem else None

    # Wins for each team
    wins_elements = h2h_widget.select('p.w50.semi-bold')
    home_wins = safe_extract_number(wins_elements[0].text) if len(wins_elements) > 0 else None
    away_wins = safe_extract_number(wins_elements[1].text) if len(wins_elements) > 1 else None

    # Draws
    draw_container = h2h_widget.select_one('p.draw-line')
    draws = safe_extract_number(draw_container.text) if draw_container else None
    draw_percentage_elem = draw_container.select_one('span') if draw_container else None
    draw_percentage = safe_extract_number(draw_percentage_elem.text) if draw_percentage_elem else None

    # *** DEFINITIVE FIX FOR GOALS BASED ON USER INSIGHT ***
    # Extract goals using regex from summary text
    home_goals, away_goals = None, None
    summary_paragraph = soup.select_one('p.h2h-trailing-text')
    if summary_paragraph:
        summary_text = summary_paragraph.text
        # Regex to find "{Team Name} scored {number} goals"
        home_pattern = re.compile(f"{re.escape(home_team_name)} scored (\\d+) goals", re.IGNORECASE)
        away_pattern = re.compile(f"{re.escape(away_team_name)} scored (\\d+) goals", re.IGNORECASE)
        
        home_match = home_pattern.search(summary_text)
        away_match = away_pattern.search(summary_text)
        
        if home_match:
            home_goals = int(home_match.group(1))
        if away_match:
            away_goals = int(away_match.group(1))

    # Extract Over/Under, BTTS, and Clean Sheets statistics
    stats_grid = soup.select_one('div.stat-grid')
    over_1_5_percentage, over_2_5_percentage, over_3_5_percentage, btts_percentage = None, None, None, None
    home_cs_percentage, away_cs_percentage = None, None
    
    if stats_grid:
        def get_stat_from_grid(label_text, parent_soup):
            element = parent_soup.find(string=re.compile(f"^{label_text}$", re.I))
            if element:
                grid_item = element.find_parent('div', class_='grid-item')
                if grid_item:
                    stat_div = grid_item.select_one('div.stat-strong')
                    return safe_extract_text(stat_div)
            return None
        
        over_1_5_percentage = safe_extract_number(get_stat_from_grid('Over 1.5', stats_grid))
        over_2_5_percentage = safe_extract_number(get_stat_from_grid('Over 2.5', stats_grid))
        over_3_5_percentage = safe_extract_number(get_stat_from_grid('Over 3.5', stats_grid))
        btts_percentage = safe_extract_number(get_stat_from_grid('BTTS', stats_grid))
        
        # Extract clean sheets for each team
        all_grid_items = stats_grid.select('.grid-item')
        for item in all_grid_items:
            item_text = item.get_text()
            if "Clean Sheets" in item_text:
                stat_strong = item.select_one('.stat-strong')
                if stat_strong and home_team_name in item_text:
                    home_cs_percentage = safe_extract_number(stat_strong.text)
                elif stat_strong and away_team_name in item_text:
                    away_cs_percentage = safe_extract_number(stat_strong.text)

    # Extract recent results
    recent_results_elements = soup.select('div.sliding-fixtures .fixture')
    recent_results = []
    for fixture in recent_results_elements:
        date_elem = fixture.select_one('time')
        date = safe_extract_text(date_elem) if date_elem else "N/A"
        
        teams = fixture.select('.team')
        if len(teams) >= 2:
            home_team_recent = " ".join(teams[0].get_text(strip=True).split())
            away_team_recent = " ".join(teams[1].get_text(strip=True).split())
            recent_results.append(f"{date}: {home_team_recent} vs {away_team_recent}")

    # --- STANDARDIZED DATA STRUCTURE ---
    # Use consistent home/away structure instead of dynamic team names
    h2h_stats = {
        'home_team_name': home_team_name,
        'away_team_name': away_team_name,
        'total_matches': int(total_matches) if total_matches else 0,
        'home_win_percentage': float(home_win_percentage) if home_win_percentage else 0.0,
        'away_win_percentage': float(away_win_percentage) if away_win_percentage else 0.0,
        'draw_percentage': float(draw_percentage) if draw_percentage else 0.0,
        'home_wins': int(home_wins) if home_wins else 0,
        'away_wins': int(away_wins) if away_wins else 0,
        'draws': int(draws) if draws else 0,
        'home_goals': home_goals if home_goals is not None else 0,
        'away_goals': away_goals if away_goals is not None else 0,
        'over_1_5_percentage': float(over_1_5_percentage) if over_1_5_percentage else 0.0,
        'over_2_5_percentage': float(over_2_5_percentage) if over_2_5_percentage else 0.0,
        'over_3_5_percentage': float(over_3_5_percentage) if over_3_5_percentage else 0.0,
        'btts_percentage': float(btts_percentage) if btts_percentage else 0.0,
        'home_clean_sheet_percentage': float(home_cs_percentage) if home_cs_percentage else 0.0,
        'away_clean_sheet_percentage': float(away_cs_percentage) if away_cs_percentage else 0.0,
        'recent_results': " | ".join(recent_results) if recent_results else ""
    }

    # Validate extracted data
    total = h2h_stats.get("total_matches", 0)
    wins_total = h2h_stats.get("home_wins", 0) + h2h_stats.get("away_wins", 0) + h2h_stats.get("draws", 0)
    
    if total > 0 and wins_total != total:
        logging.warning(f"H2H data validation issue for {url}: win/draw counts don't match total: {wins_total} != {total}")
    else:
        logging.info(f"Successfully extracted H2H stats for {home_team_name} vs {away_team_name}: {total} matches")

    return h2h_stats


# Validation function for the extracted data
def _validate_h2h_stats(stats: dict, url: str, home_team: str, away_team: str) -> None:
    """
    Validate extracted H2H statistics and log any issues.
    """
    issues = []
    
    # Check if we have basic data
    if stats.get("total_matches", 0) == 0:
        issues.append("No total matches found")
    
    # Check if wins add up logically
    home_wins = stats.get("home_wins", 0)
    away_wins = stats.get("away_wins", 0)
    draws = stats.get("draws", 0)
    total = stats.get("total_matches", 0)
    
    if total > 0 and (home_wins + away_wins + draws) != total:
        issues.append(f"Win/draw counts don't match total: {home_wins} + {away_wins} + {draws} != {total}")
    
    # Check percentages
    home_pct = stats.get("home_win_percentage", 0)
    away_pct = stats.get("away_win_percentage", 0)
    draw_pct = stats.get("draw_percentage", 0)
    
    total_pct = home_pct + away_pct + draw_pct
    if total_pct > 0 and abs(total_pct - 100.0) > 1.0:  # Allow 1% tolerance for rounding
        issues.append(f"Percentages don't add to 100%: {home_pct} + {away_pct} + {draw_pct} = {total_pct}")
    
    if issues:
        logging.warning(f"H2H data validation issues for {url} ({home_team} vs {away_team}): {'; '.join(issues)}")
        logging.debug(f"Extracted stats: {stats}")
    else:
        logging.info(f"Successfully validated H2H stats for {home_team} vs {away_team}: {total} matches, {home_wins}-{draws}-{away_wins}")