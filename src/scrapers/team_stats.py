from bs4 import BeautifulSoup
import requests
import time
import random
import logging
from utils import safe_extract
# from collections import deque # No longer needed here
# from datetime import datetime, timedelta # No longer needed here
# import threading # No longer needed here
# from typing import Optional, Dict, Deque # No longer needed here
from results_scraper import RateLimitedScraper, USER_AGENTS # Import from results_scraper


# List of user agents - This is now imported from results_scraper, can be removed if not used elsewhere exclusively in this file
# USER_AGENTS = [
#     "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
#     "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:54.0) Gecko/20100101 Firefox/54.0",
#     "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36",
#     "Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
#     "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:94.0) Gecko/20100101 Firefox/94.0",
#     "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
#     "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Edg/95.0.1020.53",
#     "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
#     "Mozilla/5.0 (X11; Linux x86_64; rv:94.0) Gecko/20100101 Firefox/94.0",
#     "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Vivaldi/4.3",
#     "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Vivaldi/4.3",
#     "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
#     "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/18.17763 Safari/537.36",
#     "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15",
#     "Mozilla/5.0 (Linux; Android 11; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Mobile Safari/537.36",
#     "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:88.0) Gecko/20100101 Firefox/88.0",
#     "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
#     "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
#     "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36",
#     "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36",
#     "Mozilla/5.0 (Windows NT 10.0; Win64; x64; Trident/7.0; rv:11.0) like Gecko",
# ]

# The RateLimitedScraper class is now imported from results_scraper.py
# class RateLimitedScraper:
#     def __init__(
#         self,
#         requests_per_window: int = 10,
#         time_window_seconds: int = 60,
#         base_delay: float = 5.0,
#         max_retries: int = 30,
#         user_agents: list = None,
#     ):
#         """
#         Initialize the rate-limited scraper with configurable parameters.
#
#         Args:
#             requests_per_window: Maximum number of requests allowed in the time window
#             time_window_seconds: Time window in seconds for rate limiting
#             base_delay: Base delay between requests in seconds
#             max_retries: Maximum number of retry attempts
#             user_agents: List of user agent strings to rotate through
#         """
#         self.requests_per_window = requests_per_window
#         self.time_window_seconds = time_window_seconds
#         self.base_delay = base_delay
#         self.max_retries = max_retries
#
#         # Request tracking
#         self.request_timestamps: Deque[datetime] = deque(maxlen=requests_per_window)
#         self.lock = threading.Lock()
#
#         # Session management
#         self.session = requests.Session()
#         self.user_agents = user_agents or [
#             "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
#             # ... add more user agents as needed
#         ]
#
#         # Error tracking
#         self.consecutive_429s = 0
#         self.last_successful_request: Optional[datetime] = None
#
#     def _wait_for_rate_limit(self) -> None:
#         """Ensure we don't exceed the rate limit by waiting if necessary."""
#         with self.lock:
#             now = datetime.now()
#
#             # Remove timestamps older than our time window
#             while (
#                 self.request_timestamps
#                 and (now - self.request_timestamps[0]).total_seconds()
#                 > self.time_window_seconds
#             ):
#                 self.request_timestamps.popleft()
#
#             # If we've hit our request limit, wait until we can make another request
#             if len(self.request_timestamps) >= self.requests_per_window:
#                 sleep_time = (
#                     self.request_timestamps[0]
#                     + timedelta(seconds=self.time_window_seconds)
#                     - now
#                 ).total_seconds()
#                 if sleep_time > 0:
#                     time.sleep(sleep_time + random.uniform(1, 3))  # Add jitter
#
#     def _get_retry_delay(
#         self, attempt: int, status_code: Optional[int] = None
#     ) -> float:
#         """Calculate delay before next retry with exponential backoff and jitter."""
#         if status_code == 429:
#             # Increase delay significantly on 429s
#             self.consecutive_429s += 1
#             base = min(
#                 300, self.base_delay * (3**self.consecutive_429s)
#             )  # Cap at 5 minutes
#         else:
#             self.consecutive_429s = 0
#             base = self.base_delay * (2**attempt)
#
#         # Add randomization (30% variance)
#         jitter = random.uniform(-0.3 * base, 0.3 * base)
#         return base + jitter
#
#     def _update_request_tracking(self) -> None:
#         """Update request tracking after a successful request."""
#         with self.lock:
#             self.request_timestamps.append(datetime.now())
#             self.last_successful_request = datetime.now()
#             self.consecutive_429s = 0
#
#     def get(self, url: str, headers: Optional[Dict] = None) -> requests.Response:
#         """
#         Make a GET request with rate limiting and retry logic.
#
#         Args:
#             url: The URL to request
#             headers: Optional additional headers
#
#         Returns:
#             requests.Response object
#
#         Raises:
#             requests.exceptions.RequestException: If all retries fail
#         """
#         headers = headers or {}
#         if "User-Agent" not in headers:
#             headers["User-Agent"] = random.choice(self.user_agents)
#
#         for attempt in range(self.max_retries):
#             try:
#                 self._wait_for_rate_limit()
#
#                 response = self.session.get(url, headers=headers)
#                 response.raise_for_status()
#
#                 self._update_request_tracking()
#                 return response
#
#             except requests.exceptions.HTTPError as e:
#                 if e.response.status_code == 429 and attempt < self.max_retries - 1:
#                     delay = self._get_retry_delay(attempt, 429)
#                     logging.warning(
#                         f"Received 429 Too Many Requests. Retrying in {delay:.2f} seconds..."
#                     )
#                     time.sleep(delay)
#                 else:
#                     raise
#
#             except requests.exceptions.RequestException as e:
#                 if attempt < self.max_retries - 1:
#                     delay = self._get_retry_delay(attempt)
#                     logging.warning(
#                         f"Request failed due to {str(e)}. Retrying in {delay:.2f} seconds..."
#                     )
#                     time.sleep(delay)
#                 else:
#                     raise


def get_team_stats(team_url: str, team_name: str, scraper: RateLimitedScraper) -> dict:
    response = scraper.get(team_url)
    if response is None: # Handle cases where the scraper returns None (e.g. due to HTTP errors)
        logging.error(f"Failed to get stats for team {team_name} from URL: {team_url}")
        return {} # Return an empty dict or appropriate default
    soup = BeautifulSoup(response, "html.parser")

    team_stats = {}

    stats_table = soup.select_one(
        'table[cellspacing="0"][cellpadding="1"][width="100%"][style*="margin-top:0px"]'
    )
    if stats_table:
        team_stats["total_home_played"] = safe_extract(
            stats_table, 'td:-soup-contains("Home") + td', value_type=int
        )
        team_stats["total_home_wins"] = safe_extract(
            stats_table, 'td:-soup-contains("Home") + td + td', value_type=int
        )
        team_stats["total_home_draws"] = safe_extract(
            stats_table,
            'td:-soup-contains("Home") + td + td + td',
            value_type=int,
        )
        team_stats["total_home_losses"] = safe_extract(
            stats_table,
            'td:-soup-contains("Home") + td + td + td + td',
            value_type=int,
        )
        team_stats["total_away_played"] = safe_extract(
            stats_table, 'td:-soup-contains("Away") + td', value_type=int
        )
        team_stats["total_away_wins"] = safe_extract(
            stats_table, 'td:-soup-contains("Away") + td + td', value_type=int
        )
        team_stats["total_away_draws"] = safe_extract(
            stats_table,
            'td:-soup-contains("Away") + td + td + td',
            value_type=int,
        )
        team_stats["total_away_losses"] = safe_extract(
            stats_table,
            'td:-soup-contains("Away") + td + td + td + td',
            value_type=int,
        )
        team_stats["total_played"] = safe_extract(
            stats_table, 'td:-soup-contains("Total") + td', value_type=int
        )
        team_stats["total_wins"] = safe_extract(
            stats_table, 'td:-soup-contains("Total") + td + td', value_type=int
        )
        team_stats["total_draws"] = safe_extract(
            stats_table,
            'td:-soup-contains("Total") + td + td + td',
            value_type=int,
        )
        team_stats["total_losses"] = safe_extract(
            stats_table,
            'td:-soup-contains("Total") + td + td + td + td',
            value_type=int,
        )
        team_stats["points_per_game"] = safe_extract(
            stats_table,
            'td:-soup-contains("Total") + td + td + td + td + td',
            value_type=float,
        )
    team_stats["home_points_per_game"] = safe_extract(
        soup,
        'tr[bgcolor="#ffffff"] td[align="right"]:contains("PPG (Home):") + td + td',
        value_type=float,
    )
    team_stats["away_points_per_game"] = safe_extract(
        soup,
        'tr[bgcolor="#ffffff"] td[align="right"]:contains("PPG (Away):") + td + td',
        value_type=float,
    )
    
    # Calculate overall points per game if not extracted
    if team_stats.get("points_per_game") is None:
        home_ppg = team_stats.get("home_points_per_game")
        away_ppg = team_stats.get("away_points_per_game")
        
        if home_ppg is not None and away_ppg is not None:
            team_stats["points_per_game"] = round((home_ppg + away_ppg) / 2, 2)

    scoring_table = soup.select_one('table:-soup-contains("SCORING")')
    if scoring_table:
        team_stats["goals_scored_home"] = safe_extract(
            scoring_table, "tr:nth-of-type(2) td:nth-of-type(2)", value_type=int
        )
        team_stats["goals_scored_away"] = safe_extract(
            scoring_table, "tr:nth-of-type(2) td:nth-of-type(3)", value_type=int
        )
        team_stats["goals_scored_all"] = safe_extract(
            scoring_table, "tr:nth-of-type(2) td:nth-of-type(4)", value_type=int
        )
        team_stats["goals_scored_per_match_home"] = safe_extract(
            scoring_table,
            "tr:nth-of-type(3) td:nth-of-type(2)",
            value_type=float,
        )
        team_stats["goals_scored_per_match_away"] = safe_extract(
            scoring_table,
            "tr:nth-of-type(3) td:nth-of-type(3)",
            value_type=float,
        )
        team_stats["goals_scored_per_match_all"] = safe_extract(
            scoring_table,
            "tr:nth-of-type(3) td:nth-of-type(4)",
            value_type=float,
        )
        team_stats["goals_conceded_home"] = safe_extract(
            scoring_table, "tr:nth-of-type(4) td:nth-of-type(2)", value_type=int
        )
        team_stats["goals_conceded_away"] = safe_extract(
            scoring_table, "tr:nth-of-type(4) td:nth-of-type(3)", value_type=int
        )
        team_stats["goals_conceded_all"] = safe_extract(
            scoring_table, "tr:nth-of-type(4) td:nth-of-type(4)", value_type=int
        )
        team_stats["goals_conceded_per_match_home"] = safe_extract(
            scoring_table,
            "tr:nth-of-type(5) td:nth-of-type(2)",
            value_type=float,
        )
        team_stats["goals_conceded_per_match_away"] = safe_extract(
            scoring_table,
            "tr:nth-of-type(5) td:nth-of-type(3)",
            value_type=float,
        )
        team_stats["goals_conceded_per_match_all"] = safe_extract(
            scoring_table,
            "tr:nth-of-type(5) td:nth-of-type(4)",
            value_type=float,
        )

    team_stats["gf_ga_per_match_home"] = safe_extract(
        soup, 'td:-soup-contains("GF + GA per match") + td', value_type=float
    )
    team_stats["gf_ga_per_match_away"] = safe_extract(
        soup,
        'td:-soup-contains("GF + GA per match") + td + td',
        value_type=float,
    )
    team_stats["gf_ga_per_match_all"] = safe_extract(
        soup,
        'td:-soup-contains("GF + GA per match") + td + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_0_5_percentage_home"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 0.5") + td', value_type=float
    )
    team_stats["gf_ga_over_0_5_percentage_away"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 0.5") + td + td', value_type=float
    )
    team_stats["gf_ga_over_0_5_percentage_all"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 0.5") + td + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_1_5_percentage_home"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 1.5") + td', value_type=float
    )
    team_stats["gf_ga_over_1_5_percentage_away"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 1.5") + td + td', value_type=float
    )
    team_stats["gf_ga_over_1_5_percentage_all"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 1.5") + td + td + td',
        value_type=float,
    )

    team_stats["gf_ga_over_2_5_percentage_home"] = safe_extract(
        soup,
        'tr.trow3 td:-soup-contains("GF+GA over 2.5") + td',
        value_type=float,
    )
    team_stats["gf_ga_over_2_5_percentage_away"] = safe_extract(
        soup,
        'tr.trow3 td:-soup-contains("GF+GA over 2.5") + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_2_5_percentage_all"] = safe_extract(
        soup,
        'tr.trow3 td:-soup-contains("GF+GA over 2.5") + td + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_3_5_percentage_home"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 3.5") + td', value_type=float
    )
    team_stats["gf_ga_over_3_5_percentage_away"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 3.5") + td + td', value_type=float
    )
    team_stats["gf_ga_over_3_5_percentage_all"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 3.5") + td + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_4_5_percentage_home"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 4.5") + td', value_type=float
    )
    team_stats["gf_ga_over_4_5_percentage_away"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 4.5") + td + td', value_type=float
    )
    team_stats["gf_ga_over_4_5_percentage_all"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 4.5") + td + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_5_5_percentage_home"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 5.5") + td', value_type=float
    )
    team_stats["gf_ga_over_5_5_percentage_away"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 5.5") + td + td', value_type=float
    )
    team_stats["gf_ga_over_5_5_percentage_all"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 5.5") + td + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_0_5_ht_percentage_home"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 0.5 at HT") + td', value_type=float
    )
    team_stats["gf_ga_over_0_5_ht_percentage_away"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 0.5 at HT") + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_0_5_ht_percentage_all"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 0.5 at HT") + td + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_1_5_ht_percentage_home"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 1.5 at HT") + td', value_type=float
    )
    team_stats["gf_ga_over_1_5_ht_percentage_away"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 1.5 at HT") + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_1_5_ht_percentage_all"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 1.5 at HT") + td + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_2_5_ht_percentage_home"] = safe_extract(
        soup, 'td:-soup-contains("GF+GA over 2.5 at HT") + td', value_type=float
    )
    team_stats["gf_ga_over_2_5_ht_percentage_away"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 2.5 at HT") + td + td',
        value_type=float,
    )
    team_stats["gf_ga_over_2_5_ht_percentage_all"] = safe_extract(
        soup,
        'td:-soup-contains("GF+GA over 2.5 at HT") + td + td + td',
        value_type=float,
    )

    team_stats["ppg_last_8"] = safe_extract(
        soup,
        'tr[height="22"] td[valign="middle"][align="right"]:contains("Points per Game") + td + td',
        value_type=float,
    )
    team_stats["avg_goals_scored_last_8"] = safe_extract(
        soup,
        'tr td[valign="middle"][align="right"]:contains("Average goals scored") + td + td',
        value_type=float,
    )
    team_stats["avg_goals_conceded_last_8"] = safe_extract(
        soup,
        'tr td[valign="middle"][align="right"]:contains("Average goals conceded") + td + td',
        value_type=float,
    )

    return team_stats
