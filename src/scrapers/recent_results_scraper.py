import requests
from bs4 import BeautifulSoup
import logging
import re
from typing import List, Dict, Any
from results_scraper import RateLimitedScraper, USER_AGENTS
from head_to_head import safe_extract_text


def extract_team_and_score(team_text: str) -> tuple:
    """
    Extract team name and score from FootyStats format.

    FootyStats uses various formats:
    - "Team Name W5" (Women's team scored 5) → ("Team Name W", "5")
    - "Team Name U215" (U21 team scored 5) → ("Team Name U21", "5")
    - "Team Name U185" (U18 team scored 5) → ("Team Name U18", "5")
    - "Team Name1" (Direct concatenation) → ("Team Name", "1")
    - "Team Name 3" (Space separated) → ("Team Name", "3")
    - "Team Name" (No score) → ("Team Name", "N/A")
    """
    text = team_text.strip()

    # Pattern 1: Youth teams (U18, U19, U20, U21, U23, etc.)
    # Examples: "Barcelona U215" → ("Barcelona U21", "5")
    pattern_youth = r"^(.+?)\s+(U\d+)(\d+)$"
    match_youth = re.match(pattern_youth, text)

    if match_youth:
        team_base = match_youth.group(1).strip()
        youth_category = match_youth.group(2)  # U21, U18, etc.
        score = match_youth.group(3)
        team_name = f"{team_base} {youth_category}"
        return team_name, score

    # Pattern 2: Women's teams and other single letter categories
    # Examples: "Sparta Praha W7" → ("Sparta Praha W", "7")
    pattern_letter = r"^(.+?)\s+([A-Za-z])(\d+)$"
    match_letter = re.match(pattern_letter, text)

    if match_letter:
        team_base = match_letter.group(1).strip()
        category = match_letter.group(2)  # W, B, etc.
        score = match_letter.group(3)
        team_name = f"{team_base} {category}"
        return team_name, score

    # Pattern 3: Team name + space + score (simple format)
    # Examples: "Team Name 5", "Arsenal 2"
    pattern_space = r"^(.+?)\s+(\d+)$"
    match_space = re.match(pattern_space, text)

    if match_space:
        team_name = match_space.group(1).strip()
        score = match_space.group(2)
        # Only return if team name is reasonable (more than 2 characters)
        if len(team_name) > 2:
            return team_name, score

    # Pattern 4: Team name directly followed by score (no space)
    # Examples: "Arsenal1", "Bokelj0", "Budućnost5"
    pattern_direct = r"^(.+?)(\d+)$"
    match_direct = re.match(pattern_direct, text)

    if match_direct:
        team_name = match_direct.group(1).strip()
        score = match_direct.group(2)
        # Only return if team name is reasonable (more than 3 characters for no-space format)
        if len(team_name) > 3:
            return team_name, score

    # If no pattern matches, return original text with N/A score
    return text, "N/A"


def scrape_recent_results_from_h2h_page(url: str, scraper: RateLimitedScraper) -> List[List[str]]:
    """
    Scrape recent results from a single H2H page.
    Returns list of recent results in format [date, home_team, home_score, away_team, away_score].
    """
    try:
        logging.info(f"Scraping recent results from H2H page: {url}")
        response = scraper.get(url)
        
        if not response or not response.content:
            logging.error(f"Failed to get content from URL: {url}")
            return []
            
        soup = BeautifulSoup(response.content, "html.parser")
        
        # Extract recent results from the sliding fixtures section
        recent_results_elements = soup.select('div.sliding-fixtures .fixture')
        recent_results = []
        
        for fixture in recent_results_elements:
            try:
                # Extract date
                date_elem = fixture.select_one('time')
                date = safe_extract_text(date_elem) if date_elem else "N/A"
                
                # Extract teams
                teams = fixture.select('.team')
                if len(teams) >= 2:
                    home_team_raw = " ".join(teams[0].get_text(strip=True).split())
                    away_team_raw = " ".join(teams[1].get_text(strip=True).split())

                    # Extract scores from team names (FootyStats format: "Team Name W5" or "Team Name L2")
                    home_team, home_score = extract_team_and_score(home_team_raw)
                    away_team, away_score = extract_team_and_score(away_team_raw)

                    # If we couldn't extract scores from team names, try the score element
                    if home_score == "N/A" or away_score == "N/A":
                        score_elem = fixture.select_one('.score')
                        if score_elem:
                            score_text = score_elem.get_text(strip=True)
                            # Try to parse score in format "X - Y" or "X-Y"
                            score_match = re.match(r"(\d+)\s*-\s*(\d+)", score_text)
                            if score_match:
                                home_score = score_match.group(1)
                                away_score = score_match.group(2)

                    recent_results.append([date, home_team, home_score, away_team, away_score])
                        
            except Exception as e:
                logging.warning(f"Error parsing fixture in {url}: {e}")
                continue
                
        logging.info(f"Extracted {len(recent_results)} recent results from {url}")
        return recent_results
        
    except Exception as e:
        logging.error(f"Error scraping recent results from {url}: {e}")
        return []


def scrape_recent_results_for_league(league_config: Dict[str, Any]) -> List[List[str]]:
    """
    Scrape recent results for all H2H matchups in a league.
    Returns aggregated recent results from all H2H pages.
    """
    h2h_urls = league_config.get("H2H_URLS", {})
    if not h2h_urls:
        logging.warning("No H2H URLs found in league config for recent results scraping")
        return []
    
    # Create a scraper instance for recent results
    scraper = RateLimitedScraper(
        requests_per_window=5,
        time_window_seconds=60,
        base_delay=10.0,
        max_retries=30,
        user_agents=USER_AGENTS,
    )
    
    all_recent_results = []
    processed_matches = set()  # To avoid duplicate matches across different H2H pages
    
    for matchup, url in h2h_urls.items():
        try:
            logging.info(f"Processing recent results for matchup: {matchup}")
            recent_results = scrape_recent_results_from_h2h_page(url, scraper)
            
            # Filter out duplicates based on date + teams combination
            for result in recent_results:
                if len(result) >= 4:  # Ensure we have date, home_team, away_team at minimum
                    match_key = f"{result[0]}_{result[1]}_{result[3]}"  # date_home_away
                    if match_key not in processed_matches:
                        processed_matches.add(match_key)
                        all_recent_results.append(result)
                        
        except Exception as e:
            logging.error(f"Error processing matchup {matchup}: {e}")
            continue
    
    logging.info(f"Total recent results collected: {len(all_recent_results)}")
    return all_recent_results


def scrape_recent_results_for_team(team_name: str, league_config: Dict[str, Any]) -> List[List[str]]:
    """
    Scrape recent results for a specific team by finding relevant H2H pages.
    Returns recent results involving the specified team.
    """
    h2h_urls = league_config.get("H2H_URLS", {})
    if not h2h_urls:
        logging.warning(f"No H2H URLs found in league config for team {team_name}")
        return []
    
    # Filter H2H URLs to only those involving the specified team
    relevant_urls = {}
    team_name_lower = team_name.lower()
    
    for matchup, url in h2h_urls.items():
        if team_name_lower in matchup.lower():
            relevant_urls[matchup] = url
    
    if not relevant_urls:
        logging.warning(f"No H2H matchups found for team {team_name}")
        return []
    
    # Create a temporary league config with only relevant URLs
    temp_config = {"H2H_URLS": relevant_urls}
    
    logging.info(f"Found {len(relevant_urls)} relevant H2H matchups for team {team_name}")
    return scrape_recent_results_for_league(temp_config)


if __name__ == "__main__":
    # Test with a specific league configuration
    from config import LEAGUE_CONFIGS
    
    # Test with the first league
    if LEAGUE_CONFIGS:
        first_league_name = list(LEAGUE_CONFIGS.keys())[0]
        first_league_config = LEAGUE_CONFIGS[first_league_name]
        
        print(f"Testing recent results scraping for {first_league_name}")
        recent_results = scrape_recent_results_for_league(first_league_config)
        
        print(f"Found {len(recent_results)} recent results")
        if recent_results:
            print("Sample results:")
            for i, result in enumerate(recent_results[:5]):  # Show first 5 results
                print(f"{i+1}: {result}")
    else:
        print("No league configurations found")
