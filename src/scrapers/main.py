import logging
import argparse
import argcomplete
import concurrent.futures
from utils import (
    save_league_stats_to_csv,
    save_team_stats_to_csv,
    save_results_to_csv,
    save_head_to_head_stats_to_csv,
    save_recent_results_to_csv,
    save_league_table_to_csv,
    get_data_directory,
)
from league_stats import get_league_stats
from team_stats import get_team_stats
from results_scraper import scrape_all_results, RateLimitedScraper, USER_AGENTS
# from head_to_head import get_head_to_head_stats # This is no longer needed
# from h2h_batch_scraper import run_h2h_batch_scraping # This is no longer needed
from league_table import get_league_table
from recent_results_scraper import scrape_recent_results_for_league
from config import LEAGUE_CONFIGS
import pickle
import os
import pandas as pd
import numpy as np

# Configure logging for scraping operations
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console output
        logging.FileHandler('scraping.log')  # File output
    ]
)

CHECKPOINT_FILE = "checkpoint.pkl"

# Configure a single scraper for all requests
SCRAPER = RateLimitedScraper(
    requests_per_window=60,
    time_window_seconds=60,
    base_delay=1.0,
    max_retries=3,
    user_agents=USER_AGENTS,
)

def save_checkpoint(state):
    with open(CHECKPOINT_FILE, "wb") as f:
        pickle.dump(state, f)

def load_checkpoint():
    if os.path.exists(CHECKPOINT_FILE):
        with open(CHECKPOINT_FILE, "rb") as f:
            return pickle.load(f)
    return None

def replace_nan_with_zero(data):
    if isinstance(data, (pd.DataFrame, pd.Series)):
        return data.fillna(0)
    elif isinstance(data, np.ndarray):
        return np.nan_to_num(data, nan=0)
    elif isinstance(data, dict):
        return {k: replace_nan_with_zero(v) for k, v in data.items()}
    elif isinstance(data, (list, tuple)):
        return [replace_nan_with_zero(v) for v in data]
    elif pd.isna(data):
        return 0
    else:
        return data

def scrape_team(team_url_tuple):
    """Helper function to scrape a single team's stats."""
    team, url = team_url_tuple
    try:
        team_stats = get_team_stats(url, team, SCRAPER)
        return team, team_stats
    except Exception as e:
        logging.error(f"Failed to scrape team {team}: {e}")
        return team, None

def main():
    parser = argparse.ArgumentParser(description="Scrape sports data (excluding H2H). H2H is now handled by head_to_head.py.")
    
    # Scope arguments
    parser.add_argument("--league", type=str, help="Target a specific league. If omitted, operations apply to all configured leagues.")
    parser.add_argument("--team", type=str, help="Target a specific team for team stats. Requires --league.")
    parser.add_argument("--match", type=str, help="Target a specific matchup (e.g., 'TeamA vs TeamB'). Requires --league.")

    # Operation arguments
    op_group = parser.add_argument_group('Data Operation Flags')
    op_group.add_argument("--force-all-data", action='store_true', help="Overwrite ALL data types (league, team, results, table, H2H).")
    op_group.add_argument("--refresh-league-data", action='store_true', help="Overwrite league_stats, team_stats, and league_table.")
    op_group.add_argument("--refresh-league-stats", action='store_true', help="Overwrite league_stats only.")
    op_group.add_argument("--refresh-team-stats", action='store_true', help="Overwrite team_stats only.")
    op_group.add_argument("--refresh-league-table", action='store_true', help="Overwrite league_table only.")
    op_group.add_argument("--refresh-results", action='store_true', help="Overwrite historical match results.")
    op_group.add_argument("--refresh-h2h", action='store_true', help="Overwrite H2H stats and their derived recent results.")
    op_group.add_argument("--refresh-recent-results", action='store_true', help="Overwrite recent results data independently from H2H.")
    op_group.add_argument("--incremental-results", action='store_true', help="Incrementally update historical match results.")
    op_group.add_argument("--incremental-h2h", action='store_true', help="Incrementally update H2H stats and their derived recent results.")
    op_group.add_argument("--incremental-all-updatable", action='store_true', help="Convenience flag to run both --incremental-results and --incremental-h2h.")
    
    argcomplete.autocomplete(parser)
    args = parser.parse_args()

    # Validate team and match usage
    if args.team and not args.league:
        parser.error("--team argument requires --league to be specified.")
    if args.match and not args.league:
        parser.error("--match argument requires --league to be specified.")

    # Handle convenience flag
    if args.incremental_all_updatable:
        args.incremental_results = True
        args.incremental_h2h = True

    any_refresh_or_incremental_flag_set = (
        args.force_all_data or
        args.refresh_league_data or
        args.refresh_league_stats or
        args.refresh_team_stats or
        args.refresh_league_table or
        args.refresh_results or
        args.refresh_h2h or
        args.refresh_recent_results or
        args.incremental_results or
        args.incremental_h2h or
        args.incremental_all_updatable
    )

    # Determine global operation settings
    g_process_league_data = False
    g_overwrite_league_data = False
    g_process_league_stats = False
    g_process_team_stats = False
    g_process_league_table = False
    g_process_results_data = False
    g_overwrite_results_data = False
    g_increment_results_data = False
    g_process_recent_results_data = False
    g_overwrite_recent_results_data = False
    is_default_full_overwrite_behavior = False

    if args.force_all_data:
        logging.info("Operation mode: --force-all-data. Overwriting all non-H2H data types for the scope.")
        g_process_league_data = True
        g_overwrite_league_data = True
        g_process_league_stats = True
        g_process_team_stats = True
        g_process_league_table = True
        g_process_results_data = True
        g_overwrite_results_data = True
        g_process_recent_results_data = True
        g_overwrite_recent_results_data = True
    elif not any_refresh_or_incremental_flag_set:
        logging.info("Operation mode: No specific operation flags. Defaulting to full overwrite for the scope (excluding H2H).")
        is_default_full_overwrite_behavior = True
        g_process_league_data = True
        g_overwrite_league_data = True
        g_process_league_stats = True
        g_process_team_stats = True
        g_process_league_table = True
        g_process_results_data = True
        g_overwrite_results_data = True
        g_process_recent_results_data = True
        g_overwrite_recent_results_data = True
    else:
        logging.info("Operation mode: Specific refresh/incremental flags detected.")
        if args.refresh_league_data:
            g_process_league_data = True
            g_overwrite_league_data = True
            g_process_league_stats = True
            g_process_team_stats = True
            g_process_league_table = True
        if args.refresh_league_stats:
            g_process_league_data = True
            g_overwrite_league_data = True
            g_process_league_stats = True
        if args.refresh_team_stats:
            g_process_league_data = True
            g_overwrite_league_data = True
            g_process_team_stats = True
        if args.refresh_league_table:
            g_process_league_data = True
            g_overwrite_league_data = True
            g_process_league_table = True
        if args.refresh_results:
            g_process_results_data = True
            g_overwrite_results_data = True
        elif args.incremental_results:
            g_process_results_data = True
            g_increment_results_data = True
        if args.refresh_recent_results:
            g_process_recent_results_data = True
            g_overwrite_recent_results_data = True

    checkpoint = load_checkpoint()
    start_league = 0
    use_checkpoint_for_this_run = not args.league and (args.force_all_data or is_default_full_overwrite_behavior)

    league_items = list(LEAGUE_CONFIGS.items())

    if args.league:
        league_items = [(name, config) for name, config in league_items if name.lower() == args.league.lower()]
        if not league_items:
            logging.error(f"Target league '{args.league}' not found or misconfigured.")
            return

    elif use_checkpoint_for_this_run and checkpoint:
        start_league = checkpoint.get("league_index", 0)
        # ... (rest of your checkpoint logic)

    is_full_run_and_intended_overwrite = not args.league and (args.force_all_data or is_default_full_overwrite_behavior)

    for league_index in range(start_league, len(league_items)):
        league_name, league_config = league_items[league_index]
        try:
            logging.info(f"Processing league: {league_name}")

            if g_process_league_data:
                # Fetch and save league stats (if enabled)
                if g_process_league_stats:
                    league_stats_url = league_config.get("LEAGUE_STATS_URL")
                    if league_stats_url:
                        logging.info(f"Scraping league stats for {league_name}")
                        league_stats = get_league_stats(league_stats_url, SCRAPER)
                        save_league_stats_to_csv(league_stats, league_name, f"{league_name}_league_stats.csv")

                # Fetch and save team stats (if enabled)
                if g_process_team_stats:
                    team_urls = league_config.get("TEAM_URLS", {})
                    if team_urls:
                        logging.info(f"Scraping team stats for {league_name} using concurrency...")
                        team_stats_dict = {}
                        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                            future_to_team = {executor.submit(scrape_team, (team, url)): team for team, url in team_urls.items()}
                            for future in concurrent.futures.as_completed(future_to_team):
                                team = future_to_team[future]
                                try:
                                    _, team_stats = future.result()
                                    if team_stats is not None:
                                        team_stats_dict[team] = team_stats
                                        logging.info(f"Successfully scraped team: {team}")
                                except Exception as exc:
                                    logging.error(f'{team} generated an exception: {exc}')
                        save_team_stats_to_csv(team_stats_dict, league_name, f"{league_name}_team_stats.csv")

                # Fetch and save league table (if enabled)
                if g_process_league_table:
                    league_table_url = league_config.get("LEAGUE_TABLE_URL")
                    if league_table_url:
                        logging.info(f"Scraping league table for {league_name}")
                        league_table = get_league_table(league_table_url, SCRAPER)
                        save_league_table_to_csv(league_table, league_name, f"{league_name}_league_table.csv")
            
            if g_process_results_data:
                # Fetch and save match results
                team_urls = league_config.get("TEAM_URLS", {})
                if team_urls:
                    logging.info(f"🏈 Scraping results for {league_name} - {len(team_urls)} teams")
                    start_date_dt = None
                    if g_increment_results_data:
                        # For incremental updates, determine the last date from existing results
                        from pathlib import Path
                        existing_results_file = Path(get_data_directory(league_name)) / f"{league_name}_results.csv"
                        if existing_results_file.exists():
                            try:
                                import pandas as pd
                                from datetime import datetime
                                existing_df = pd.read_csv(existing_results_file)
                                if not existing_df.empty and 'Date' in existing_df.columns:
                                    # Parse the last date from the existing data
                                    last_date_str = existing_df['Date'].iloc[-1]
                                    # Try different date formats
                                    for fmt in ["%b %d, %Y", "%d %b %Y", "%Y-%m-%d", "%d/%m/%Y"]:
                                        try:
                                            start_date_dt = datetime.strptime(last_date_str, fmt)
                                            logging.info(f"Incremental update from date: {start_date_dt.strftime('%Y-%m-%d')}")
                                            break
                                        except ValueError:
                                            continue
                                    if not start_date_dt:
                                        logging.warning(f"Could not parse last date '{last_date_str}', doing full refresh")
                            except Exception as e:
                                logging.warning(f"Error reading existing results for incremental update: {e}")
                    else:
                        logging.info(f"🔄 Full results refresh for {league_name}")

                    logging.info(f"📊 Starting results scraping for {league_name}...")
                    results = scrape_all_results(team_urls, SCRAPER, start_date_dt)
                    logging.info(f"✅ Scraped {len(results)} results for {league_name}")
                    save_results_to_csv(results, league_name, f"{league_name}_results.csv",
                                       append=g_increment_results_data)
                    logging.info(f"💾 Saved results to {league_name}_results.csv")
                else:
                    logging.warning(f"⚠️ No team URLs found for {league_name} - skipping results scraping")
            
            # Process recent results data
            if g_process_recent_results_data:
                # Fetch and save recent match results
                recent_results = scrape_recent_results_for_league(league_config)
                save_recent_results_to_csv(recent_results, league_name, f"{league_name}_recent_results.csv", append=False)

            # Process H2H data if specified
            if args.refresh_h2h or args.incremental_h2h:
                logging.info(f"H2H processing for {league_name} not yet implemented")
                # TODO: Add H2H processing logic here
                # This would call the head_to_head.py functionality
                pass

            if is_full_run_and_intended_overwrite:
                save_checkpoint({"league_index": league_index + 1, "league_name_at_checkpoint": league_name})

        except Exception as e:
            logging.error(f"Error processing league {league_name}: {e}")
            logging.exception(f"Traceback for {league_name}:")
            if is_full_run_and_intended_overwrite:
                save_checkpoint({"league_index": league_index, "league_name_at_checkpoint": league_name})
            logging.error("Stopping script due to error.")
            break
            
    if is_full_run_and_intended_overwrite and os.path.exists(CHECKPOINT_FILE):
        # ... (your checkpoint removal logic) ...
        pass

if __name__ == "__main__":
    main()
