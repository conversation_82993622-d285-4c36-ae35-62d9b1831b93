import os
import re


def comment_out_urls(file_path):
    with open(file_path, "r") as file:
        content = file.read()

    # Use regex to find the HEAD_TO_HEAD_URLS dictionary
    pattern = r'"HEAD_TO_HEAD_URLS":\s*{([^}]*)}'
    match = re.search(pattern, content)

    if match:
        head_to_head_content = match.group(1)

        # Modified regex to only comment out the key
        commented_content = re.sub(
            r'("([^"]+)":\s*"[^"]+"),?', r"# \1,", head_to_head_content
        )

        # Remove trailing comma if present
        commented_content = commented_content.rstrip(",")

        modified_content = content.replace(head_to_head_content, commented_content)

        with open(file_path, "w") as file:
            file.write(modified_content)
        print(f"URLs in HEAD_TO_HEAD_URLS have been commented out in {file_path}.")
    else:
        print(f"No HEAD_TO_HEAD_URLS dictionary found in {file_path}.")


# The rest of your script remains the same
def process_directory(directory_path):
    for root, dirs, files in os.walk(directory_path):
        for file_name in files:
            if file_name.endswith(".py"):
                file_path = os.path.join(root, file_name)
                comment_out_urls(file_path)


# Specify the path to the league_configs directory
directory_path = os.path.join(os.path.dirname(__file__), "league_configs")
process_directory(directory_path)
