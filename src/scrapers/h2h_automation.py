#!/usr/bin/env python3
"""
Automation script for H2H batch scraping.
Runs the scraper repeatedly with delays until all URLs are processed.
"""

import time
import random
import logging
import argparse
import sys
import os
import json
from typing import Dict, List
from datetime import datetime
from h2h_batch_scraper import run_h2h_batch_scraping, get_all_progress, print_progress_summary
from head_to_head import RateLimitedScraper, USER_AGENTS
from config import LEAGUE_CONFIGS

# Configuration
MIN_DELAY = 3  # Minimum seconds between runs
MAX_DELAY = 5  # Maximum seconds between runs

# Failed leagues tracking
FAILED_LEAGUES_FILE = "h2h_failed_leagues.json"

def mark_league_as_failed(league_name: str, error_message: str):
    """Mark a league as failed to prevent infinite retries."""
    try:
        failed_leagues = {}
        if os.path.exists(FAILED_LEAGUES_FILE):
            with open(FAILED_LEAGUES_FILE, 'r') as f:
                failed_leagues = json.load(f)
        
        failed_leagues[league_name] = {
            'error': error_message,
            'timestamp': datetime.now().isoformat(),
            'retry_count': failed_leagues.get(league_name, {}).get('retry_count', 0) + 1
        }
        
        with open(FAILED_LEAGUES_FILE, 'w') as f:
            json.dump(failed_leagues, f, indent=2)
        
        logging.warning(f"Marked league {league_name} as failed (attempt #{failed_leagues[league_name]['retry_count']}): {error_message}")
    except Exception as e:
        logging.error(f"Failed to mark league {league_name} as failed: {e}")

def is_league_failed(league_name: str, max_failures: int = 3) -> bool:
    """Check if a league has been marked as failed too many times."""
    try:
        if not os.path.exists(FAILED_LEAGUES_FILE):
            return False
        
        with open(FAILED_LEAGUES_FILE, 'r') as f:
            failed_leagues = json.load(f)
        
        league_data = failed_leagues.get(league_name, {})
        retry_count = league_data.get('retry_count', 0)
        
        return retry_count >= max_failures
    except Exception as e:
        logging.error(f"Error checking failed status for league {league_name}: {e}")
        return False

def setup_logging():
    """Setup logging for the automation script."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler("h2h_automation.log")
        ]
    )

def get_leagues_with_h2h() -> List[tuple]:
    """Get all leagues that have H2H URLs configured, sorted alphabetically."""
    leagues = [
        (name, config)
        for name, config in LEAGUE_CONFIGS.items()
        if config.get("HEAD_TO_HEAD_URLS") and config.get("TEAM_NAME_MAPPING")
    ]
    # Sort alphabetically by league name
    return sorted(leagues, key=lambda x: x[0])


def check_other_leagues_for_work(league_items: List[tuple]) -> bool:
    """Check if any leagues have remaining work."""
    from h2h_batch_scraper import load_checkpoint

    for name, config in league_items:
        try:
            # Skip leagues that have failed too many times
            if is_league_failed(name):
                logging.debug(f"Skipping failed league {name} during work check")
                continue

            h2h_urls = config.get("HEAD_TO_HEAD_URLS", {})
            if not h2h_urls:
                continue

            # Check if this league has remaining work
            checkpoint = load_checkpoint(name, configured_h2h_urls=h2h_urls)
            
            # If checkpoint indicates completion from CSV, skip this league
            if checkpoint.get('is_complete_from_csv', False):
                continue
                
            total_urls = len(h2h_urls) # This should align with checkpoint['total_urls'] if config was passed
            completed_count = len(checkpoint.get('completed_urls', []))
            permanently_failed_count = len(checkpoint.get('permanently_failed_urls', []))

            if completed_count + permanently_failed_count < total_urls:
                return True
        except Exception as e:
            logging.error(f"Error checking work for league {name}: {e}")
            # Mark this league as failed and continue
            mark_league_as_failed(name, str(e))
            continue

    return False

def run_single_iteration(target_league: str = None):
    """
    Run a SINGLE iteration of H2H automation for ONE league only.
    This processes one batch (4 URLs) for the first league with remaining work and then exits.

    Args:
        target_league: Specific league to process (None for auto-select first with work)

    Returns:
        bool: True if more work remains, False if all leagues are complete
    """
    setup_logging()

    # Create H2H scraper
    h2h_scraper = RateLimitedScraper(
        requests_per_window=5,
        time_window_seconds=60,
        base_delay=10.0,
        max_retries=30,
        user_agents=USER_AGENTS,
    )

    # Get leagues to process
    league_items = get_leagues_with_h2h()

    if target_league:
        # Process specific league
        league_items = [
            (name, config) for name, config in league_items
            if name.lower() == target_league.lower()
        ]
        if not league_items:
            logging.error(f"League '{target_league}' not found or has no H2H URLs configured")
            return False

        selected_league = league_items[0]
        logging.info(f"Starting H2H single iteration for specific league: {target_league}")
    else:
        # Auto-select first league with remaining work
        from h2h_batch_scraper import load_checkpoint

        selected_league = None
        for name, config in league_items:
            try:
                # Skip leagues that have failed too many times
                if is_league_failed(name):
                    logging.debug(f"Auto-selection check for {name}: Skipping failed league")
                    continue

                h2h_urls = config.get("HEAD_TO_HEAD_URLS", {})
                if not h2h_urls:
                    continue

                # Check if this league has remaining work using enhanced checkpoint loading
                checkpoint = load_checkpoint(name, configured_h2h_urls=h2h_urls)
                
                # If checkpoint indicates completion from CSV, skip this league
                if checkpoint.get('is_complete_from_csv', False):
                    logging.debug(f"Auto-selection check for {name}: Complete based on CSV")
                    continue
                    
                total_urls = len(h2h_urls) # This should align with checkpoint['total_urls']
                completed_count = len(checkpoint.get('completed_urls', []))
                permanently_failed_count = len(checkpoint.get('permanently_failed_urls', []))

                logging.debug(f"Auto-selection check for {name}: {completed_count + permanently_failed_count}/{total_urls} completed")

                if completed_count + permanently_failed_count < total_urls:
                    selected_league = (name, config)
                    logging.info(f"Auto-selected league with remaining work: {name}")
                    break
            except Exception as e:
                logging.error(f"Error checking league {name} during auto-selection: {e}")
                # Mark this league as failed and continue to next league
                mark_league_as_failed(name, str(e))
                continue

        if not selected_league:
            logging.info("🎉 All leagues completed!")
            return False

    league_name, league_config = selected_league

    logging.info(f"{'='*60}")
    logging.info(f"SINGLE ITERATION RUN - LEAGUE: {league_name}")
    logging.info(f"{'='*60}")

    try:
        h2h_urls = league_config.get("HEAD_TO_HEAD_URLS", {})
        if not h2h_urls:
            logging.warning(f"No H2H URLs for {league_name}")
            return False

        # Run batch scraping for this league (processes ONE batch only)
        has_more_work = run_h2h_batch_scraping(league_name, h2h_urls, h2h_scraper)

        if not has_more_work:
            logging.info(f"✓ League {league_name} completed!")

            # Check if other leagues have remaining work
            logging.info("Checking other leagues for remaining work...")
            other_leagues_have_work = check_other_leagues_for_work(league_items)
            if other_leagues_have_work:
                if target_league is None:
                    logging.info("Other leagues have remaining work. Continuing automation.")
                else:
                    logging.info(f"✅ {league_name} complete, but other leagues still need work")
                return True
            else:
                logging.info("🎉 All leagues completed! No more work remaining.")
                return False

        # Return the work status of the current league only.
        # True if the current league has more URLs to process, False otherwise.
        return has_more_work
    except KeyboardInterrupt:
        logging.info("\n⚠️  Automation interrupted by user")
        logging.info("Progress has been saved. You can resume by running the script again.")
        return True  # Assume more work remains if interrupted
    except Exception as e:
        logging.error(f"❌ CRITICAL ERROR processing league {league_name}: {e}")
        logging.exception("Full traceback:")
        
        # Mark this league as failed to prevent infinite retries
        mark_league_as_failed(league_name, str(e))
        
        # If processing a specific league, return False to stop
        if target_league:
            logging.error(f"Stopping automation for specific league {league_name} due to critical error")
            return False
        
        # If auto-selecting leagues, continue to next league
        logging.info(f"Skipping failed league {league_name} and continuing to next league")
        return True  # Continue to next league

    finally:
        logging.info(f"\nSingle iteration finished")


def run_automation(target_league: str = None, max_iterations: int = None):
    """
    Legacy function for backward compatibility.
    Now just runs a single iteration and exits.
    """
    logging.warning("run_automation() is deprecated. Use run_single_iteration() instead.")
    return run_single_iteration(target_league)

def reset_failed_leagues():
    """Reset the failed leagues file to allow retrying all leagues."""
    try:
        if os.path.exists(FAILED_LEAGUES_FILE):
            os.remove(FAILED_LEAGUES_FILE)
            print(f"✓ Reset failed leagues file: {FAILED_LEAGUES_FILE}")
        else:
            print("No failed leagues file found.")
    except Exception as e:
        print(f"Error resetting failed leagues: {e}")

def show_failed_leagues():
    """Show currently failed leagues."""
    try:
        if not os.path.exists(FAILED_LEAGUES_FILE):
            print("No failed leagues found.")
            return
        
        with open(FAILED_LEAGUES_FILE, 'r') as f:
            failed_leagues = json.load(f)
        
        if not failed_leagues:
            print("No failed leagues found.")
            return
        
        print("\n🚫 Failed Leagues:")
        print("=" * 60)
        for league_name, data in failed_leagues.items():
            retry_count = data.get('retry_count', 0)
            timestamp = data.get('timestamp', 'Unknown')
            error = data.get('error', 'Unknown error')
            print(f"League: {league_name}")
            print(f"  Failures: {retry_count}")
            print(f"  Last Failed: {timestamp}")
            print(f"  Error: {error}")
            print()
    except Exception as e:
        print(f"Error showing failed leagues: {e}")


def run_recent_results_only(target_league: str = None, dry_run: bool = False):
    """
    Run recent results scraping only for specified league(s).
    This scrapes only the recent results data without processing H2H stats.

    Args:
        target_league: Specific league to process (None for all leagues with missing recent results)
        dry_run: If True, only show what would be processed without actually scraping

    Returns:
        bool: True if more work remains, False if all leagues are complete
    """
    setup_logging()

    if dry_run:
        logging.info("🔍 DRY RUN: Starting recent results analysis (no actual scraping)")
    else:
        logging.info("Starting recent results only scraping")

    # Import the recent results batch scraper
    from recent_results_batch_scraper import run_recent_results_batch_scraping
    import importlib.util

    # Create H2H scraper for recent results
    scraper = RateLimitedScraper(
        requests_per_window=5,
        time_window_seconds=60,
        base_delay=10.0,
        max_retries=30,
        user_agents=USER_AGENTS,
    )

    # Get leagues that need recent results
    leagues_to_process = []

    if target_league:
        leagues_to_process = [target_league]
        logging.info(f"Processing specific league: {target_league}")
    else:
        # Find all leagues missing recent results and sort alphabetically
        leagues_to_process = sorted(find_leagues_missing_recent_results())
        logging.info(f"Found {len(leagues_to_process)} leagues missing recent results (alphabetical order)")

    if not leagues_to_process:
        logging.info("No leagues need recent results processing")
        return False

    # Process ONE league with ONE batch (4 URLs) - prioritize completing leagues one at a time
    # Find the league with the most progress (highest completion percentage) that still has work
    target_league_to_process = None
    best_completion_percentage = -1

    if target_league:
        # Check if specific league has work remaining
        target_league_to_process = target_league
    else:
        # Find league with highest completion percentage that still has work remaining
        # Process in alphabetical order for easier tracking
        for league in sorted(leagues_to_process):
            try:
                # Load league configuration
                config_path = f"league_configs/{league}.py"
                if not os.path.exists(config_path):
                    config_path = f"src/scrapers/league_configs/{league}.py"  # Try from project root
                if not os.path.exists(config_path):
                    continue

                # Import league config
                spec = importlib.util.spec_from_file_location(league, config_path)
                league_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(league_module)

                h2h_urls = getattr(league_module, 'HEAD_TO_HEAD_URLS', {})

                if h2h_urls:
                    # Check if this league has remaining work and calculate completion percentage
                    from recent_results_batch_scraper import RecentResultsBatchProcessor
                    processor = RecentResultsBatchProcessor(league, h2h_urls)
                    batch = processor.get_next_batch()

                    if batch:  # Has work remaining
                        completed_count = len(processor.completed_urls)
                        total_count = len(h2h_urls)
                        completion_percentage = (completed_count / total_count) * 100

                        # Prioritize league with highest completion percentage
                        if completion_percentage > best_completion_percentage:
                            best_completion_percentage = completion_percentage
                            target_league_to_process = league
                            logging.info(f"Found league {league} with {completion_percentage:.1f}% completion ({completed_count}/{total_count})")

            except Exception as e:
                logging.warning(f"Error checking {league}: {e}")
                continue

    if not target_league_to_process:
        logging.info("No leagues have remaining recent results work")
        return False

    # Process ONE batch for the selected league
    try:
        if dry_run:
            logging.info(f"🔍 DRY RUN: Would process one batch for {target_league_to_process}")
            return True  # Simulate more work remaining

        logging.info(f"Processing one batch of recent results for {target_league_to_process}")

        # Load league configuration
        config_path = f"league_configs/{target_league_to_process}.py"
        if not os.path.exists(config_path):
            config_path = f"src/scrapers/league_configs/{target_league_to_process}.py"  # Try from project root

        spec = importlib.util.spec_from_file_location(target_league_to_process, config_path)
        league_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(league_module)

        h2h_urls = getattr(league_module, 'HEAD_TO_HEAD_URLS', {})

        # Run batch processing (processes 4 URLs and exits)
        has_more_work = run_recent_results_batch_scraping(target_league_to_process, h2h_urls, scraper)

        if has_more_work:
            logging.info(f"✅ Processed one batch for {target_league_to_process}. More work remains.")
            return True  # This league has more work
        else:
            logging.info(f"✅ Completed all recent results for {target_league_to_process}")

            # Check if OTHER leagues still need work
            remaining_leagues = find_leagues_missing_recent_results()
            if remaining_leagues:
                logging.info(f"✅ {target_league_to_process} complete, but {len(remaining_leagues)} other leagues still need work")
                return True  # Other leagues have work
            else:
                logging.info("🎉 ALL leagues completed! No more recent results work remaining.")
                return False  # All leagues complete

    except Exception as e:
        logging.error(f"❌ Error processing {target_league_to_process}: {e}")
        return True  # Assume more work remains on error




def find_leagues_missing_recent_results():
    """Find all leagues that are missing recent_results.csv, have empty files, or are incomplete."""
    import os
    import importlib.util
    from recent_results_batch_scraper import RecentResultsBatchProcessor

    missing_leagues = []

    data_dir = "../../data/raw"
    if not os.path.exists(data_dir):
        data_dir = "data/raw"  # Try relative path
    if not os.path.exists(data_dir):
        data_dir = "../data/raw"  # Try another relative path

    if not os.path.exists(data_dir):
        logging.error(f"Data directory not found: {data_dir}")
        return missing_leagues

    for league_dir in os.listdir(data_dir):
        league_path = os.path.join(data_dir, league_dir)
        if os.path.isdir(league_path):
            recent_results_file = os.path.join(league_path, f"{league_dir}_recent_results.csv")

            # Check if file is missing
            if not os.path.exists(recent_results_file):
                missing_leagues.append(league_dir)
                logging.info(f"Missing recent_results.csv: {league_dir}")
                continue

            # Check if file is empty (only header)
            try:
                with open(recent_results_file, 'r') as f:
                    lines = f.readlines()
                    if len(lines) <= 1:  # Only header or empty
                        missing_leagues.append(league_dir)
                        logging.info(f"Empty recent_results.csv: {league_dir}")
                        continue
            except Exception as e:
                logging.warning(f"Error reading {recent_results_file}: {e}")
                missing_leagues.append(league_dir)
                continue

            # Check if league has incomplete progress
            try:
                # Load league configuration to get total H2H URLs
                config_path = f"league_configs/{league_dir}.py"
                if not os.path.exists(config_path):
                    config_path = f"src/scrapers/league_configs/{league_dir}.py"

                if os.path.exists(config_path):
                    # Import league config
                    spec = importlib.util.spec_from_file_location(league_dir, config_path)
                    league_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(league_module)

                    h2h_urls = getattr(league_module, 'HEAD_TO_HEAD_URLS', {})
                    total_urls = len(h2h_urls)

                    if total_urls > 0:
                        # Check actual progress using batch processor
                        processor = RecentResultsBatchProcessor(league_dir, h2h_urls)
                        completed_count = len(processor.completed_urls)

                        if completed_count < total_urls:
                            missing_leagues.append(league_dir)
                            logging.info(f"Incomplete recent_results.csv: {league_dir} ({completed_count}/{total_urls} completed)")
                        else:
                            logging.info(f"Complete recent_results.csv: {league_dir} ({completed_count}/{total_urls} completed)")

            except Exception as e:
                logging.warning(f"Error checking progress for {league_dir}: {e}")
                # If we can't check progress, assume it needs work
                missing_leagues.append(league_dir)

    return missing_leagues


def save_recent_results_to_csv(league_name: str, recent_results: List[List[str]]) -> bool:
    """
    Save recent results to CSV file.

    Args:
        league_name: Name of the league
        recent_results: List of recent results data

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        import csv

        # Determine output directory
        output_dir = f"../../data/raw/{league_name}"
        if not os.path.exists(output_dir):
            output_dir = f"data/raw/{league_name}"  # Try relative path

        if not os.path.exists(output_dir):
            logging.error(f"Output directory not found: {output_dir}")
            return False

        output_file = os.path.join(output_dir, f"{league_name}_recent_results.csv")

        # Write CSV file
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['date', 'home_team', 'home_score', 'away_team', 'away_score']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for result in recent_results:
                if len(result) >= 5:
                    writer.writerow({
                        'date': result[0],
                        'home_team': result[1],
                        'home_score': result[2],
                        'away_team': result[3],
                        'away_score': result[4]
                    })

        logging.info(f"✅ Saved {len(recent_results)} recent results to {output_file}")
        return True

    except Exception as e:
        logging.error(f"Error saving recent results for {league_name}: {e}")
        return False


def main():
    """Main entry point for the automation script."""
    parser = argparse.ArgumentParser(description="Run H2H batch scraping (single iteration)")
    parser.add_argument(
        "--league",
        type=str,
        help="Target a specific league (default: all leagues)"
    )
    parser.add_argument(
        "--progress",
        action="store_true",
        help="Show current progress and exit"
    )
    parser.add_argument(
        "--reset-failed",
        action="store_true",
        help="Reset failed leagues to allow retrying them"
    )
    parser.add_argument(
        "--show-failed",
        action="store_true",
        help="Show currently failed leagues"
    )
    parser.add_argument(
        "--recent-results-only",
        action="store_true",
        help="Scrape only recent results data (skip H2H stats)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be processed without actually scraping"
    )

    args = parser.parse_args()

    if args.progress:
        print_progress_summary()
        show_failed_leagues()
        return

    if args.reset_failed:
        reset_failed_leagues()
        return

    if args.show_failed:
        show_failed_leagues()
        return

    # Run single iteration and exit with appropriate code
    if args.recent_results_only:
        has_more_work = run_recent_results_only(args.league, dry_run=args.dry_run)
    else:
        has_more_work = run_single_iteration(args.league)

    # Exit with code 0 if complete, 1 if more work remains
    exit_code = 1 if has_more_work else 0
    logging.info(f"Exiting with code {exit_code} ({'more work remains' if has_more_work else 'all complete'})")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()