import os
import ast


def is_urls_uncommented(file_path):
    try:
        with open(file_path, "r") as file:
            content = file.read()

        # Parse the Python file
        tree = ast.parse(content)

        # Look for dictionary assignments
        for node in ast.walk(tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name) and target.id == "LEAGUE_CONFIGS":
                        # Found the LEAGUE_CONFIGS dictionary
                        config_dict = ast.literal_eval(node.value)

                        # Check each league's HEAD_TO_HEAD_URLS
                        for league_config in config_dict.values():
                            if "HEAD_TO_HEAD_URLS" in league_config:
                                h2h_urls = league_config["HEAD_TO_HEAD_URLS"]
                                # If there are any uncommented URLs, return True
                                if h2h_urls:
                                    return True
        return False
    except Exception as e:
        print(f"Error processing {file_path}: {str(e)}")
        return False


def find_uncommented_configs():
    current_dir = os.path.dirname(os.path.abspath(__file__))
    uncommented_files = []

    for filename in os.listdir(current_dir):
        if filename.endswith(".py") and filename != "__init__.py":
            file_path = os.path.join(current_dir, filename)
            if is_urls_uncommented(file_path):
                uncommented_files.append(filename)

    return uncommented_files


if __name__ == "__main__":
    uncommented_files = find_uncommented_configs()
    if uncommented_files:
        print("Files with uncommented HEAD_TO_HEAD_URLS:")
        for file in uncommented_files:
            print(f"- {file}")
    else:
        print("No files found with uncommented HEAD_TO_HEAD_URLS")
