from bs4 import BeautifulSoup
from utils import safe_extract
from team_stats import RateLimitedScraper
from typing import Dict


def get_league_stats(league_url: str, scraper: RateLimitedScraper) -> Dict:
    """
    Get league statistics using rate-limited scraper.

    Args:
        league_url: URL of the league statistics page
        scraper: RateLimitedScraper instance for making requests

    Returns:
        Dictionary containing league statistics
    """
    response = scraper.get(league_url)
    if response is None:
        # Log an error or warning, and return an empty dict or raise a specific exception
        # For now, let's log and return empty, consistent with team_stats.py
        import logging # Add import if not already present at the top
        logging.error(f"Failed to get league stats from URL: {league_url}")
        return {}
    soup = BeautifulSoup(response, "html.parser")

    league_stats = {}

    # Extract various league statistics
    league_stats["avg_goals_per_match"] = safe_extract(
        soup, 'td:-soup-contains("Goals per match:") + td', value_type=float
    )
    league_stats["home_goals_per_match"] = safe_extract(
        soup, 'td:-soup-contains("Home goals per match:") + td', value_type=float
    )
    league_stats["away_goals_per_match"] = safe_extract(
        soup, 'td:-soup-contains("Away goals per match:") + td', value_type=float
    )
    league_stats["home_win_percentage"] = safe_extract(
        soup,
        'td[valign="top"][width="40%"] table td[align="right"]:contains("Home wins:") + td b',
        value_type=float,
    )
    league_stats["draw_percentage"] = safe_extract(
        soup,
        'td[valign="top"][width="40%"] table td[align="right"]:contains("Draws:") + td b',
        value_type=float,
    )
    league_stats["away_win_percentage"] = safe_extract(
        soup,
        'td[valign="top"][width="40%"] table td[align="right"]:contains("Away wins:") + td b',
        value_type=float,
    )
    league_stats["both_teams_scored_percentage"] = safe_extract(
        soup, 'td:-soup-contains("Both teams scored:") + td', value_type=float
    )
    league_stats["over_1_5_goals_percentage"] = safe_extract(
        soup,
        'td[valign="top"][width="40%"] table tr:nth-child(1) td:nth-last-child(1) b',
        value_type=float,
    )
    league_stats["over_2_5_goals_percentage"] = safe_extract(
        soup,
        'td[valign="top"][width="40%"] table tr:nth-child(2) td:nth-last-child(1) b',
        value_type=float,
    )
    league_stats["over_3_5_goals_percentage"] = safe_extract(
        soup,
        'td[valign="top"][width="40%"] table tr:nth-child(3) td:nth-last-child(1) b',
        value_type=float,
    )
    league_stats["total_goals_scored"] = safe_extract(
        soup, 'td[align="right"]:contains("Goals:") + td b', value_type=int
    )
    league_stats["matches_played"] = safe_extract(
        soup,
        'td[valign="top"][width="19%"] table tr.trow3 td[valign="middle"][align="center"] b',
        value_type=int,
    )

    matches_text = safe_extract(
        soup,
        'td[valign="top"][width="19%"] table tr.trow3 td[valign="middle"][align="center"]',
        value_type=str,
    )

    if matches_text:
        try:
            league_stats["total_matches"] = int(matches_text.split("/")[-1].strip())
        except (ValueError, IndexError):
            print("Error: Unable to extract total matches from the text.")
    else:
        print("Error: Unable to find the matches text.")

    league_stats["completion_percentage"] = safe_extract(
        soup,
        'tr.trow3[height="14"] td[valign="middle"][align="center"] font[size="1"]',
        value_type=float,
    )
    league_stats["first_half_goals_percentage"] = safe_extract(
        soup,
        'tr.odd[height="30"] td[align="right"]:has(b:contains("1st half")) ~ td:last-child font[color="blue"]',
        value_type=float,
    )
    league_stats["second_half_goals_percentage"] = safe_extract(
        soup,
        'tr.odd[height="30"] td[align="center"][width="60"] font[color="blue"]',
        value_type=float,
    )
    league_stats["avg_minute_home_team_scored_first"] = safe_extract(
        soup,
        'td:-soup-contains("Average minute when home team scored first:") + td',
        value_type=str,
    )
    league_stats["avg_minute_away_team_scored_first"] = safe_extract(
        soup,
        'td:-soup-contains("Average minute when away team scored first:") + td',
        value_type=str,
    )
    league_stats["home_team_lead_defending_rate"] = safe_extract(
        soup,
        'td:-soup-contains("Home team lead-defending rate:") + td',
        value_type=str,
    )
    league_stats["away_team_lead_defending_rate"] = safe_extract(
        soup,
        'td:-soup-contains("Away team lead-defending rate:") + td',
        value_type=str,
    )
    league_stats["home_team_equalizing_rate"] = safe_extract(
        soup,
        'td:-soup-contains("Home team equalizing rate:") + td',
        value_type=str,
    )
    league_stats["away_team_equalizing_rate"] = safe_extract(
        soup,
        'td:-soup-contains("Away team equalizing rate:") + td',
        value_type=str,
    )
    league_stats["home_team_lead_duration"] = safe_extract(
        soup,
        'td:-soup-contains("Home team lead duration:") + td',
        value_type=str,
    )
    league_stats["away_team_lead_duration"] = safe_extract(
        soup,
        'td:-soup-contains("Away team lead duration:") + td',
        value_type=str,
    )
    league_stats["teams_level_in_goals_percentage"] = safe_extract(
        soup,
        'td:-soup-contains("Teams level in goals:") + td',
        value_type=str,
    )
    league_stats["home_ppg_when_home_team_scored_first"] = safe_extract(
        soup,
        'td:-soup-contains("Home PPG when home team scored first:") + td',
        value_type=str,
    )
    league_stats["away_ppg_when_away_team_scored_first"] = safe_extract(
        soup,
        'td:-soup-contains("Away PPG when away team scored first:") + td',
        value_type=str,
    )
    league_stats["home_team_scored_first_percentage"] = safe_extract(
        soup,
        'td:-soup-contains("Home team scored first:") + td',
        value_type=str,
    )
    league_stats["no_goal_scored_percentage"] = safe_extract(
        soup,
        'td:-soup-contains("No goal scored:") + td',
        value_type=str,
    )
    league_stats["away_team_scored_first_percentage"] = safe_extract(
        soup,
        'td:-soup-contains("Away team scored first:") + td',
        value_type=str,
    )

    return league_stats
