import os
import re

def uncomment_urls(file_path):
    with open(file_path, "r") as file:
        content = file.read()

    # Use regex to find and uncomment the URLs in the HEAD_TO_HEAD_URLS dictionary
    pattern = r'"HEAD_TO_HEAD_URLS":\s*{([^}]*)}'
    match = re.search(pattern, content)

    if match:
        head_to_head_content = match.group(1)
        uncommented_content = re.sub(r'# "([^"]+)"', r'"\1"', head_to_head_content)
        modified_content = content.replace(head_to_head_content, uncommented_content)

        with open(file_path, "w") as file:
            file.write(modified_content)
        print(f"URLs in HEAD_TO_HEAD_URLS have been uncommented in {file_path}.")
    else:
        print(f"No HEAD_TO_HEAD_URLS dictionary found in {file_path}.")

def process_directory(directory_path):
    for root, dirs, files in os.walk(directory_path):
        for file_name in files:
            if file_name.endswith(".py"):
                file_path = os.path.join(root, file_name)
                uncomment_urls(file_path)

# Specify the path to the league_configs directory
directory_path = os.path.join(os.path.dirname(__file__), "league_configs")
process_directory(directory_path)
