import undetected_chromedriver as uc
import requests
from bs4 import BeautifulSoup
import csv
import re
import time
import sys
from config import LEAGUE_CONFIGS
from collections import deque
from datetime import datetime, timedelta
import threading
from typing import Optional, Dict, Deque, List # Added List
import random
import logging

USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
    "Mozilla/5.0 (Windows NT 6.1; WOW64; rv:54.0) Gecko/20100101 Firefox/54.0",
    "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.81 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:94.0) Gecko/20100101 Firefox/94.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Edg/95.0.1020.53",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
    "Mozilla/5.0 (X11; Linux x86_64; rv:94.0) Gecko/20100101 Firefox/94.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Vivaldi/4.3",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36 Vivaldi/4.3",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/18.17763 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15",
    "Mozilla/5.0 (Linux; Android 11; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Mobile Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:88.0) Gecko/20100101 Firefox/88.0",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:90.0) Gecko/20100101 Firefox/90.0",
    "Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.190 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 11_2_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; Trident/7.0; rv:11.0) like Gecko",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:95.0) Gecko/20100101 Firefox/95.0",
    "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:95.0) Gecko/20100101 Firefox/95.0",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Safari/605.1.15",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPad; CPU OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.2 Mobile/15E148 Safari/604.1",
]


class RateLimitedScraper:
    def __init__(
        self,
        requests_per_window: int = 10,
        time_window_seconds: int = 60,
        base_delay: float = 5.0,
        max_retries: int = 30,
        user_agents: list = None,
    ):
        """
        Initialize the rate-limited scraper with configurable parameters.

        Args:
            requests_per_window: Maximum number of requests allowed in the time window
            time_window_seconds: Time window in seconds for rate limiting
            base_delay: Base delay between requests in seconds
            max_retries: Maximum number of retry attempts
            user_agents: List of user agent strings to rotate through
        """
        self.requests_per_window = requests_per_window
        self.time_window_seconds = time_window_seconds
        self.base_delay = base_delay
        self.max_retries = max_retries

        # Request tracking
        self.request_timestamps: Deque[datetime] = deque(maxlen=requests_per_window)
        self.lock = threading.Lock()

        # Session management
        self.driver = uc.Chrome(version_main=138)
        self.user_agents = user_agents or [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36",
            # ... add more user agents as needed
        ]

        # Error tracking
        self.consecutive_429s = 0
        self.last_successful_request: Optional[datetime] = None
        
        # Cloudflare bypass tracking
        self._cloudflare_bypassed_urls = set()

    def _wait_for_rate_limit(self) -> None:
        """Ensure we don't exceed the rate limit by waiting if necessary."""
        with self.lock:
            now = datetime.now()

            # Remove timestamps older than our time window
            while (
                self.request_timestamps
                and (now - self.request_timestamps[0]).total_seconds()
                > self.time_window_seconds
            ):
                self.request_timestamps.popleft()

            # If we've hit our request limit, wait until we can make another request
            if len(self.request_timestamps) >= self.requests_per_window:
                sleep_time = (
                    self.request_timestamps[0]
                    + timedelta(seconds=self.time_window_seconds)
                    - now
                ).total_seconds()
                if sleep_time > 0:
                    time.sleep(sleep_time + random.uniform(1, 3))  # Add jitter

    def _get_retry_delay(
        self, attempt: int, status_code: Optional[int] = None
    ) -> float:
        """Calculate delay before next retry with exponential backoff and jitter."""
        if status_code == 429:
            # Increase delay significantly on 429s
            self.consecutive_429s += 1
            base = min(
                300, self.base_delay * (3**self.consecutive_429s)
            )  # Cap at 5 minutes
        else:
            self.consecutive_429s = 0
            base = self.base_delay * (2**attempt)

        # Add randomization (30% variance)
        jitter = random.uniform(-0.3 * base, 0.3 * base)
        return base + jitter

    def _update_request_tracking(self) -> None:
        """Update request tracking after a successful request."""
        with self.lock:
            self.request_timestamps.append(datetime.now())
            self.last_successful_request = datetime.now()
            self.consecutive_429s = 0

    def _check_cloudflare_challenge(self, page_source: str) -> bool:
        """
        Check if the page contains a Cloudflare challenge.
        
        Args:
            page_source: The HTML content of the page
            
        Returns:
            True if Cloudflare challenge is detected, False otherwise
        """
        cloudflare_indicators = [
            'Checking your browser',
            'cf-browser-verification',
            'jschl-answer',
            'Just a moment...',
            'Enable JavaScript and cookies to continue',
            'DDoS protection by Cloudflare',
            'Verifying you are human',
            'Attention Required!',
            'challenge-running',
            'challenge-form'
        ]
        
        if not page_source:
            return False
            
        page_source_lower = page_source.lower()
        
        # Check page title for common Cloudflare indicators
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', page_source, re.IGNORECASE)
        if title_match:
            title = title_match.group(1).strip().lower()
            title_indicators = ['just a moment', 'attention required', 'access denied', 'security check']
            if any(indicator in title for indicator in title_indicators):
                return True
        
        return any(indicator.lower() in page_source_lower for indicator in cloudflare_indicators)

    def _wait_for_manual_bypass(self, url: str) -> bool:
        """
        Wait for user to manually bypass Cloudflare challenge.
        
        Args:
            url: The URL that triggered the challenge
            
        Returns:
            True if user confirms bypass is complete, False if user wants to skip
        """
        print("\n" + "="*70)
        print("🚨 CLOUDFLARE CHALLENGE DETECTED")
        print("="*70)
        print(f"URL: {url}")
        print("\n📋 INSTRUCTIONS:")
        print("1. Look at the browser window that opened")
        print("2. Complete the Cloudflare challenge (CAPTCHA, checkbox, etc.)")
        print("3. Wait for the actual page to load completely")
        print("4. Verify you see football results/league content")
        print("\n⏰ The scraper will wait for your input...")
        print("\nOptions:")
        print("  y + Enter = Challenge completed, continue scraping")
        print("  n + Enter = Skip this URL and move to next")
        print("  q + Enter = Quit the entire scraping process")
        print("="*70)
        
        while True:
            user_input = input("\nYour choice (y/n/q): ").strip().lower()
            if user_input == 'y':
                print("✅ Continuing with scraping...")
                return True
            elif user_input == 'n':
                print("⏭️  Skipping this URL...")
                return False
            elif user_input == 'q':
                print("🛑 Quitting scraper...")
                # Clean exit
                try:
                    self.driver.quit()
                except:
                    pass
                sys.exit(0)
            else:
                print("Please enter 'y' for yes, 'n' for no, or 'q' to quit.")

    def get(self, url: str, headers: Optional[Dict] = None) -> Optional[str]:
        """
        Make a GET request with rate limiting and retry logic.

        Args:
            url: The URL to request
            headers: Optional additional headers

        Returns:
            Page source string if successful, None if request failed after retries
        """
        for attempt in range(self.max_retries):
            try:
                self._wait_for_rate_limit()

                self.driver.get(url)
                
                # Check for Cloudflare challenge
                page_source = self.driver.page_source
                if self._check_cloudflare_challenge(page_source):
                    logging.warning(f"Cloudflare challenge detected for URL: {url}")
                    
                    # Wait for manual bypass
                    if self._wait_for_manual_bypass(url):
                        # Re-check the page after manual bypass
                        page_source = self.driver.page_source
                        if self._check_cloudflare_challenge(page_source):
                            logging.error("Cloudflare challenge still present after manual bypass")
                            if attempt < self.max_retries - 1:
                                delay = self._get_retry_delay(attempt)
                                logging.warning(f"Retrying in {delay:.2f} seconds...")
                                time.sleep(delay)
                                continue
                            else:
                                return None
                        else:
                            logging.info("Cloudflare challenge successfully bypassed")
                            # Continue with successful page source
                    else:
                        logging.warning(f"Skipping URL due to Cloudflare challenge: {url}")
                        return None
                
                self._update_request_tracking()
                return page_source

            except Exception as e:
                if attempt < self.max_retries - 1:
                    delay = self._get_retry_delay(attempt)
                    logging.warning(
                        f"Request failed due to {str(e)}. Retrying in {delay:.2f} seconds..."
                    )
                    time.sleep(delay)
                else:
                    logging.error(f"Request failed for URL: {url} after {attempt+1} retries: {str(e)}")
                    return None


def scrape_team_results(url: str, team_name: str, scraper: RateLimitedScraper, start_date_dt: Optional[datetime] = None) -> List[list]:
    page_source = scraper.get(url)
    if page_source is None:
        logging.error(f"Failed to get results for team {team_name} from URL: {url}")
        return []
        
    soup = BeautifulSoup(page_source, "html.parser")
    table = soup.find("table", {"bgcolor": "#cccccc"})
    
    if table is None:
        logging.warning(f"Could not find results table for team {team_name} on page {url}. This may be because the season has not started yet.")
        logging.debug(f"Page source for {team_name}:\n{page_source}")
        return []

    results = []
    current_year = datetime.now().year # For parsing dates that might not have a year

    for row in table.find_all("tr", {"height": ["36", "24"]}):
        cells = row.find_all("td")

        if len(cells) == 1 and cells[0].has_attr('colspan'):
            logging.debug(f"Skipping filter link row: {cells}")
            continue

        if len(cells) < 4:
            logging.debug(f"Skipping row with insufficient cells: {cells}")
            continue

        date_str = cells[0].text.strip()
        
        # Attempt to parse date
        match_date_dt = None
        # Common formats: "Oct 19, 2024", "19/10/2024", "Oct 19" (assume current year)
        date_formats_to_try = ["%b %d, %Y", "%d/%m/%Y", "%Y/%m/%d"]
        
        for fmt in date_formats_to_try:
            try:
                match_date_dt = datetime.strptime(date_str, fmt)
                break
            except ValueError:
                continue
        
        if not match_date_dt: # Try parsing without year, assuming current year
            try:
                # Handle cases like "Oct 19" or "17 Aug"
                for fmt in ["%b %d", "%d %b"]:
                    try:
                        temp_date_dt = datetime.strptime(date_str, fmt)
                        break
                    except ValueError:
                        continue
                else:
                    # If neither format worked, log and skip
                    logging.warning(f"Could not parse date string: '{date_str}' for team {team_name} on {url}. Skipping row.")
                    continue
                
                # For football season spanning across years (Aug 2024 - May 2025)
                # Assign years based on month: Aug-Dec = 2024, Jan-May = 2025
                if temp_date_dt.month >= 8:  # Aug, Sep, Oct, Nov, Dec
                    match_date_dt = temp_date_dt.replace(year=2024)
                else:  # Jan, Feb, Mar, Apr, May, Jun, Jul
                    match_date_dt = temp_date_dt.replace(year=2025)
                    
            except ValueError:
                logging.warning(f"Could not parse date string: '{date_str}' for team {team_name} on {url}. Skipping row.")
                continue
        
        if start_date_dt and match_date_dt <= start_date_dt:
            logging.debug(f"Skipping result for {team_name} on {match_date_dt.strftime('%Y-%m-%d')} as it's not newer than {start_date_dt.strftime('%Y-%m-%d')}")
            continue

        home_team = cells[1].text.strip()
        score_cell = cells[2].find("a")
        score_text = score_cell.text.strip() if score_cell else cells[2].text.strip()

        score_match = re.match(r"(\d+)\s*-\s*(\d+)", score_text) # Made regex more flexible for spaces around hyphen
        if score_match:
            score = f"{score_match.group(1)} - {score_match.group(2)}"
            home_goals, away_goals = int(score_match.group(1)), int(score_match.group(2))
        else:
            # Handle postponed/cancelled matches if score is not in "X - Y" format
            if any(indicator in score_text.upper() for indicator in ["POSTP", "CANC", "ABD"]):
                 logging.info(f"Match {home_team} vs {cells[3].text.strip()} on {date_str} is {score_text}. Skipping.")
                 continue
            logging.warning(f"Skipping row with invalid score format: '{score_text}' for {home_team} vs {cells[3].text.strip()} on {date_str}")
            continue
        
        away_team = cells[3].text.strip()

        # Determine result based on the perspective of 'team_name'
        # Team column = the team whose page we're scraping (team_name)
        # Result column = result from that team's perspective
        result_code = -1 # Default to unknown

        if team_name.lower() in home_team.lower():
            # team_name is playing at home
            if home_goals > away_goals: result_code = 0  # Home win for team_name
            elif home_goals == away_goals: result_code = 1  # Home draw for team_name
            else: result_code = 2  # Home loss for team_name
        elif team_name.lower() in away_team.lower():
            # team_name is playing away
            if away_goals > home_goals: result_code = 3  # Away win for team_name
            elif away_goals == home_goals: result_code = 4  # Away draw for team_name
            else: result_code = 5  # Away loss for team_name
        else:
            # This match doesn't involve the team we're scraping for - skip it
            logging.warning(
                f"Team '{team_name}' not found in match: {home_team} vs {away_team} on {url}. Skipping this match."
            )
            continue  # Skip matches that don't involve the team we're scraping for

        # Use the original date string for CSV to maintain original format
        results.append([date_str, home_team, score, away_team, result_code, team_name])

    return results


def scrape_all_results(team_urls: Dict[str, str], scraper: RateLimitedScraper, start_date_dt: Optional[datetime] = None) -> List[list]:
    logging.info(f"🚀 Starting scrape_all_results for {len(team_urls)} teams sequentially")
    logging.info(f"📅 Start date: {start_date_dt.strftime('%Y-%m-%d') if start_date_dt else 'No limit (full history)'}")

    all_results = []
    processed_matches = set()

    for team_name, url in team_urls.items():
        try:
            team_specific_results = scrape_team_results(url, team_name, scraper, start_date_dt)
            if team_specific_results:
                logging.info(f"✅ Got {len(team_specific_results)} results for {team_name}")

                for result in team_specific_results:
                    sorted_teams = tuple(sorted([result[1], result[3]]))
                    match_id = (result[0], sorted_teams)

                    if match_id not in processed_matches:
                        all_results.append(result)
                        processed_matches.add(match_id)
                    else:
                        logging.debug(f"Skipping duplicate match: {result[1]} vs {result[3]} on {result[0]}")
        except Exception as exc:
            logging.error(f'{team_name} generated an exception during results scraping: {exc}')

    logging.info(f"🎉 Completed scraping! Total results: {len(all_results)}")
    return all_results


if __name__ == "__main__":
    # Use the first league's configuration for testing
    first_league_name = list(LEAGUE_CONFIGS.keys())[0]
    first_league_config = LEAGUE_CONFIGS[first_league_name]
    results = scrape_all_results(first_league_config["TEAM_URLS"])

    # Write all results to a CSV file
    with open(
        f"../data/{first_league_name}_all_team_results.csv",
        "w",
        newline="",
        encoding="utf-8",
    ) as file:
        writer = csv.writer(file)
        writer.writerow(["Date", "Home Team", "Score", "Away Team", "Result", "Team"])
        writer.writerows(results)

    print(
        f"Data has been successfully scraped and saved to 'data/{first_league_name}_all_team_results.csv'"
    )
