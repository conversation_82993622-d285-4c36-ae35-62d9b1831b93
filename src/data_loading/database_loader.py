#!/usr/bin/env python3
"""
Database Data Loader

This module replaces CSV-based data loading with database queries,
providing a more efficient and centralized data access system.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple
import sys
import os

# Add database module to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from database.football_db import FootballDatabase, get_database

logger = logging.getLogger(__name__)

class DatabaseDataLoader:
    """Data loader that uses the SQLite database instead of CSV files."""
    
    def __init__(self, db_path='data/football_betting.db'):
        self.db_path = db_path
        self.db = FootballDatabase(db_path)
    
    def get_available_leagues(self) -> List[str]:
        """Get list of available leagues from database."""
        with self.db as db:
            leagues_df = db.get_leagues()
            return leagues_df['league_name'].tolist()
    
    def load_league_data(self, league_name: str) -> <PERSON>ple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Load all data for a league from database.
        
        Returns:
            Tuple of (results, team_stats, league_stats, h2h_stats, league_table)
        """
        logger.info(f"Loading data for league: {league_name}")
        
        with self.db as db:
            # Load match results
            results = self._load_results(db, league_name)
            
            # Load team statistics
            team_stats = self._load_team_stats(db, league_name)
            
            # Load league statistics
            league_stats = self._load_league_stats(db, league_name)
            
            # Load head-to-head statistics
            h2h_stats = self._load_h2h_stats(db, league_name)
            
            # Load league table
            league_table = self._load_league_table(db, league_name)
        
        logger.info(f"Data loaded successfully for {league_name}")
        logger.info(f"  Results: {len(results)} records")
        logger.info(f"  Team stats: {len(team_stats)} records")
        logger.info(f"  League stats: {len(league_stats)} records")
        logger.info(f"  H2H stats: {len(h2h_stats)} records")
        logger.info(f"  League table: {len(league_table)} records")
        
        return results, team_stats, league_stats, h2h_stats, league_table
    
    def _load_results(self, db: FootballDatabase, league_name: str) -> pd.DataFrame:
        """Load match results from database."""
        results = db.get_match_results(league_name)
        
        if results.empty:
            logger.warning(f"No match results found for {league_name}")
            return pd.DataFrame()
        
        # Convert to format expected by prediction system
        results_formatted = pd.DataFrame()
        results_formatted['Date'] = results['match_date']
        results_formatted['Home Team'] = results['home_team']
        results_formatted['Away Team'] = results['away_team']
        results_formatted['Home Goals'] = results['home_score']
        results_formatted['Away Goals'] = results['away_score']
        results_formatted['Result'] = results['result_code']
        
        # Create score string
        results_formatted['Score'] = results.apply(
            lambda row: f"{row['home_score']} - {row['away_score']}" 
            if pd.notna(row['home_score']) and pd.notna(row['away_score']) 
            else None, axis=1
        )
        
        return results_formatted
    
    def _load_team_stats(self, db: FootballDatabase, league_name: str) -> pd.DataFrame:
        """Load team statistics from database."""
        # Get all teams in the league
        teams = db.get_teams(league_name)
        
        if teams.empty:
            logger.warning(f"No teams found for {league_name}")
            return pd.DataFrame()
        
        # Get team statistics for all teams
        all_team_stats = []
        for _, team in teams.iterrows():
            team_stats = db.get_team_stats(team['team_name'], league_name)
            if not team_stats.empty:
                all_team_stats.append(team_stats)
        
        if not all_team_stats:
            logger.warning(f"No team statistics found for {league_name}")
            return pd.DataFrame()
        
        # Combine all team stats
        team_stats_df = pd.concat(all_team_stats, ignore_index=True)
        
        # Rename team_name to Team for consistency
        team_stats_df = team_stats_df.rename(columns={'team_name': 'Team'})
        
        return team_stats_df
    
    def _load_league_stats(self, db: FootballDatabase, league_name: str) -> pd.DataFrame:
        """Load league statistics from database."""
        league_stats = db.get_league_stats(league_name)
        
        if league_stats.empty:
            logger.warning(f"No league statistics found for {league_name}")
            return pd.DataFrame()
        
        # Convert to format expected by prediction system
        league_stats_formatted = pd.DataFrame()
        league_stats_formatted['Stat'] = league_stats['stat_name']
        league_stats_formatted['Value'] = league_stats['stat_value']
        
        return league_stats_formatted
    
    def _load_h2h_stats(self, db: FootballDatabase, league_name: str) -> pd.DataFrame:
        """Load head-to-head statistics from database."""
        h2h_stats = db.get_all_h2h_stats(league_name)
        
        if h2h_stats.empty:
            logger.warning(f"No H2H statistics found for {league_name}")
            return pd.DataFrame()
        
        # Convert to format expected by prediction system
        h2h_formatted = pd.DataFrame()
        h2h_formatted['Matchup'] = h2h_stats['matchup']
        h2h_formatted['team_a_name'] = h2h_stats['home_team']
        h2h_formatted['team_b_name'] = h2h_stats['away_team']
        h2h_formatted['total_matches'] = h2h_stats['total_matches']
        h2h_formatted['team_a_win_percentage'] = h2h_stats['home_win_percentage']
        h2h_formatted['team_b_win_percentage'] = h2h_stats['away_win_percentage']
        h2h_formatted['draw_percentage'] = h2h_stats['draw_percentage']
        h2h_formatted['team_a_wins'] = h2h_stats['home_wins']
        h2h_formatted['team_b_wins'] = h2h_stats['away_wins']
        h2h_formatted['draws'] = h2h_stats['draws']
        h2h_formatted['team_a_goals'] = h2h_stats['home_goals']
        h2h_formatted['team_b_goals'] = h2h_stats['away_goals']
        h2h_formatted['btts_percentage'] = h2h_stats['btts_percentage']
        h2h_formatted['over_1_5_percentage'] = h2h_stats['over_1_5_percentage']
        h2h_formatted['over_2_5_percentage'] = h2h_stats['over_2_5_percentage']
        h2h_formatted['over_3_5_percentage'] = h2h_stats['over_3_5_percentage']
        h2h_formatted['recent_results'] = h2h_stats['recent_results']
        
        return h2h_formatted
    
    def _load_league_table(self, db: FootballDatabase, league_name: str) -> pd.DataFrame:
        """Load league table from database."""
        league_table = db.get_league_table(league_name)
        
        if league_table.empty:
            logger.warning(f"No league table found for {league_name}")
            return pd.DataFrame()
        
        # Rename team_name to Team for consistency
        league_table = league_table.rename(columns={'team_name': 'Team'})
        
        return league_table
    
    def get_team_recent_form(self, team_name: str, league_name: str, last_n_matches: int = 5) -> pd.DataFrame:
        """Get recent form for a team."""
        with self.db as db:
            return db.get_team_form(team_name, league_name, last_n_matches)
    
    def get_head_to_head_between_teams(self, team1: str, team2: str, league_name: str) -> pd.DataFrame:
        """Get head-to-head statistics between two specific teams."""
        with self.db as db:
            return db.get_head_to_head_stats(team1, team2, league_name)
    
    def validate_league_data(self, league_name: str) -> bool:
        """Validate that a league has sufficient data for predictions."""
        try:
            results, team_stats, league_stats, h2h_stats, league_table = self.load_league_data(league_name)
            
            # Check if essential data is present
            if results.empty:
                logger.warning(f"No match results for {league_name}")
                return False
            
            if team_stats.empty:
                logger.warning(f"No team statistics for {league_name}")
                return False
            
            if league_stats.empty:
                logger.warning(f"No league statistics for {league_name}")
                return False
            
            # H2H and league table are optional but recommended
            if h2h_stats.empty:
                logger.info(f"No H2H statistics for {league_name} (optional)")
            
            if league_table.empty:
                logger.info(f"No league table for {league_name} (optional)")
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating data for {league_name}: {e}")
            return False

# Compatibility functions for existing code
def load_data(league_name: str, league_config: Dict = None) -> Tuple[Tuple, Dict]:
    """
    Load data for a league using the database system.
    
    This function maintains compatibility with the existing prediction system
    while using the new database backend.
    """
    loader = DatabaseDataLoader()
    
    # Validate league exists
    if not loader.validate_league_data(league_name):
        logger.error(f"Invalid or insufficient data for league: {league_name}")
        return None, league_config
    
    # Load all data
    data_tuple = loader.load_league_data(league_name)
    
    return data_tuple, league_config

def get_available_leagues() -> List[str]:
    """Get list of available leagues from database."""
    loader = DatabaseDataLoader()
    return loader.get_available_leagues()

def validate_and_convert_data(data_tuple: Tuple) -> Tuple:
    """
    Validate and convert data types for compatibility.
    
    This ensures the database-loaded data is compatible with
    the existing prediction pipeline.
    """
    results, team_stats, league_stats, h2h_stats, league_table = data_tuple
    
    # Convert numeric columns to appropriate types
    numeric_columns = [
        'Home Goals', 'Away Goals', 'Result'
    ]
    
    for col in numeric_columns:
        if col in results.columns:
            results[col] = pd.to_numeric(results[col], errors='coerce')
    
    # Ensure team stats have numeric columns
    if not team_stats.empty:
        for col in team_stats.columns:
            if col != 'Team':
                team_stats[col] = pd.to_numeric(team_stats[col], errors='coerce')
    
    # Ensure league stats have numeric values
    if not league_stats.empty:
        league_stats['Value'] = pd.to_numeric(league_stats['Value'], errors='coerce')
    
    # Handle H2H stats numeric columns
    if not h2h_stats.empty:
        h2h_numeric_cols = [
            'total_matches', 'team_a_win_percentage', 'team_b_win_percentage', 'draw_percentage',
            'team_a_wins', 'team_b_wins', 'draws', 'team_a_goals', 'team_b_goals',
            'btts_percentage', 'over_1_5_percentage', 'over_2_5_percentage', 'over_3_5_percentage'
        ]
        
        for col in h2h_numeric_cols:
            if col in h2h_stats.columns:
                h2h_stats[col] = pd.to_numeric(h2h_stats[col], errors='coerce')
    
    # Handle league table numeric columns
    if not league_table.empty:
        table_numeric_cols = ['position', 'MP', 'W', 'D', 'L', 'GF', 'GA', 'GD', 'Pts']
        
        for col in table_numeric_cols:
            if col in league_table.columns:
                league_table[col] = pd.to_numeric(league_table[col], errors='coerce')
    
    return results, team_stats, league_stats, h2h_stats, league_table

# Example usage and testing
if __name__ == "__main__":
    # Test the database loader
    loader = DatabaseDataLoader()
    
    # Get available leagues
    leagues = loader.get_available_leagues()
    print(f"Available leagues: {len(leagues)}")
    
    if leagues:
        # Test loading data for first league
        test_league = leagues[0]
        print(f"\nTesting data load for: {test_league}")
        
        try:
            data = loader.load_league_data(test_league)
            results, team_stats, league_stats, h2h_stats, league_table = data
            
            print(f"✅ Successfully loaded data:")
            print(f"   Results: {len(results)} records")
            print(f"   Team stats: {len(team_stats)} records") 
            print(f"   League stats: {len(league_stats)} records")
            print(f"   H2H stats: {len(h2h_stats)} records")
            print(f"   League table: {len(league_table)} records")
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
    else:
        print("No leagues found in database. Run create_database.py first.")