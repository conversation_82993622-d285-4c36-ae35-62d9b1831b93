import sqlite3
import pandas as pd
import numpy as np
from .. import utils
import os
import glob
from datetime import datetime
import logging
import json

PROJECT_ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# --- Constants ---
DB_PATH = os.path.join(PROJECT_ROOT_DIR, 'data', 'sports_data.db')
RAW_DATA_DIR = os.path.join(PROJECT_ROOT_DIR, 'data', 'raw/')
LOG_DIR = os.path.join(PROJECT_ROOT_DIR, 'logs')
LOG_FILE = os.path.join(LOG_DIR, 'data_ingestion.log')
CURRENT_SEASON = "2024-2025"
CURRENT_SEASON_YEAR_START = 2024

# --- Logger Setup ---
os.makedirs(LOG_DIR, exist_ok=True)
logger = logging.getLogger(__name__)
if not logger.handlers: # Avoid adding multiple handlers
    logger.setLevel(logging.DEBUG)
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    fh = logging.FileHandler(LOG_FILE, mode='a')
    fh.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
    ch.setFormatter(formatter)
    fh.setFormatter(formatter)
    logger.addHandler(ch)
    logger.addHandler(fh)

# --- Table Creation SQL (omitted for brevity in this explanation, same as before) ---
CREATE_LEAGUE_TABLES_SQL = """
CREATE TABLE IF NOT EXISTS league_tables (
    league_name TEXT,
    season TEXT,
    position INTEGER,
    team_name TEXT,
    matches_played INTEGER,
    wins INTEGER,
    draws INTEGER,
    losses INTEGER,
    goals_for INTEGER,
    goals_against INTEGER,
    goal_difference INTEGER,
    points INTEGER,
    last_updated TIMESTAMP,
    PRIMARY KEY (league_name, season, team_name)
);
"""

CREATE_MATCH_RESULTS_SQL = """
CREATE TABLE IF NOT EXISTS match_results (
    league_name TEXT,
    season TEXT,
    match_date TEXT, 
    home_team TEXT,
    away_team TEXT,
    home_goals INTEGER,
    away_goals INTEGER,
    score TEXT, 
    result_code TEXT, 
    perspective_team TEXT, 
    last_updated TIMESTAMP,
    PRIMARY KEY (league_name, season, match_date, home_team, away_team)
);
"""

CREATE_HEAD_TO_HEAD_STATS_SQL = """
CREATE TABLE IF NOT EXISTS head_to_head_stats (
    league_name TEXT,
    season TEXT, 
    team_a TEXT,
    team_b TEXT,
    total_matches INTEGER,
    team_a_win_percentage REAL,
    team_b_win_percentage REAL,
    draw_percentage REAL,
    team_a_wins INTEGER,
    team_b_wins INTEGER,
    draws INTEGER,
    team_a_goals INTEGER,
    team_b_goals INTEGER,
    over_1_5_percentage REAL,
    over_2_5_percentage REAL,
    over_3_5_percentage REAL,
    btts_percentage REAL,
    team_a_clean_sheet_percentage REAL,
    team_b_clean_sheet_percentage REAL,
    h2h_recent_results_raw TEXT,
    last_updated TIMESTAMP,
    PRIMARY KEY (league_name, season, team_a, team_b)
);
"""

CREATE_LEAGUE_SUMMARY_STATS_SQL = """
CREATE TABLE IF NOT EXISTS league_summary_stats (
    league_name TEXT,
    season TEXT,
    stat_name TEXT,
    stat_value TEXT, 
    last_updated TIMESTAMP,
    PRIMARY KEY (league_name, season, stat_name)
);
"""

CREATE_TEAM_DETAILED_STATS_SQL = """
CREATE TABLE IF NOT EXISTS team_detailed_stats (
    league_name TEXT,
    season TEXT,
    team_name TEXT,
    total_home_played INTEGER,
    total_home_wins INTEGER,
    total_home_draws INTEGER,
    total_home_losses INTEGER,
    total_away_played INTEGER,
    total_away_wins INTEGER,
    total_away_draws INTEGER,
    total_away_losses INTEGER,
    total_played INTEGER,
    total_wins INTEGER,
    total_draws INTEGER,
    total_losses INTEGER,
    points_per_game REAL,
    home_points_per_game REAL,
    away_points_per_game REAL,
    goals_scored_home INTEGER,
    goals_scored_away INTEGER,
    goals_scored_all INTEGER,
    goals_scored_per_match_home REAL,
    goals_scored_per_match_away REAL,
    goals_scored_per_match_all REAL,
    goals_conceded_home INTEGER,
    goals_conceded_away INTEGER,
    goals_conceded_all INTEGER,
    goals_conceded_per_match_home REAL,
    goals_conceded_per_match_away REAL,
    goals_conceded_per_match_all REAL,
    gf_ga_per_match_home REAL,
    gf_ga_per_match_away REAL,
    gf_ga_per_match_all REAL,
    gf_ga_over_0_5_percentage_home REAL,
    gf_ga_over_0_5_percentage_away REAL,
    gf_ga_over_0_5_percentage_all REAL,
    gf_ga_over_1_5_percentage_home REAL,
    gf_ga_over_1_5_percentage_away REAL,
    gf_ga_over_1_5_percentage_all REAL,
    gf_ga_over_2_5_percentage_home REAL,
    gf_ga_over_2_5_percentage_away REAL,
    gf_ga_over_2_5_percentage_all REAL,
    gf_ga_over_3_5_percentage_home REAL,
    gf_ga_over_3_5_percentage_away REAL,
    gf_ga_over_3_5_percentage_all REAL,
    gf_ga_over_4_5_percentage_home REAL,
    gf_ga_over_4_5_percentage_away REAL,
    gf_ga_over_4_5_percentage_all REAL,
    gf_ga_over_5_5_percentage_home REAL,
    gf_ga_over_5_5_percentage_away REAL,
    gf_ga_over_5_5_percentage_all REAL,
    gf_ga_over_0_5_ht_percentage_home REAL,
    gf_ga_over_0_5_ht_percentage_away REAL,
    gf_ga_over_0_5_ht_percentage_all REAL,
    gf_ga_over_1_5_ht_percentage_home REAL,
    gf_ga_over_1_5_ht_percentage_away REAL,
    gf_ga_over_1_5_ht_percentage_all REAL,
    gf_ga_over_2_5_ht_percentage_home REAL,
    gf_ga_over_2_5_ht_percentage_away REAL,
    gf_ga_over_2_5_ht_percentage_all REAL,
    ppg_last_8 REAL,
    avg_goals_scored_last_8 REAL,
    avg_goals_conceded_last_8 REAL,
    last_updated TIMESTAMP,
    PRIMARY KEY (league_name, season, team_name)
);
"""

# --- Helper Functions ---
def get_db_connection():
    logger.info(f"Attempting to connect to database at: {os.path.abspath(DB_PATH)}")
    logger.info(f"Database parent directory exists: {os.path.exists(os.path.dirname(os.path.abspath(DB_PATH)))}")
    conn = sqlite3.connect(DB_PATH)
    return conn

def create_tables(conn):
    cursor = conn.cursor()
    cursor.execute(CREATE_LEAGUE_TABLES_SQL)
    cursor.execute(CREATE_MATCH_RESULTS_SQL)
    cursor.execute(CREATE_HEAD_TO_HEAD_STATS_SQL)
    cursor.execute(CREATE_LEAGUE_SUMMARY_STATS_SQL)
    cursor.execute(CREATE_TEAM_DETAILED_STATS_SQL)
    conn.commit()
    logger.info("All tables created or already exist.")

def get_season_from_date(date_str, current_season_year_start=CURRENT_SEASON_YEAR_START):
    if pd.isna(date_str) or not date_str: return None
    date_str = str(date_str)
    try: dt_obj = datetime.strptime(date_str, '%b %d, %Y')
    except ValueError:
        try: dt_obj = datetime.strptime(date_str, '%d %b, %Y')
        except ValueError:
            try:
                dt_obj_no_year = datetime.strptime(date_str, '%d %b')
                year_to_use = current_season_year_start + (1 if dt_obj_no_year.month < 8 else 0)
                dt_obj = dt_obj_no_year.replace(year=year_to_use)
            except ValueError:
                try:
                    dt_obj_no_year = datetime.strptime(date_str, '%b %d')
                    year_to_use = current_season_year_start + (1 if dt_obj_no_year.month < 8 else 0)
                    dt_obj = dt_obj_no_year.replace(year=year_to_use)
                except ValueError:
                    logger.warning(f"Could not parse date: {date_str}. Returning None for season.")
                    return None
    return f"{dt_obj.year}-{dt_obj.year + 1}" if dt_obj.month >= 8 else f"{dt_obj.year - 1}-{dt_obj.year}"

# --- Processing Functions for each CSV type (except match_results) ---
def process_league_table(conn, csv_path, league_name):
    try:
        df = pd.read_csv(csv_path)
        if df.empty: logger.info(f"CSV league_table is empty: {csv_path}"); return
        df['league_name'] = league_name
        df['season'] = CURRENT_SEASON
        df['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df.rename(columns={'Position': 'position', 'Team': 'team_name', 'MP': 'matches_played', 'W': 'wins', 'D': 'draws', 'L': 'losses', 'GF': 'goals_for', 'GA': 'goals_against', 'GD': 'goal_difference', 'Pts': 'points'}, inplace=True)
        required = ['league_name', 'season', 'position', 'team_name', 'matches_played', 'wins', 'draws', 'losses', 'goals_for', 'goals_against', 'goal_difference', 'points', 'last_updated']
        for col in required: 
            if col not in df.columns: df[col] = None
        df[required].to_sql('league_tables', conn, if_exists='append', index=False)
        logger.info(f"Processed league table: {csv_path}")
    except Exception as e: logger.exception(f"Error processing league table {csv_path}")

def load_and_prepare_match_results_df(csv_path, league_name, is_recent_results_file=False):
    """Loads a match results CSV, processes it, and returns a DataFrame."""
    try:
        logger.debug(f"Reading match results CSV for preparation: {csv_path}")
        df = pd.read_csv(csv_path)
        if df.empty:
            logger.info(f"Match results CSV is empty: {csv_path}")
            return None

        df['league_name'] = league_name
        df['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if 'Date' in df.columns:
            df.rename(columns={'Date': 'match_date_raw'}, inplace=True)
            df['season'] = df['match_date_raw'].apply(lambda x: get_season_from_date(x))
            df['match_date'] = df['match_date_raw'].astype(str)
        else:
            logger.warning(f"'Date' column not found in {csv_path}. Using fallback season/date.")
            df['season'] = CURRENT_SEASON 
            df['match_date'] = None
        
        # Standardize team name columns if they exist
        if 'Home Team' in df.columns: df.rename(columns={'Home Team': 'home_team'}, inplace=True)
        if 'Away Team' in df.columns: df.rename(columns={'Away Team': 'away_team'}, inplace=True)

        if is_recent_results_file:
            if 'Home Score' in df.columns: df.rename(columns={'Home Score': 'home_goals'}, inplace=True)
            if 'Away Score' in df.columns: df.rename(columns={'Away Score': 'away_goals'}, inplace=True)

            if 'home_goals' in df.columns and 'away_goals' in df.columns:
                hg_str = df['home_goals'].apply(lambda x: str(int(x)) if pd.notna(x) and str(x).strip() != '' else '')
                ag_str = df['away_goals'].apply(lambda x: str(int(x)) if pd.notna(x) and str(x).strip() != '' else '')
                df['score'] = hg_str + " - " + ag_str
                df['score'] = df['score'].str.replace(r'^\s*-\s*', '', regex=True).str.replace(r'\s*-\s*$', '', regex=True).str.strip()
                df.loc[df['score'] == "-", 'score'] = None 
                df.loc[df['score'] == "", 'score'] = None
            else:
                df['score'] = None
            df['result_code'] = None
            df['perspective_team'] = None
        else: # _results.csv
            if 'Result' in df.columns: df.rename(columns={'Result': 'result_code'}, inplace=True)
            if 'Team' in df.columns: df.rename(columns={'Team': 'perspective_team'}, inplace=True)
            if 'Score' in df.columns:
                df.rename(columns={'Score': 'score'}, inplace=True)
            else:
                df['score'] = None

            def parse_score_string(s_elem):
                if pd.isna(s_elem) or not isinstance(s_elem, str) or '-' not in s_elem: return pd.Series([None, None], index=['home_goals', 'away_goals'])
                parts = s_elem.split('-')
                try: return pd.Series([int(parts[0].strip()), int(parts[1].strip())], index=['home_goals', 'away_goals'])
                except: return pd.Series([None, None], index=['home_goals', 'away_goals'])

            if 'score' in df.columns and df['score'].notna().any():
                goals_df = df['score'].apply(parse_score_string)
                df = pd.concat([df, goals_df], axis=1)
            else:
                df['home_goals'] = None
                df['away_goals'] = None
        
        required_db_cols = ['league_name', 'season', 'match_date', 'home_team', 'away_team', 'home_goals', 'away_goals', 'score', 'result_code', 'perspective_team', 'last_updated']
        for col in required_db_cols:
            if col not in df.columns: df[col] = None
        
        return df[required_db_cols]

    except Exception as e:
        logger.exception(f"Error in load_and_prepare_match_results_df for {csv_path}")
        return None

def process_head_to_head_stats(conn, csv_path, league_name):
    try:
        logger.debug(f"Starting H2H processing for {csv_path}")
        df = pd.read_csv(csv_path)
        if df.empty:
            logger.info(f"CSV head_to_head is empty: {csv_path}")
            return

        df['league_name'] = league_name
        # The problem description does not ask to change season logic, keeping original.
        df['season'] = f"as_of_{CURRENT_SEASON}"
        df['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        team_mapping_filename = f"team_mappings/{league_name}_team_mapping.json"
        team_name_mapping = {}
        try:
            # Corrected path assuming script is run from project root and team_mappings is at root
            full_team_mapping_path = os.path.join(PROJECT_ROOT_DIR, team_mapping_filename)
            with open(full_team_mapping_path, 'r') as f:
                team_name_mapping = json.load(f)
            logger.info(f"Loaded team name mapping for {league_name} from {full_team_mapping_path}")
        except FileNotFoundError:
            logger.warning(f"Team name mapping file not found for {league_name} at {full_team_mapping_path}. Using empty mapping.")
        except json.JSONDecodeError:
            logger.error(f"Error decoding JSON from team name mapping file {full_team_mapping_path}. Using empty mapping.")

        standardize_function = utils.standardize_team_name

        # Helper function to parse teams preserving CSV order
        def parse_matchup_teams_original_order(matchup_str, standardize_func, mapping):
            if pd.isna(matchup_str) or ' vs ' not in str(matchup_str): # Ensure matchup_str is string for 'in'
                return pd.Series([None, None], index=['team_1_original_csv_order', 'team_2_original_csv_order'])
            try:
                teams = [team.strip() for team in str(matchup_str).split(' vs ', 1)]
                if len(teams) != 2 or not teams[0] or not teams[1]:
                    return pd.Series([None, None], index=['team_1_original_csv_order', 'team_2_original_csv_order'])
                standardized_team1 = standardize_func(teams[0], mapping)
                standardized_team2 = standardize_func(teams[1], mapping)
                # Return None if standardization results in empty string or None
                if not standardized_team1 or not standardized_team2:
                    return pd.Series([None, None], index=['team_1_original_csv_order', 'team_2_original_csv_order'])
                return pd.Series([standardized_team1, standardized_team2], index=['team_1_original_csv_order', 'team_2_original_csv_order'])
            except Exception as e:
                logger.warning(f"Error parsing original matchup string '{matchup_str}': {e}")
                return pd.Series([None, None], index=['team_1_original_csv_order', 'team_2_original_csv_order'])

        # Helper function to parse and sort teams for DB key
        def parse_matchup_teams_sorted_for_db(matchup_str, standardize_func, mapping):
            if pd.isna(matchup_str) or ' vs ' not in str(matchup_str): # Ensure matchup_str is string
                return pd.Series([None, None], index=['team_a', 'team_b'])
            try:
                teams = [team.strip() for team in str(matchup_str).split(' vs ', 1)]
                if len(teams) != 2 or not teams[0] or not teams[1]:
                     return pd.Series([None, None], index=['team_a', 'team_b'])
                standardized_teams = [standardize_func(teams[0], mapping), standardize_func(teams[1], mapping)]
                if not standardized_teams[0] or not standardized_teams[1]:
                    return pd.Series([None, None], index=['team_a', 'team_b'])
                return pd.Series(sorted(standardized_teams), index=['team_a', 'team_b'])
            except Exception as e:
                logger.warning(f"Error parsing sorted matchup string '{matchup_str}': {e}")
                return pd.Series([None, None], index=['team_a', 'team_b'])

        # Step A: Extract Original Order Teams
        original_teams_df = df['Matchup'].apply(lambda x: parse_matchup_teams_original_order(x, standardize_function, team_name_mapping))
        df = pd.concat([df, original_teams_df], axis=1)

        # Step B: Determine Alphabetically Sorted team_a and team_b
        sorted_teams_df = df['Matchup'].apply(lambda x: parse_matchup_teams_sorted_for_db(x, standardize_function, team_name_mapping))
        df = pd.concat([df, sorted_teams_df], axis=1)
        
        # Step C: Clean up rows with missing team data from parsing
        df.dropna(subset=['team_1_original_csv_order', 'team_2_original_csv_order', 'team_a', 'team_b'], inplace=True)
        if df.empty:
            logger.info(f"No valid H2H data after team parsing for {csv_path}")
            return

        # Step D: Conditionally Assign Statistics
        csv_stat_cols_team_a = ['team_a_win_percentage', 'team_a_wins', 'team_a_goals', 'team_a_clean_sheet_percentage']
        csv_stat_cols_team_b = ['team_b_win_percentage', 'team_b_wins', 'team_b_goals', 'team_b_clean_sheet_percentage']
        
        # Final DB column names (matching the CSV names after correct assignment)
        db_cols_team_a = ['team_a_win_percentage', 'team_a_wins', 'team_a_goals', 'team_a_clean_sheet_percentage']
        db_cols_team_b = ['team_b_win_percentage', 'team_b_wins', 'team_b_goals', 'team_b_clean_sheet_percentage']

        all_csv_stat_cols = csv_stat_cols_team_a + csv_stat_cols_team_b
        for col in all_csv_stat_cols:
            if col not in df.columns:
                logger.warning(f"CSV column '{col}' not found in {csv_path}. Filling with NaN.")
                df[col] = np.nan # Ensure column exists for np.where

        temp_db_cols_mapping = {} # To store temp names for later renaming

        for i in range(len(csv_stat_cols_team_a)):
            csv_col_a = csv_stat_cols_team_a[i]
            csv_col_b = csv_stat_cols_team_b[i]
            
            # These are the final intended names in the DB
            final_db_col_a = db_cols_team_a[i]
            final_db_col_b = db_cols_team_b[i]

            # Use temporary names for the new columns to avoid conflicts during np.where
            temp_col_a_name = f"temp_db_{final_db_col_a}"
            temp_col_b_name = f"temp_db_{final_db_col_b}"
            temp_db_cols_mapping[temp_col_a_name] = final_db_col_a
            temp_db_cols_mapping[temp_col_b_name] = final_db_col_b

            df[temp_col_a_name] = np.where(
                df['team_a'] == df['team_1_original_csv_order'],
                df[csv_col_a],
                df[csv_col_b]
            )
            df[temp_col_b_name] = np.where(
                df['team_a'] == df['team_1_original_csv_order'],
                df[csv_col_b],
                df[csv_col_a]
            )
        
        # Step E: Rename temporary columns to final database column names
        df.rename(columns=temp_db_cols_mapping, inplace=True)

        # Drop intermediate columns and original Matchup
        cols_to_drop = ['team_1_original_csv_order', 'team_2_original_csv_order', 'Matchup']
        # Original CSV stat columns (e.g. 'team_a_win_percentage') are now overwritten by the rename operation.
        # No need to explicitly drop them again if their names matched final_db_col_a/b.
        df.drop(columns=cols_to_drop, inplace=True, errors='ignore')

        # Handle 'h2h_recent_results_raw' (original script renames 'recent_results')
        if 'recent_results' in df.columns and 'h2h_recent_results_raw' not in df.columns:
            df.rename(columns={'recent_results': 'h2h_recent_results_raw'}, inplace=True)
        elif 'recent_results' in df.columns and 'h2h_recent_results_raw' in df.columns:
             # If h2h_recent_results_raw might have been added (e.g. by a full 'required' list) but is empty
             # and 'recent_results' has data, prioritize 'recent_results'.
             if df['h2h_recent_results_raw'].isnull().all() and df['recent_results'].notnull().any():
                 df['h2h_recent_results_raw'] = df['recent_results']
             df.drop(columns=['recent_results'], inplace=True, errors='ignore')


        final_db_columns = [
            'league_name', 'season', 'team_a', 'team_b', 'total_matches',
            'team_a_win_percentage', 'team_b_win_percentage', 'draw_percentage',
            'team_a_wins', 'team_b_wins', 'draws',
            'team_a_goals', 'team_b_goals',
            'over_1_5_percentage', 'over_2_5_percentage', 'over_3_5_percentage',
            'btts_percentage',
            'team_a_clean_sheet_percentage', 'team_b_clean_sheet_percentage',
            'h2h_recent_results_raw', 'last_updated'
        ]
        
        for col in final_db_columns:
            if col not in df.columns:
                logger.warning(f"Final DB column '{col}' missing after processing for {csv_path}. Filling with None.")
                df[col] = np.nan

        df_to_insert = df[final_db_columns].copy() # Use .copy() to avoid SettingWithCopyWarning on type conversion

        percentage_cols_in_db = [
            'team_a_win_percentage', 'team_b_win_percentage', 'draw_percentage',
            'over_1_5_percentage', 'over_2_5_percentage', 'over_3_5_percentage',
            'btts_percentage', 'team_a_clean_sheet_percentage', 'team_b_clean_sheet_percentage'
        ]
        for target_col_name in percentage_cols_in_db:
            if target_col_name in df_to_insert.columns:
                # Check if target_col_name refers to a single Series or multiple columns (a DataFrame)
                # due to potential duplicate column names in df_to_insert.
                selection = df_to_insert[target_col_name]

                if isinstance(selection, pd.DataFrame):
                    # target_col_name is duplicated in df_to_insert.columns
                    logger.warning(
                        f"Duplicate column name '{target_col_name}' in df_to_insert for {csv_path}. "
                        f"Processing all instances by their actual positions."
                    )
                    # Find all original integer positions of columns named target_col_name in df_to_insert
                    # This is important because df_to_insert.iloc[:, idx] gives a Series.
                    # And we need to assign back to these specific positions.
                    original_indices_for_target_col = [
                        i for i, name in enumerate(df_to_insert.columns) if name == target_col_name
                    ]
                    
                    for col_idx in original_indices_for_target_col:
                        series_to_process = df_to_insert.iloc[:, col_idx]
                        if series_to_process.dtype.name == 'object':
                            # Modify the specific column at col_idx in df_to_insert
                            df_to_insert.iloc[:, col_idx] = series_to_process.astype(str).str.rstrip('%').replace('', np.nan).replace('None', np.nan)
                        # Convert this specific column (now potentially modified if it was object type) to numeric
                        df_to_insert.iloc[:, col_idx] = pd.to_numeric(df_to_insert.iloc[:, col_idx], errors='coerce')
                
                elif isinstance(selection, pd.Series):
                    # target_col_name is unique, so selection is a Series
                    if selection.dtype.name == 'object':
                        df_to_insert[target_col_name] = selection.astype(str).str.rstrip('%').replace('', np.nan).replace('None', np.nan)
                    # Convert this column (now potentially modified if it was object type) to numeric
                    df_to_insert[target_col_name] = pd.to_numeric(df_to_insert[target_col_name], errors='coerce')
                
                else:
                    # Should not happen if target_col_name is in df_to_insert.columns
                    logger.error(f"Unexpected type for df_to_insert['{target_col_name}']: {type(selection)}")

        integer_cols_in_db = [
            'total_matches', 'team_a_wins', 'team_b_wins', 'draws',
            'team_a_goals', 'team_b_goals'
        ]
        for target_col_name in integer_cols_in_db:
            if target_col_name in df_to_insert.columns:
                selection = df_to_insert[target_col_name]

                if isinstance(selection, pd.DataFrame):
                    logger.warning(
                        f"Duplicate column name '{target_col_name}' in df_to_insert for {csv_path}. "
                        f"Processing all instances by their actual positions for integer conversion."
                    )
                    original_indices_for_target_col = [
                        i for i, name in enumerate(df_to_insert.columns) if name == target_col_name
                    ]
                    
                    for col_idx in original_indices_for_target_col:
                        series_to_process = df_to_insert.iloc[:, col_idx]
                        # Coerce to numeric first, then to Int64 for nullable integers
                        numeric_series = pd.to_numeric(series_to_process, errors='coerce')
                        try:
                            df_to_insert.iloc[:, col_idx] = numeric_series.astype('Int64')
                        except (AttributeError, TypeError):
                            # Fallback for pandas compatibility issues
                            df_to_insert.iloc[:, col_idx] = numeric_series.fillna(0).astype(int)
                
                elif isinstance(selection, pd.Series):
                    # Coerce to numeric first, then to Int64 for nullable integers
                    numeric_series = pd.to_numeric(selection, errors='coerce')
                    try:
                        df_to_insert[target_col_name] = numeric_series.astype('Int64')
                    except (AttributeError, TypeError):
                        # Fallback for pandas compatibility issues
                        df_to_insert[target_col_name] = numeric_series.fillna(0).astype(int)
                
                else:
                    logger.error(
                        f"Unexpected type for df_to_insert['{target_col_name}'] "
                        f"during integer conversion: {type(selection)} for {csv_path}"
                    )

        df_to_insert.to_sql('head_to_head_stats', conn, if_exists='append', index=False)
        logger.info(f"Processed H2H stats with remapped columns: {csv_path} ({len(df_to_insert)} rows)")

    except Exception as e:
        logger.exception(f"Error processing H2H stats {csv_path}")

def process_league_summary_stats(conn, csv_path, league_name):
    try:
        df = pd.read_csv(csv_path)
        if df.empty: logger.info(f"CSV league_summary is empty: {csv_path}"); return
        df['league_name'] = league_name
        df['season'] = CURRENT_SEASON
        df['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df.rename(columns={'Stat': 'stat_name', 'Value': 'stat_value'}, inplace=True)
        required = ['league_name', 'season', 'stat_name', 'stat_value', 'last_updated']
        for col in required:
            if col not in df.columns: df[col] = None
        df[required].to_sql('league_summary_stats', conn, if_exists='append', index=False)
        logger.info(f"Processed league summary: {csv_path}")
    except Exception as e: logger.exception(f"Error processing league summary {csv_path}")

def process_team_detailed_stats(conn, csv_path, league_name):
    try:
        df = pd.read_csv(csv_path)
        if df.empty: logger.info(f"CSV team_detailed is empty: {csv_path}"); return
        df['league_name'] = league_name
        df['season'] = CURRENT_SEASON
        df['last_updated'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        df.rename(columns={'Team': 'team_name'}, inplace=True)
        # All other columns assumed to match DB schema from CSV
        all_expected_cols = ['league_name', 'season', 'team_name', 'total_home_played', 'total_home_wins', 'total_home_draws', 'total_home_losses', 'total_away_played', 'total_away_wins', 'total_away_draws', 'total_away_losses', 'total_played', 'total_wins', 'total_draws', 'total_losses', 'points_per_game', 'home_points_per_game', 'away_points_per_game', 'goals_scored_home', 'goals_scored_away', 'goals_scored_all', 'goals_scored_per_match_home', 'goals_scored_per_match_away', 'goals_scored_per_match_all', 'goals_conceded_home', 'goals_conceded_away', 'goals_conceded_all', 'goals_conceded_per_match_home', 'goals_conceded_per_match_away', 'goals_conceded_per_match_all', 'gf_ga_per_match_home', 'gf_ga_per_match_away', 'gf_ga_per_match_all', 'gf_ga_over_0_5_percentage_home', 'gf_ga_over_0_5_percentage_away', 'gf_ga_over_0_5_percentage_all', 'gf_ga_over_1_5_percentage_home', 'gf_ga_over_1_5_percentage_away', 'gf_ga_over_1_5_percentage_all', 'gf_ga_over_2_5_percentage_home', 'gf_ga_over_2_5_percentage_away', 'gf_ga_over_2_5_percentage_all', 'gf_ga_over_3_5_percentage_home', 'gf_ga_over_3_5_percentage_away', 'gf_ga_over_3_5_percentage_all', 'gf_ga_over_4_5_percentage_home', 'gf_ga_over_4_5_percentage_away', 'gf_ga_over_4_5_percentage_all', 'gf_ga_over_5_5_percentage_home', 'gf_ga_over_5_5_percentage_away', 'gf_ga_over_5_5_percentage_all', 'gf_ga_over_0_5_ht_percentage_home', 'gf_ga_over_0_5_ht_percentage_away', 'gf_ga_over_0_5_ht_percentage_all', 'gf_ga_over_1_5_ht_percentage_home', 'gf_ga_over_1_5_ht_percentage_away', 'gf_ga_over_1_5_ht_percentage_all', 'gf_ga_over_2_5_ht_percentage_home', 'gf_ga_over_2_5_ht_percentage_away', 'gf_ga_over_2_5_ht_percentage_all', 'ppg_last_8', 'avg_goals_scored_last_8', 'avg_goals_conceded_last_8', 'last_updated']
        for col in all_expected_cols:
            if col not in df.columns: df[col] = None
        df[all_expected_cols].to_sql('team_detailed_stats', conn, if_exists='append', index=False)
        logger.info(f"Processed team detailed stats: {csv_path}")
    except Exception as e: logger.exception(f"Error processing team detailed stats {csv_path}")

# --- Main Execution ---
def main():
    logger.info("Starting CSV to SQLite migration process.")
    conn = get_db_connection()
    create_tables(conn) 

    if True: # CLEAR_TABLES_ON_RUN
        cursor = conn.cursor()
        logger.info("Clearing existing data from tables...")
        try:
            for table in ['league_tables', 'match_results', 'head_to_head_stats', 'league_summary_stats', 'team_detailed_stats']:
                cursor.execute(f"DELETE FROM {table};")
            conn.commit()
            logger.info("Cleared existing data from tables.")
        except sqlite3.OperationalError as e:
            logger.warning(f"Could not clear tables: {e}")

    league_folders = [f.path for f in os.scandir(RAW_DATA_DIR) if f.is_dir()]
    logger.info(f"Found {len(league_folders)} league folders in {RAW_DATA_DIR}.")

    for league_folder_path in league_folders:
        league_name = os.path.basename(league_folder_path)
        if league_name == "DUMMY_LEAGUE" and not any(os.path.getsize(os.path.join(league_folder_path, item)) > 0 for item in os.listdir(league_folder_path) if os.path.isfile(os.path.join(league_folder_path, item))):
            logger.info(f"Skipping empty DUMMY_LEAGUE folder: {league_folder_path}")
            continue
        logger.info(f"Processing league: {league_name}")
        
        all_csv_files_in_league = glob.glob(os.path.join(league_folder_path, "*.csv"))
        
        path_results_csv = None
        path_recent_results_csv = None
        other_csv_files = []

        for csv_file in all_csv_files_in_league:
            filename = os.path.basename(csv_file)
            if "_recent_results.csv" in filename: path_recent_results_csv = csv_file
            elif "_results.csv" in filename: path_results_csv = csv_file
            elif "_league_table.csv" in filename or \
                 "_head_to_head_stats.csv" in filename or \
                 "_league_stats.csv" in filename or \
                 "_team_stats.csv" in filename:
                other_csv_files.append(csv_file)
            else: logger.warning(f"Unknown CSV file type: {filename}. Skipping.")

        # Process Match Results with De-duplication
        match_dfs_to_concat = []
        if path_results_csv: # General results first
            df_general = load_and_prepare_match_results_df(path_results_csv, league_name, is_recent_results_file=False)
            if df_general is not None and not df_general.empty:
                match_dfs_to_concat.append(df_general)
        if path_recent_results_csv: # Recent results after
            df_recent = load_and_prepare_match_results_df(path_recent_results_csv, league_name, is_recent_results_file=True)
            if df_recent is not None and not df_recent.empty:
                match_dfs_to_concat.append(df_recent)
        
        if match_dfs_to_concat:
            combined_df = pd.concat(match_dfs_to_concat, ignore_index=True)
            pk_cols = ['league_name', 'season', 'match_date', 'home_team', 'away_team']
            
            # Clean PK columns before drop_duplicates
            for col in pk_cols:
                if col not in combined_df.columns: # Should not happen if load_and_prepare is correct
                    logger.error(f"Critical: PK column {col} missing before drop_duplicates for {league_name}.")
                    combined_df[col] = None # Add it to prevent error, though data might be flawed
            
            combined_df.dropna(subset=pk_cols, inplace=True) # Drop rows if any PK part is NaN

            # Convert empty strings in PK text columns to None for consistent duplicate checking
            for col in ['match_date', 'home_team', 'away_team']:
                 if col in combined_df.columns:
                    combined_df[col] = combined_df[col].apply(lambda x: None if isinstance(x, str) and not x.strip() else x)
            
            # Keep='last' prioritizes data from _recent_results.csv if it was appended last and there's a PK clash
            combined_df.drop_duplicates(subset=pk_cols, keep='last', inplace=True)
            
            if not combined_df.empty:
                try:
                    combined_df.to_sql('match_results', conn, if_exists='append', index=False)
                    logger.info(f"Inserted de-duplicated match results for {league_name} ({len(combined_df)} rows).")
                except Exception as e:
                    logger.exception(f"Error inserting de-duplicated match results for {league_name}")
            else:
                logger.info(f"No valid, de-duplicated match results to insert for {league_name}.")
        else:
            logger.info(f"No match result files found or loaded for {league_name}.")

        # Process Other CSV files
        for csv_file in other_csv_files:
            filename = os.path.basename(csv_file)
            logger.debug(f"Processing other file: {filename}")
            if "_league_table.csv" in filename: process_league_table(conn, csv_file, league_name)
            elif "_head_to_head_stats.csv" in filename: process_head_to_head_stats(conn, csv_file, league_name)
            elif "_league_stats.csv" in filename: process_league_summary_stats(conn, csv_file, league_name)
            elif "_team_stats.csv" in filename: process_team_detailed_stats(conn, csv_file, league_name)
            
    conn.close()
    logger.info("Data migration process completed.")

if __name__ == '__main__':
    main()
