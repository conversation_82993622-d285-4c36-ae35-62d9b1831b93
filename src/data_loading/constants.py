"""
Constants used throughout the data loading package.
"""

import os

# File path constants
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data")
RAW_DATA_DIR = os.path.join(DATA_DIR, "raw")

# Required columns for data validation
RESULTS_REQUIRED_COLUMNS = [
    "Date",
    "Home Team",
    "Score",
    "Away Team"
]

# Non-numeric columns by data type
NON_NUMERIC_COLUMNS = {
    "team_stats": ["Team"],
    "h2h_stats": ["Matchup", "recent_results"],
    "league_stats": ["Stat"],
    "league_table": ["Team"]
}

# File names template for each data type
DATA_FILES = {
    "results": "{league_name}_results.csv",
    "team_stats": "{league_name}_team_stats.csv",
    "league_stats": "{league_name}_league_stats.csv",
    "h2h_stats": "{league_name}_head_to_head_stats.csv",
    "league_table": "{league_name}_league_table.csv"
}

# Data type descriptions for logging
DATA_TYPE_DESCRIPTIONS = {
    "results": "match results",
    "team_stats": "team statistics",
    "league_stats": "league statistics",
    "h2h_stats": "head-to-head statistics",
    "league_table": "league table"
}
