"""
Core data loading functionality.
"""

import os
import logging
import pandas as pd
from typing import Dict, <PERSON><PERSON>, Tu<PERSON>, Any

from .constants import DATA_FILES
from .utils import get_file_path
from .validation import validate_and_convert_data, validate_league_config

logger = logging.getLogger(__name__)

def load_data(
    league_name: str,
    league_config: Dict[str, Any]
) -> Tuple[
    Optional[Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]],
    Dict[str, Any]
]:
    """
    Load all necessary CSV files for the prediction model.

    Args:
        league_name: Name of the league
        league_config: Configuration dictionary for the league

    Returns:
        Tuple containing:
        - Optional tuple of DataFrames (results, team stats, league stats, h2h stats, league table)
        - League configuration dictionary

    Example:
        >>> league_name = "ENGLAND_PREMIER_LEAGUE"
        >>> league_config = {"TEAM_NAME_MAPPING": {}}
        >>> data, config = load_data(league_name, league_config)
        >>> if data:
        ...     results, team_stats, league_stats, h2h_stats, league_table = data
        ...     print(f"Loaded {len(results)} matches")
        Loaded 380 matches
    """
    try:
        # Validate league configuration
        if not validate_league_config(league_config):
            logger.error(f"Invalid league configuration for {league_name}")
            return None, league_config

        logger.info(f"Loading data for league: {league_name}")
        data = _load_all_data_files(league_name)

        if not data:
            return None, league_config

        # Convert dictionary of DataFrames to tuple in specific order
        data_tuple = _convert_data_to_tuple(data)
        if not data_tuple:
            return None, league_config

        logger.info(f"Successfully loaded all data files for league: {league_name}")
        return data_tuple, league_config

    except Exception as e:
        logger.error(f"Error loading data for league {league_name}: {str(e)}")
        return None, league_config

def _load_all_data_files(league_name: str) -> Optional[Dict[str, pd.DataFrame]]:
    """
    Load all data files for a league.

    Args:
        league_name: Name of the league

    Returns:
        Dictionary of DataFrames, or None if loading fails
    """
    data: Dict[str, pd.DataFrame] = {}

    for data_type in DATA_FILES.keys():
        file_path = get_file_path(league_name, data_type)
        
        if not os.path.exists(file_path):
            logger.warning(f"Missing file: {file_path}")
            continue

        df = _load_and_validate_file(file_path, data_type)
        if df is not None:
            data[data_type] = df
            logger.info(f"Loaded {data_type} dataframe with shape: {df.shape}")
        else:
            logger.error(f"Data validation failed for {data_type}")
            return None

    if len(data) < len(DATA_FILES):
        missing_files = set(DATA_FILES.keys()) - set(data.keys())
        logger.warning(f"Missing data files for {league_name}: {', '.join(missing_files)}")
        return None

    return data

def _load_and_validate_file(
    file_path: str,
    data_type: str
) -> Optional[pd.DataFrame]:
    """
    Load and validate a single data file.

    Args:
        file_path: Path to the data file
        data_type: Type of data being loaded

    Returns:
        Validated DataFrame or None if loading/validation fails
    """
    try:
        # Prevent pandas from auto-detecting index columns
        df = pd.read_csv(file_path, index_col=None)
        if df.empty:
            logger.warning(f"Empty file: {file_path}")
            return None

        return validate_and_convert_data(df, data_type)

    except Exception as e:
        logger.error(f"Error loading file {file_path}: {str(e)}")
        return None

def _convert_data_to_tuple(
    data: Dict[str, pd.DataFrame]
) -> Optional[Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]]:
    """
    Convert dictionary of DataFrames to tuple in specific order.

    Args:
        data: Dictionary of DataFrames

    Returns:
        Tuple of DataFrames in specific order, or None if conversion fails
    """
    try:
        return (
            data["results"],
            data["team_stats"],
            data["league_stats"],
            data["h2h_stats"],
            data["league_table"]
        )
    except KeyError as e:
        logger.error(f"Missing required data type: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error converting data to tuple: {str(e)}")
        return None


