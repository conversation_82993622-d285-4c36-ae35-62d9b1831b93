import os
import json
import time
import random
import logging
import argparse
import yaml
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional
from dataclasses import dataclass
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from requests.exceptions import ChunkedEncodingError
from urllib3.exceptions import ProtocolError
import requests
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("url_extractor.log"), logging.StreamHandler()],
)

# Configuration
MAX_SHOW_MORE_CLICKS = 50
SCROLL_PAUSE_TIME = 2
BASE_WAIT_TIME = 10
VISIBLE_BROWSER = False


@dataclass
class League:
    name: str
    stats_url: str
    table_url: str
    fixtures_url: str


class UserAgentManager:
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Safari/537.36",
    ]

    @classmethod
    def get_random(cls) -> str:
        return random.choice(cls.USER_AGENTS)


class ProgressTracker:
    def __init__(self):
        self.progress_dir = Path("src/config/progress")
        self.progress_dir.mkdir(parents=True, exist_ok=True)
        self.leagues_progress_file = self.progress_dir / "leagues_progress.json"
        self.matches_progress_file = self.progress_dir / "matches_progress.json"
        self.load_progress()

    def load_progress(self):
        try:
            self.processed_leagues = set(
                json.loads(self.leagues_progress_file.read_text())
            )
        except FileNotFoundError:
            self.processed_leagues = set()

        try:
            self.processed_matches = json.loads(self.matches_progress_file.read_text())
        except FileNotFoundError:
            self.processed_matches = {}

    def save_league_progress(self, league_name: str):
        self.processed_leagues.add(league_name)
        self.leagues_progress_file.write_text(json.dumps(list(self.processed_leagues)))

    def save_match_progress(self, league_name: str, match_urls: List[str]):
        if league_name not in self.processed_matches:
            self.processed_matches[league_name] = []
        self.processed_matches[league_name].extend(match_urls)
        self.processed_matches[league_name] = list(
            set(self.processed_matches[league_name])
        )
        self.matches_progress_file.write_text(json.dumps(self.processed_matches))

    def get_new_matches(self, league_name: str, match_urls: List[str]) -> List[str]:
        existing_matches = set(self.processed_matches.get(league_name, []))
        return [url for url in match_urls if url not in existing_matches]


class WebDriverManager:
    @staticmethod
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ChunkedEncodingError, ProtocolError)),
    )
    def setup_driver() -> webdriver.Chrome:
        try:
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument(f"user-agent={UserAgentManager.get_random()}")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option(
                "excludeSwitches", ["enable-automation"]
            )
            chrome_options.add_experimental_option("useAutomationExtension", False)

            if not VISIBLE_BROWSER:
                chrome_options.add_argument("--headless=new")

            cache_dir = Path.home() / ".wdm" / "cache"
            cache_dir.mkdir(parents=True, exist_ok=True)

            driver_path = ChromeDriverManager().install()

            driver = webdriver.Chrome(
                service=Service(driver_path), options=chrome_options
            )

            if VISIBLE_BROWSER:
                driver.set_window_size(1366, 768)
            driver.get("about:blank")

            return driver

        except Exception as e:
            logging.error(f"Failed to setup WebDriver: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def cleanup_driver(driver: Optional[webdriver.Chrome]):
        try:
            if driver:
                driver.delete_all_cookies()
                driver.execute_script("window.localStorage.clear();")
                driver.execute_script("window.sessionStorage.clear();")
                driver.quit()
        except Exception as e:
            logging.error(f"Error cleaning up driver: {str(e)}")


class URLExtractor:
    def __init__(self):
        self.driver = None
        self.progress_tracker = ProgressTracker()

    def scroll_page(self):
        last_height = self.driver.execute_script("return document.body.scrollHeight")
        while True:
            self.driver.execute_script(
                "window.scrollTo(0, document.body.scrollHeight);"
            )
            time.sleep(random.uniform(1, SCROLL_PAUSE_TIME))
            new_height = self.driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height

    def extract_team_urls(self, url: str) -> List[str]:
        self.driver.get(url)
        time.sleep(random.uniform(2, 4))
        return self.driver.execute_script(
            """
            // Look for both new and old format team links
            var newFormatLinks = document.querySelectorAll('a[href*="teamstats.asp"][href*="stats="]');
            var oldFormatLinks = document.querySelectorAll('a[href*="team.asp"][href*="stats="]');

            // Combine both sets of links
            var allTeamLinks = [...Array.from(newFormatLinks), ...Array.from(oldFormatLinks)];

            // Return all found links
            return allTeamLinks.map(link => link.href);
            """
        )

    def extract_all_h2h_links(self, url: str) -> List[str]:
        self.driver.get(url)
        time.sleep(random.uniform(3, 5))
        logging.info(f"Loaded URL: {url}")

        show_more_clicks = 0
        consecutive_failures = 0
        max_consecutive_failures = 2  # Stop after 2 consecutive failures to find the button
        
        while show_more_clicks < MAX_SHOW_MORE_CLICKS and consecutive_failures < max_consecutive_failures:
            try:
                # First, quickly check if the button exists without waiting long
                show_more_buttons = self.driver.find_elements(By.ID, "matches-list-next-matchday")
                
                if not show_more_buttons:
                    consecutive_failures += 1
                    logging.info(f"Show More button not found (attempt {consecutive_failures}/{max_consecutive_failures})")
                    if consecutive_failures >= max_consecutive_failures:
                        logging.info("Show More button no longer exists - stopping clicks")
                        break
                    time.sleep(1)  # Brief wait before trying again
                    continue
                
                # Button exists, now wait for it to be clickable with shorter timeout
                show_more_button = WebDriverWait(self.driver, 3).until(
                    EC.element_to_be_clickable((By.ID, "matches-list-next-matchday"))
                )
                
                self.driver.execute_script("arguments[0].click();", show_more_button)
                show_more_clicks += 1
                consecutive_failures = 0  # Reset failure counter on successful click
                logging.info(f"Clicked 'Show More' button {show_more_clicks} times")
                time.sleep(random.uniform(1, 2))
                
            except TimeoutException:
                consecutive_failures += 1
                logging.info(f"Show More button not clickable (attempt {consecutive_failures}/{max_consecutive_failures})")
                if consecutive_failures >= max_consecutive_failures:
                    logging.info("Show More button no longer clickable - stopping clicks")
                    break
                time.sleep(1)  # Brief wait before trying again
            except Exception as e:
                consecutive_failures += 1
                logging.warning(f"Error clicking Show More button: {e} (attempt {consecutive_failures}/{max_consecutive_failures})")
                if consecutive_failures >= max_consecutive_failures:
                    logging.info("Too many errors with Show More button - stopping clicks")
                    break
                time.sleep(1)  # Brief wait before trying again

        self.scroll_page()

        try:
            logging.info("Waiting for H2H links to load...")
            WebDriverWait(self.driver, 30).until(
                EC.presence_of_element_located(
                    (By.CSS_SELECTOR, "a[href*='h2h-stats']")
                )
            )

            match_elements = self.driver.find_elements(By.CSS_SELECTOR, "ul.match")
            all_urls = []

            for match in match_elements:
                try:
                    h2h_link = match.find_element(
                        By.CSS_SELECTOR, "a[href*='h2h-stats']"
                    )
                    url = h2h_link.get_attribute("href")
                    all_urls.append(url)
                    logging.info(f"Added H2H URL: {url}")

                    ActionChains(self.driver).move_to_element(h2h_link).perform()
                    time.sleep(random.uniform(0.1, 0.3))
                except Exception as e:
                    logging.error(f"Error processing match element: {e}")
                    continue

            logging.info(f"Total H2H URLs extracted: {len(all_urls)}")
            return all_urls

        except Exception as e:
            logging.error(f"Error extracting H2H links: {e}")
            return []

    def process_league(self, league: League, force: bool = False, team_urls_only: bool = False, h2h_urls_only: bool = False) -> Optional[str]:
        try:
            if not force and league.name in self.progress_tracker.processed_leagues:
                logging.info(f"Skipping already processed league: {league.name}")
                return None

            logging.info(f"Processing league: {league.name}")

            # Load existing config if it exists (for partial updates)
            config_file = Path("src/config/json_database") / f"{league.name}_config.json"
            if config_file.exists():
                try:
                    league_data = json.loads(config_file.read_text())
                    logging.info(f"Loaded existing config for {league.name}")
                except Exception as e:
                    logging.warning(f"Could not load existing config for {league.name}: {e}. Creating new config.")
                    league_data = {
                        "LEAGUE_STATS_URL": league.stats_url,
                        "LEAGUE_TABLE_URL": league.table_url,
                        "TEAM_URLS": {},
                        "HEAD_TO_HEAD_URLS": {},
                    }
            else:
                league_data = {
                    "LEAGUE_STATS_URL": league.stats_url,
                    "LEAGUE_TABLE_URL": league.table_url,
                    "TEAM_URLS": {},
                    "HEAD_TO_HEAD_URLS": {},
                }

            # Extract team URLs (unless h2h_urls_only is specified)
            if not h2h_urls_only:
                logging.info(f"Extracting team URLs for {league.name}")
                team_urls = self.extract_team_urls(league.stats_url)
                league_data["TEAM_URLS"] = {}  # Clear existing team URLs
                for url in team_urls:
                    # Extract team name from URL (handle both old and new formats)
                    if "stats=" in url:
                        stats_part = url.split("stats=")[-1]

                        # Handle new format: teamstats.asp?league=chile2&stats=u5221-antofagasta
                        if "teamstats.asp" in url:
                            # Remove the unique ID part (e.g., "u5221-") and get the team name
                            if "-" in stats_part:
                                team_name = "-".join(stats_part.split("-")[1:]).replace("-", " ").title()
                            else:
                                team_name = stats_part.replace("-", " ").title()

                        # Handle old format: team.asp?league=belgium6&stats=1-waasland-b.-u21
                        elif "team.asp" in url:
                            # Remove the number prefix (e.g., "1-") and get the team name
                            if "-" in stats_part:
                                team_name = "-".join(stats_part.split("-")[1:]).replace("-", " ").title()
                            else:
                                team_name = stats_part.replace("-", " ").title()

                        else:
                            # Fallback for any other format
                            team_name = stats_part.replace("-", " ").title()

                        league_data["TEAM_URLS"][team_name] = url
                logging.info(f"Extracted {len(team_urls)} team URLs")

            # Extract H2H URLs (unless team_urls_only is specified)
            if not team_urls_only:
                logging.info(f"Extracting H2H links for {league.name}")
                all_urls = self.extract_all_h2h_links(league.fixtures_url)
                new_urls = self.progress_tracker.get_new_matches(league.name, all_urls)

                for url in new_urls:
                    teams = url.split("/")[-1].split("-vs-")
                    home_team = teams[0].replace("-", " ").title()
                    away_team = teams[1].split("-h2h-stats")[0].replace("-", " ").title()
                    match_key = f"{home_team} vs {away_team}"
                    league_data["HEAD_TO_HEAD_URLS"][match_key] = url

                # Save progress
                self.progress_tracker.save_match_progress(league.name, all_urls)
                logging.info(f"Extracted {len(new_urls)} new H2H URLs")

            # Save league data
            output_dir = Path("src/config/json_database")
            output_dir.mkdir(parents=True, exist_ok=True)
            output_file = output_dir / f"{league.name}_config.json"
            output_file.write_text(json.dumps(league_data, indent=2))

            logging.info(f"Configuration for {league.name} has been saved")

            # Only mark as processed if we did a full extraction (not partial)
            if not team_urls_only and not h2h_urls_only:
                self.progress_tracker.save_league_progress(league.name)

            return league.name

        except Exception as e:
            logging.error(f"Error processing {league.name}: {e}", exc_info=True)
            return None

    def run(self, target_leagues=None, force=False, team_urls_only=False, h2h_urls_only=False):
        logging.info("Starting the scraping process")

        try:
            leagues_file = Path("src/config/leagues.json")
            leagues_data = json.loads(leagues_file.read_text())
            leagues = [League(**league) for league in leagues_data["leagues"]]

            # Filter by target leagues if specified
            if target_leagues:
                leagues = [league for league in leagues if league.name in target_leagues]
                if not leagues:
                    logging.error(f"No leagues found matching: {target_leagues}")
                    return

            # Apply processed league filter unless force is used
            if force:
                leagues_to_process = leagues
                logging.info("Force mode: Processing all target leagues regardless of previous processing")
            else:
                leagues_to_process = [
                    league
                    for league in leagues
                    if league.name not in self.progress_tracker.processed_leagues
                ]

            if not leagues_to_process:
                logging.info("All target leagues have been processed")
                return

            self.driver = WebDriverManager.setup_driver()

            for league in leagues_to_process:
                try:
                    self.process_league(league, force=force, team_urls_only=team_urls_only, h2h_urls_only=h2h_urls_only)
                except Exception as exc:
                    logging.error(f"Error processing {league.name}: {exc}")
                time.sleep(random.uniform(5, 10))

        except Exception as e:
            logging.error(f"Fatal error in URL extraction process: {e}", exc_info=True)
            raise

        finally:
            WebDriverManager.cleanup_driver(self.driver)
            logging.info("URL extraction process completed")


def load_batch_config(config_file: str = "url_fix_batches.yaml") -> Dict:
    """Load batch configuration from YAML file."""
    try:
        with open(config_file, 'r') as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        logging.error(f"Batch config file not found: {config_file}")
        return None
    except Exception as e:
        logging.error(f"Failed to load batch config: {e}")
        return None


def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Extract URLs for betting leagues")
    parser.add_argument("--league", type=str, help="Extract URLs for a specific league only")
    parser.add_argument("--leagues", type=str, help="Extract URLs for specific leagues (comma-separated)")
    parser.add_argument("--batch", type=str, help="Process a batch of leagues from YAML config file")
    parser.add_argument("--batch-config", type=str, default="url_fix_batches.yaml", help="Path to batch config file")
    parser.add_argument("--force", action="store_true", help="Force re-extraction even if league was already processed")
    parser.add_argument("--team-urls-only", action="store_true", help="Extract only team URLs (skip H2H URLs)")
    parser.add_argument("--h2h-urls-only", action="store_true", help="Extract only H2H URLs (skip team URLs)")
    args = parser.parse_args()

    total_start_time = time.time()
    processed_leagues = 0
    total_leagues = 0

    try:
        extractor = URLExtractor()

        leagues_file = Path("src/config/leagues.json")
        leagues_data = json.loads(leagues_file.read_text())

        # Filter leagues based on command line arguments
        target_leagues = None
        if args.league:
            target_leagues = [args.league]
        elif args.leagues:
            target_leagues = [league.strip() for league in args.leagues.split(",")]
        elif args.batch:
            # Load batch configuration
            batch_config = load_batch_config(args.batch_config)
            if not batch_config:
                logging.error("Failed to load batch configuration")
                return

            if args.batch not in batch_config['batches']:
                available_batches = list(batch_config['batches'].keys())
                logging.error(f"Batch '{args.batch}' not found. Available batches: {available_batches}")
                return

            batch = batch_config['batches'][args.batch]
            target_leagues = batch['leagues']
            logging.info(f"🔧 Processing batch '{args.batch}': {batch['description']}")
            logging.info(f"📋 Batch contains {len(target_leagues)} leagues: {target_leagues}")

        if target_leagues:
            # Filter to only target leagues
            filtered_leagues = [
                league for league in leagues_data["leagues"]
                if league["name"] in target_leagues
            ]
            if not filtered_leagues:
                logging.error(f"No leagues found matching: {target_leagues}")
                return
            leagues_data["leagues"] = filtered_leagues
            logging.info(f"Targeting specific leagues: {target_leagues}")

        # Apply processed league filter unless --force is used
        if args.force:
            leagues_to_count = leagues_data["leagues"]
            logging.info("Force mode: Re-extracting even previously processed leagues")
        else:
            leagues_to_count = [
                league
                for league in leagues_data["leagues"]
                if league["name"] not in extractor.progress_tracker.processed_leagues
            ]

        total_leagues = len(leagues_to_count)
        logging.info(f"Starting extraction for {total_leagues} leagues")

        def log_timing(elapsed_time: float, prefix: str = ""):
            hours, remainder = divmod(elapsed_time, 3600)
            minutes, seconds = divmod(remainder, 60)
            return f"{prefix}{int(hours)}h {int(minutes)}m {seconds:.2f}s"

        def update_progress():
            current_time = time.time()
            elapsed = current_time - total_start_time
            if processed_leagues > 0:
                avg_time_per_league = elapsed / processed_leagues
                estimated_remaining = avg_time_per_league * (
                    total_leagues - processed_leagues
                )
                logging.info(f"Progress: {processed_leagues}/{total_leagues} leagues")
                logging.info(f"Elapsed time: {log_timing(elapsed)}")
                logging.info(
                    f"Estimated remaining time: {log_timing(estimated_remaining)}"
                )
                logging.info(
                    f"Average time per league: {log_timing(avg_time_per_league)}"
                )

        original_process_league = extractor.process_league

        def wrapped_process_league(league, force=False, team_urls_only=False, h2h_urls_only=False):
            nonlocal processed_leagues
            league_start = time.time()

            result = original_process_league(league, force=force, team_urls_only=team_urls_only, h2h_urls_only=h2h_urls_only)

            if result:
                processed_leagues += 1
                league_time = time.time() - league_start
                logging.info(f"Completed {league.name} in {log_timing(league_time)}")
                update_progress()

            return result

        extractor.process_league = wrapped_process_league
        extractor.run(target_leagues=target_leagues, force=args.force, team_urls_only=args.team_urls_only, h2h_urls_only=args.h2h_urls_only)

        final_time = time.time() - total_start_time
        logging.info(f"\nExtraction completed!")
        logging.info(f"Total leagues processed: {processed_leagues}")
        logging.info(f"Total execution time: {log_timing(final_time)}")
        if processed_leagues > 0:
            avg_time = final_time / processed_leagues
            logging.info(f"Average time per league: {log_timing(avg_time)}")

    except Exception as e:
        elapsed = time.time() - total_start_time
        hours, remainder = divmod(elapsed, 3600)
        minutes, seconds = divmod(remainder, 60)
        elapsed_str = f"{int(hours)}h {int(minutes)}m {seconds:.2f}s"
        logging.error(
            f"Script failed after {elapsed_str}: {str(e)}", exc_info=True
        )
        raise


if __name__ == "__main__":
    main()
