"""
Team strength calculation functionality.
Handles attack, defense, and overall team strength calculations.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Optional
from .constants import (
    HOME_BOOST,
    AWAY_REDUCTION,
    SCORING_WEIGHT,
    DEFENSIVE_WEIGHT,
    WIN_RATE_WEIGHT,
    FORM_WEIGHT,
    HOME_VENUE_BOOST,
    AWAY_VENUE_REDUCTION,
    BASE_WEIGHT,
    FORM_ADJUSTMENT_WEIGHT
)

logger = logging.getLogger(__name__)

def calculate_team_strength(
    team_stats: pd.Series,
    league_stats_dict: Dict[str, float],
    is_home: bool = True
) -> float:
    """
    Calculate overall team strength based on various metrics.

    Args:
        team_stats: Statistics for the team
        league_stats_dict: Dictionary of league-wide statistics
        is_home: Whether the team is playing at home

    Returns:
        Calculated team strength (0-1 scale)
    """
    try:
        # Get relevant statistics - handle NaN values
        if is_home:
            goals_scored = team_stats.get("goals_scored_per_match_home", 1.0)
            goals_conceded = team_stats.get("goals_conceded_per_match_home", 1.0)
            wins = team_stats.get("total_home_wins", 0)
            matches = team_stats.get("total_home_played", 1)
        else:
            goals_scored = team_stats.get("goals_scored_per_match_away", 1.0)
            goals_conceded = team_stats.get("goals_conceded_per_match_away", 1.0)
            wins = team_stats.get("total_away_wins", 0)
            matches = team_stats.get("total_away_played", 1)

        # Handle NaN values
        if pd.isna(goals_scored):
            goals_scored = 1.0
        if pd.isna(goals_conceded):
            goals_conceded = 1.0
        if pd.isna(wins):
            wins = 0
        if pd.isna(matches):
            matches = 1

        # Calculate various strength components using league averages - handle None values
        league_avg_goals_raw = league_stats_dict.get("avg_goals_per_match", 2.5)
        league_avg_goals = float(league_avg_goals_raw if league_avg_goals_raw is not None else 2.5)
        scoring_strength = goals_scored / league_avg_goals
        defensive_strength = 1 - (goals_conceded / league_avg_goals)
        win_rate = wins / matches if matches > 0 else 0.0

        # Handle missing ppg_last_8 by calculating from available data
        ppg_last_8 = team_stats.get("ppg_last_8")
        if pd.isna(ppg_last_8):
            # Fallback: calculate from total points and matches
            total_points = wins * 3 + team_stats.get("total_draws", 0)
            total_matches = team_stats.get("total_played", matches)
            ppg_last_8 = total_points / total_matches if total_matches > 0 else 1.5

        form = ppg_last_8 / 3.0  # Normalize by max points possible

        # Combine components with weights
        strength = (
            SCORING_WEIGHT * scoring_strength
            + DEFENSIVE_WEIGHT * defensive_strength
            + WIN_RATE_WEIGHT * win_rate
            + FORM_WEIGHT * form
        )

        # Apply venue adjustment
        strength *= HOME_BOOST if is_home else AWAY_REDUCTION

        # Ensure result is between 0 and 1
        return np.clip(strength, 0, 1)

    except Exception as e:
        logger.error(f"Error calculating team strength: {str(e)}")
        return 0.5  # Return neutral strength on error

def calculate_attack_strength(
    team_stats: pd.Series,
    league_stats_dict: Dict[str, float],
    is_home: bool = True
) -> float:
    """
    Calculate attack strength with enhanced consideration of form and opposition quality.

    Args:
        team_stats: Statistics for the team
        league_stats_dict: Dictionary of league-wide statistics
        is_home: Whether the team is playing at home

    Returns:
        Calculated attack strength
    """
    try:
        # Get base scoring rate - handle NaN values
        if is_home:
            goals_scored = team_stats.get("goals_scored_per_match_home", 1.0)
        else:
            goals_scored = team_stats.get("goals_scored_per_match_away", 1.0)

        # Handle NaN values
        if pd.isna(goals_scored):
            goals_scored = 1.0

        # Handle missing ppg_last_8 by calculating from available data
        ppg_last_8 = team_stats.get("ppg_last_8")
        if pd.isna(ppg_last_8):
            # Fallback: calculate from total points and matches
            total_wins = team_stats.get("total_wins", 0)
            total_draws = team_stats.get("total_draws", 0)
            total_points = total_wins * 3 + total_draws
            total_matches = team_stats.get("total_played", 1)
            ppg_last_8 = total_points / total_matches if total_matches > 0 else 1.5

        form_factor = ppg_last_8 / 2.0

        # Get league averages - handle None values
        league_avg_raw = league_stats_dict.get("avg_goals_per_match", 2.5)
        league_home_avg_raw = league_stats_dict.get("home_goals_per_match", 1.5)
        league_away_avg_raw = league_stats_dict.get("away_goals_per_match", 1.0)

        league_avg = float(league_avg_raw if league_avg_raw is not None else 2.5)
        league_home_avg = float(league_home_avg_raw if league_home_avg_raw is not None else 1.5)
        league_away_avg = float(league_away_avg_raw if league_away_avg_raw is not None else 1.0)

        # Calculate base attack strength
        if is_home:
            base_strength = goals_scored / league_home_avg if league_home_avg > 0 else 1.0
        else:
            base_strength = goals_scored / league_away_avg if league_away_avg > 0 else 1.0

        # Apply form adjustment with exponential weighting
        form_weight = np.exp(form_factor - 1)  # Exponential scaling of form
        form_adjusted = base_strength * (BASE_WEIGHT + FORM_ADJUSTMENT_WEIGHT * form_weight)

        # Apply venue adjustment with dynamic factor
        venue_factor = HOME_VENUE_BOOST if is_home else AWAY_VENUE_REDUCTION
        final_strength = form_adjusted * venue_factor

        # Normalize to reasonable range with smoother bounds
        final_strength = 0.5 + 1.5 / (1 + np.exp(-2 * (final_strength - 1)))

        return final_strength

    except Exception as e:
        logger.error(f"Error calculating attack strength: {str(e)}")
        return 1.0  # Return neutral strength on error

def calculate_defense_strength(
    team_stats: pd.Series,
    league_stats_dict: Dict[str, float],
    is_home: bool = True
) -> float:
    """
    Calculate defense strength with enhanced consideration of clean sheets and goals conceded.

    Args:
        team_stats: Statistics for the team
        league_stats_dict: Dictionary of league-wide statistics
        is_home: Whether the team is playing at home

    Returns:
        Calculated defense strength
    """
    try:
        if is_home:
            goals_conceded = team_stats.get("goals_conceded_per_match_home", 1.0)
            clean_sheets = team_stats.get("clean_sheets_home", 0)
            matches_played = team_stats.get("total_home_played", 1)
        else:
            goals_conceded = team_stats.get("goals_conceded_per_match_away", 1.0)
            clean_sheets = team_stats.get("clean_sheets_away", 0)
            matches_played = team_stats.get("total_away_played", 1)

        # Handle NaN values
        if pd.isna(goals_conceded):
            goals_conceded = 1.0
        if pd.isna(clean_sheets):
            clean_sheets = 0
        if pd.isna(matches_played):
            matches_played = 1

        league_avg_raw = league_stats_dict.get("avg_goals_per_match", 2.5)
        league_avg = float(league_avg_raw if league_avg_raw is not None else 2.5)

        # Calculate clean sheet ratio
        clean_sheet_ratio = clean_sheets / matches_played if matches_played > 0 else 0

        # Calculate base defense strength
        base_strength = goals_conceded / league_avg if league_avg > 0 else 1.0

        # Invert and adjust with clean sheet bonus
        defense_strength = (2 - base_strength) * (1 + 0.2 * clean_sheet_ratio)

        # Apply venue adjustment with dynamic factor
        venue_factor = HOME_VENUE_BOOST if is_home else AWAY_VENUE_REDUCTION
        defense_strength *= venue_factor

        # Normalize to reasonable range with smoother bounds
        defense_strength = 0.5 + 1.5 / (1 + np.exp(-2 * (defense_strength - 1)))

        return defense_strength

    except Exception as e:
        logger.error(f"Error calculating defense strength: {str(e)}")
        return 1.0  # Return neutral strength on error

def calculate_form(
    team_stats: pd.Series,
    recent_weight: float = 0.7
) -> float:
    """
    Calculate team form with exponential decay for recent results.

    Args:
        team_stats: Statistics for the team
        recent_weight: Weight given to recent performance

    Returns:
        Calculated form value (0-1 scale)
    """
    try:
        # Handle missing ppg_last_8 and points_per_game
        ppg_last_8 = team_stats.get("ppg_last_8")
        overall_ppg = team_stats.get("points_per_game")

        # Calculate fallback values if missing - handle NaN values
        if pd.isna(ppg_last_8) or pd.isna(overall_ppg):
            total_wins = team_stats.get("total_wins", 0)
            total_draws = team_stats.get("total_draws", 0)
            total_matches = team_stats.get("total_played", 1)

            # Handle NaN values
            if pd.isna(total_wins):
                total_wins = 0
            if pd.isna(total_draws):
                total_draws = 0
            if pd.isna(total_matches):
                total_matches = 1

            total_points = total_wins * 3 + total_draws
            calculated_ppg = total_points / total_matches if total_matches > 0 else 1.5

            if pd.isna(ppg_last_8):
                ppg_last_8 = calculated_ppg
            if pd.isna(overall_ppg):
                overall_ppg = calculated_ppg

        # Calculate recent goals scored and conceded
        recent_goals_scored = team_stats.get("avg_goals_scored_last_8")
        recent_goals_conceded = team_stats.get("avg_goals_conceded_last_8")

        # Use fallback values if missing - handle NaN values
        if pd.isna(recent_goals_scored):
            fallback_scored = team_stats.get("goals_scored_per_match_all", 1.0)
            if pd.isna(fallback_scored):
                fallback_scored = 1.0
            recent_goals_scored = fallback_scored
        if pd.isna(recent_goals_conceded):
            fallback_conceded = team_stats.get("goals_conceded_per_match_all", 1.0)
            if pd.isna(fallback_conceded):
                fallback_conceded = 1.0
            recent_goals_conceded = fallback_conceded

        # Calculate goal difference impact
        goal_diff_factor = np.tanh((recent_goals_scored - recent_goals_conceded) / 2)

        # Weight recent form more heavily with goal difference consideration
        form = (recent_weight * ppg_last_8 + (1 - recent_weight) * overall_ppg) * (
            1 + 0.2 * goal_diff_factor
        )

        # Normalize to [0, 1] range
        form = np.clip(form / 3.0, 0, 1)

        return form

    except Exception as e:
        logger.error(f"Error calculating form: {str(e)}")
        return 0.5  # Return neutral form on error
