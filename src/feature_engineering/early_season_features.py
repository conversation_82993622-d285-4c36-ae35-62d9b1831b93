"""
Enhanced early season features that capture team quality differences
when current season data is insufficient.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple

logger = logging.getLogger(__name__)

def create_early_season_team_quality_features(
    home_team: str,
    away_team: str,
    league_table: pd.DataFrame,
    h2h_data: Optional[pd.Series] = None,
    season_reliability: float = 0.0
) -> Dict[str, float]:
    """
    Create team quality features for early season when current data is limited.
    
    Args:
        home_team: Home team name
        away_team: Away team name
        league_table: Current league table (may reflect previous season end)
        h2h_data: Head-to-head data
        season_reliability: How reliable current season data is (0-1)
        
    Returns:
        Dictionary of early season team quality features
    """
    features = {}
    
    # Team quality indicators based on typical Premier League hierarchy
    team_quality_tiers = {
        # Tier 1: Top 6 traditional big clubs
        'Arsenal': 1, 'Manchester City': 1, 'Manchester Utd': 1, 
        'Liverpool': 1, 'Chelsea': 1, 'Tottenham': 1,
        
        # Tier 2: Established Premier League clubs
        'Newcastle Utd': 2, 'Aston Villa': 2, 'Brighton': 2, 
        'West Ham Utd': 2, 'Fulham': 2, 'Crystal Palace': 2,
        'Brentford': 2, 'Wolverhampton': 2, 'Everton': 2,
        
        # Tier 3: Lower table / recently promoted
        'Nottm Forest': 3, 'Bournemouth': 3, 'Leeds Utd': 3,
        'Burnley': 3, 'Sunderland': 3
    }
    
    home_tier = team_quality_tiers.get(home_team, 3)  # Default to tier 3 if unknown
    away_tier = team_quality_tiers.get(away_team, 3)
    
    # Quality differential (lower tier number = higher quality)
    quality_diff = away_tier - home_tier  # Positive means home team is better
    
    features.update({
        'home_team_quality_tier': home_tier,
        'away_team_quality_tier': away_tier,
        'team_quality_differential': quality_diff,
        'quality_advantage_home': max(0, quality_diff),  # Home advantage from quality
        'quality_advantage_away': max(0, -quality_diff),  # Away advantage from quality
    })
    
    # Enhanced H2H dominance features
    if h2h_data is not None and not h2h_data.empty:
        home_win_pct = h2h_data.get('home_win_percentage', 50) / 100
        away_win_pct = h2h_data.get('away_win_percentage', 30) / 100
        total_matches = h2h_data.get('total_matches', 0)
        
        # H2H dominance strength (how one-sided the matchup is)
        h2h_dominance = abs(home_win_pct - away_win_pct)
        h2h_home_dominance = home_win_pct - away_win_pct  # Positive = home dominates
        
        # Sample size confidence
        h2h_confidence = min(1.0, total_matches / 10)
        
        # Strong H2H signals for early season
        features.update({
            'h2h_dominance_strength': h2h_dominance,
            'h2h_home_dominance_score': h2h_home_dominance,
            'h2h_sample_confidence': h2h_confidence,
            'h2h_weighted_home_advantage': h2h_home_dominance * h2h_confidence,
            'h2h_extreme_dominance': 1.0 if h2h_dominance > 0.6 else 0.0,  # Very one-sided
        })
        
        # Goal-based H2H features
        if not pd.isna(h2h_data.get('home_goals')) and not pd.isna(h2h_data.get('away_goals')):
            home_goals = h2h_data.get('home_goals', 0)
            away_goals = h2h_data.get('away_goals', 0)
            total_goals = home_goals + away_goals
            
            if total_goals > 0:
                h2h_goal_dominance = (home_goals - away_goals) / total_goals
                features.update({
                    'h2h_goal_dominance': h2h_goal_dominance,
                    'h2h_goals_per_match': total_goals / max(1, total_matches),
                    'h2h_home_scoring_rate': home_goals / max(1, total_matches),
                    'h2h_away_scoring_rate': away_goals / max(1, total_matches),
                })
    else:
        # No H2H data - use neutral values
        features.update({
            'h2h_dominance_strength': 0.0,
            'h2h_home_dominance_score': 0.0,
            'h2h_sample_confidence': 0.0,
            'h2h_weighted_home_advantage': 0.0,
            'h2h_extreme_dominance': 0.0,
            'h2h_goal_dominance': 0.0,
            'h2h_goals_per_match': 2.5,  # League average
            'h2h_home_scoring_rate': 1.5,
            'h2h_away_scoring_rate': 1.0,
        })
    
    # Early season uncertainty indicators
    features.update({
        'early_season_uncertainty': 1.0 - season_reliability,
        'data_scarcity_multiplier': 2.0 - season_reliability,  # Amplify other signals when data is scarce
    })
    
    return features

def create_market_expectation_features(
    home_team: str,
    away_team: str,
    h2h_data: Optional[pd.Series] = None
) -> Dict[str, float]:
    """
    Create features that try to capture what the betting market might be seeing.
    
    Args:
        home_team: Home team name
        away_team: Away team name
        h2h_data: Head-to-head data
        
    Returns:
        Dictionary of market expectation features
    """
    features = {}
    
    # Big club bias (markets often favor big clubs early in season)
    big_clubs = {'Arsenal', 'Manchester City', 'Manchester Utd', 'Liverpool', 'Chelsea', 'Tottenham'}
    
    home_is_big_club = 1.0 if home_team in big_clubs else 0.0
    away_is_big_club = 1.0 if away_team in big_clubs else 0.0
    
    features.update({
        'home_big_club_bias': home_is_big_club,
        'away_big_club_bias': away_is_big_club,
        'big_club_vs_small_club': home_is_big_club * (1.0 - away_is_big_club),
        'small_club_vs_big_club': (1.0 - home_is_big_club) * away_is_big_club,
    })
    
    # Recent Premier League status (newly promoted teams often underestimated)
    recently_promoted = {'Burnley', 'Sunderland'}  # Adjust based on actual promoted teams
    
    home_recently_promoted = 1.0 if home_team in recently_promoted else 0.0
    away_recently_promoted = 1.0 if away_team in recently_promoted else 0.0
    
    features.update({
        'home_recently_promoted': home_recently_promoted,
        'away_recently_promoted': away_recently_promoted,
        'promoted_vs_established': home_recently_promoted * (1.0 - away_recently_promoted),
        'established_vs_promoted': (1.0 - home_recently_promoted) * away_recently_promoted,
    })
    
    # H2H market signals
    if h2h_data is not None and not h2h_data.empty:
        home_win_pct = h2h_data.get('home_win_percentage', 50) / 100
        
        # Market would heavily favor team with strong H2H record
        if home_win_pct > 0.7:  # 70%+ H2H win rate
            features['h2h_market_favorite_home'] = 1.0
            features['h2h_market_favorite_away'] = 0.0
        elif home_win_pct < 0.3:  # Less than 30% H2H win rate
            features['h2h_market_favorite_home'] = 0.0
            features['h2h_market_favorite_away'] = 1.0
        else:
            features['h2h_market_favorite_home'] = 0.5
            features['h2h_market_favorite_away'] = 0.5
    else:
        features['h2h_market_favorite_home'] = 0.5
        features['h2h_market_favorite_away'] = 0.5
    
    return features

def amplify_strong_signals(
    features: Dict[str, float],
    h2h_data: Optional[pd.Series] = None,
    season_reliability: float = 0.0
) -> Dict[str, float]:
    """
    Amplify strong signals when season data is unreliable.
    
    Args:
        features: Existing features dictionary
        h2h_data: Head-to-head data
        season_reliability: How reliable current season data is
        
    Returns:
        Features dictionary with amplified signals
    """
    amplified_features = features.copy()
    
    # Amplification factor increases as season reliability decreases
    amplification = 1.0 + (2.0 * (1.0 - season_reliability))  # 1.0 to 3.0 range
    
    # Amplify H2H-based features when they show strong signals
    if h2h_data is not None and not h2h_data.empty:
        home_win_pct = h2h_data.get('home_win_percentage', 50) / 100
        
        # If H2H shows strong dominance, amplify related features
        if home_win_pct > 0.7 or home_win_pct < 0.3:  # Strong H2H signal
            h2h_features_to_amplify = [
                'h2h_adjusted_home_advantage',
                'h2h_adjusted_away_advantage', 
                'h2h_weighted_home_advantage',
                'h2h_home_dominance_score',
                'h2h_market_favorite_home',
                'h2h_market_favorite_away'
            ]
            
            for feature_name in h2h_features_to_amplify:
                if feature_name in amplified_features:
                    amplified_features[feature_name] *= amplification
    
    # Amplify team quality differences when season data is scarce
    quality_features_to_amplify = [
        'team_quality_differential',
        'quality_advantage_home',
        'quality_advantage_away',
        'big_club_vs_small_club',
        'small_club_vs_big_club'
    ]
    
    for feature_name in quality_features_to_amplify:
        if feature_name in amplified_features:
            amplified_features[feature_name] *= amplification
    
    # Add amplification metadata
    amplified_features['signal_amplification_factor'] = amplification
    amplified_features['strong_h2h_signal'] = 1.0 if (
        h2h_data is not None and not h2h_data.empty and 
        abs(h2h_data.get('home_win_percentage', 50) / 100 - 0.5) > 0.2
    ) else 0.0
    
    return amplified_features