"""
Adaptive weighting system for early season predictions.
When teams have played fewer than 8 games, heavily weight H2H and recent historical data.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Optional, Tuple

logger = logging.getLogger(__name__)

# Configuration constants
MIN_GAMES_FOR_BALANCED_WEIGHTING = 8
MAX_H2H_WEIGHT = 0.8  # Maximum weight for H2H when data is scarce
MIN_H2H_WEIGHT = 0.2  # Minimum weight for H2H when sufficient data exists
RECENT_RESULTS_WEIGHT = 0.6  # Weight for recent results when season data is scarce

def calculate_adaptive_weights(
    home_games_played: int,
    away_games_played: int,
    h2h_matches_available: int = 0
) -> Dict[str, float]:
    """
    Calculate adaptive weights based on games played this season.
    
    Args:
        home_games_played: Number of games home team has played this season
        away_games_played: Number of games away team has played this season  
        h2h_matches_available: Number of H2H matches available
        
    Returns:
        Dictionary with weights for different data sources
    """
    # Use the minimum games played between both teams
    min_games_played = min(home_games_played, away_games_played)
    
    # Calculate season data reliability (0 = no data, 1 = fully reliable)
    season_reliability = min(1.0, min_games_played / MIN_GAMES_FOR_BALANCED_WEIGHTING)
    
    # Calculate H2H weight (inverse relationship with season reliability)
    h2h_weight = MAX_H2H_WEIGHT - (MAX_H2H_WEIGHT - MIN_H2H_WEIGHT) * season_reliability
    
    # Boost H2H weight if we have good H2H data
    if h2h_matches_available >= 5:
        h2h_weight = min(0.9, h2h_weight * 1.2)
    elif h2h_matches_available >= 3:
        h2h_weight = min(0.8, h2h_weight * 1.1)
    
    # Calculate other weights
    season_weight = 1.0 - h2h_weight
    recent_form_weight = RECENT_RESULTS_WEIGHT * (1 - season_reliability)
    
    weights = {
        'h2h_weight': h2h_weight,
        'season_weight': season_weight,
        'recent_form_weight': recent_form_weight,
        'season_reliability': season_reliability,
        'min_games_played': min_games_played
    }
    
    logger.info(f"Adaptive weights calculated: min_games={min_games_played}, "
                f"h2h_weight={h2h_weight:.2f}, season_weight={season_weight:.2f}, "
                f"season_reliability={season_reliability:.2f}")
    
    return weights

def apply_adaptive_weighting_to_features(
    features: Dict[str, float],
    weights: Dict[str, float],
    h2h_data: Optional[pd.Series] = None,
    league_stats_dict: Optional[Dict[str, float]] = None
) -> Dict[str, float]:
    """
    Apply adaptive weighting to existing features.
    
    Args:
        features: Dictionary of calculated features
        weights: Adaptive weights from calculate_adaptive_weights
        h2h_data: Head-to-head data if available
        league_stats_dict: League statistics
        
    Returns:
        Dictionary of weighted features
    """
    weighted_features = features.copy()
    
    h2h_weight = weights['h2h_weight']
    season_weight = weights['season_weight']
    season_reliability = weights['season_reliability']
    
    # If we have low season reliability, boost H2H-based features
    if season_reliability < 0.5:  # Less than 4 games played
        
        # Always add these features, but use defaults if no H2H data
        if h2h_data is not None and not h2h_data.empty and not h2h_data.isna().all():
            # We have valid H2H data
            h2h_home_win_pct_raw = h2h_data.get("home_win_percentage", 50)
            h2h_away_win_pct_raw = h2h_data.get("away_win_percentage", 30)
            h2h_draw_pct_raw = h2h_data.get("draw_percentage", 20)
            
            # Handle NaN values
            h2h_home_win_pct = (h2h_home_win_pct_raw / 100) if not pd.isna(h2h_home_win_pct_raw) else 0.5
            h2h_away_win_pct = (h2h_away_win_pct_raw / 100) if not pd.isna(h2h_away_win_pct_raw) else 0.3
            h2h_draw_pct = (h2h_draw_pct_raw / 100) if not pd.isna(h2h_draw_pct_raw) else 0.2
            
            # Get H2H goal data
            h2h_home_goals_raw = h2h_data.get("home_goals", 1.5)
            h2h_away_goals_raw = h2h_data.get("away_goals", 1.0)
            h2h_home_goals = h2h_home_goals_raw if not pd.isna(h2h_home_goals_raw) else 1.5
            h2h_away_goals = h2h_away_goals_raw if not pd.isna(h2h_away_goals_raw) else 1.0
        else:
            # No valid H2H data, use league defaults
            h2h_home_win_pct = 0.5
            h2h_away_win_pct = 0.3
            h2h_draw_pct = 0.2
            h2h_home_goals = 1.5
            h2h_away_goals = 1.0
        
        # Apply H2H adjustments
        weighted_features['h2h_adjusted_home_advantage'] = h2h_home_win_pct * h2h_weight
        weighted_features['h2h_adjusted_away_advantage'] = h2h_away_win_pct * h2h_weight
        weighted_features['h2h_adjusted_draw_tendency'] = h2h_draw_pct * h2h_weight
        
        # Adjust form-based features with H2H bias
        if 'home_form' in features and 'away_form' in features:
            h2h_form_bias = (h2h_home_win_pct - h2h_away_win_pct) * 2  # Scale to form range
            weighted_features['home_form'] = (
                features['home_form'] * season_weight + 
                (1.5 + h2h_form_bias) * h2h_weight
            )
            weighted_features['away_form'] = (
                features['away_form'] * season_weight + 
                (1.5 - h2h_form_bias) * h2h_weight
            )
        
        # Adjust attack/defense strength with H2H goal patterns
        if 'home_attack_strength' in features:
            h2h_attack_factor = h2h_home_goals / 1.5  # Normalize to league average
            weighted_features['home_attack_strength'] = (
                features['home_attack_strength'] * season_weight +
                h2h_attack_factor * h2h_weight
            )
            
        if 'away_attack_strength' in features:
            h2h_attack_factor = h2h_away_goals / 1.0  # Normalize to league average
            weighted_features['away_attack_strength'] = (
                features['away_attack_strength'] * season_weight +
                h2h_attack_factor * h2h_weight
            )
        
        # Add early season indicators
        weighted_features['early_season_flag'] = 1.0
        weighted_features['data_scarcity_factor'] = 1.0 - season_reliability
        
        # Reduce weight of unreliable season-based features
        unreliable_features = [
            'home_points_per_game', 'away_points_per_game',
            'home_recent_scoring_rate', 'away_recent_scoring_rate',
            'home_recent_conceding_rate', 'away_recent_conceding_rate',
            'form_differential', 'momentum_differential'
        ]
        
        for feature in unreliable_features:
            if feature in weighted_features:
                # Reduce the impact of these features when data is scarce
                weighted_features[feature] *= season_reliability
    
    else:
        # Sufficient data available, use balanced weighting
        weighted_features['early_season_flag'] = 0.0
        weighted_features['data_scarcity_factor'] = 0.0
    
    return weighted_features

def enhance_h2h_features_for_early_season(
    h2h_data: Optional[pd.Series],
    home_team: str,
    away_team: str,
    league_stats_dict: Dict[str, float],
    weights: Dict[str, float]
) -> Dict[str, float]:
    """
    Create enhanced H2H features specifically for early season predictions.
    
    Args:
        h2h_data: Head-to-head statistics
        home_team: Home team name
        away_team: Away team name
        league_stats_dict: League statistics
        weights: Adaptive weights
        
    Returns:
        Dictionary of enhanced H2H features
    """
    features = {}
    
    # Default values for when H2H data is not available
    default_home_goals = league_stats_dict.get('home_goals_per_match', 1.5)
    default_away_goals = league_stats_dict.get('away_goals_per_match', 1.0)
    
    # Handle NaN values in league stats
    if pd.isna(default_home_goals):
        default_home_goals = 1.5
    if pd.isna(default_away_goals):
        default_away_goals = 1.0
    
    if h2h_data is None or h2h_data.empty:
        # Use league averages but mark as low confidence
        features.update({
            'h2h_confidence': 0.1,
            'h2h_sample_size': 0,
            'h2h_recency_score': 0.0,
            'h2h_dominance_factor': 0.0,
            'h2h_goal_expectation_home': default_home_goals,
            'h2h_goal_expectation_away': default_away_goals,
            'h2h_home_dominance': 0.5,  # Neutral
            'h2h_away_dominance': 0.3,  # Slight away disadvantage
            'h2h_draw_tendency_strong': 0.2,  # Default draw rate
            'h2h_total_goals_tendency': (default_home_goals + default_away_goals) / 2.5,
            'h2h_btts_likelihood': 0.5  # Neutral BTTS likelihood
        })
        return features
    
    # Calculate H2H confidence based on sample size and recency
    total_matches = h2h_data.get('total_matches', 0)
    if pd.isna(total_matches):
        total_matches = 0
    h2h_confidence = min(1.0, total_matches / 10)  # Max confidence at 10+ matches
    
    # Extract key H2H metrics with NaN handling
    home_win_pct_raw = h2h_data.get('home_win_percentage', 50)
    away_win_pct_raw = h2h_data.get('away_win_percentage', 30)
    draw_pct_raw = h2h_data.get('draw_percentage', 20)
    
    home_win_pct = (home_win_pct_raw / 100) if not pd.isna(home_win_pct_raw) else 0.5
    away_win_pct = (away_win_pct_raw / 100) if not pd.isna(away_win_pct_raw) else 0.3
    draw_pct = (draw_pct_raw / 100) if not pd.isna(draw_pct_raw) else 0.2
    
    # Calculate dominance factor (how much one team dominates the matchup)
    dominance_factor = abs(home_win_pct - away_win_pct)
    
    # Goal expectations based on H2H history with NaN handling
    h2h_home_goals_raw = h2h_data.get('home_goals', default_home_goals)
    h2h_away_goals_raw = h2h_data.get('away_goals', default_away_goals)
    
    h2h_home_goals = h2h_home_goals_raw if not pd.isna(h2h_home_goals_raw) else default_home_goals
    h2h_away_goals = h2h_away_goals_raw if not pd.isna(h2h_away_goals_raw) else default_away_goals
    
    # BTTS percentage with NaN handling
    btts_pct_raw = h2h_data.get('btts_percentage', 50)
    btts_pct = (btts_pct_raw / 100) if not pd.isna(btts_pct_raw) else 0.5
    
    # Recent form in H2H (if available)
    try:
        recent_results_raw = h2h_data.get("recent_results", "[]")
        if isinstance(recent_results_raw, str):
            recent_results = eval(recent_results_raw) if recent_results_raw.strip() else []
        else:
            recent_results = recent_results_raw if recent_results_raw is not None else []
        
        recency_score = min(1.0, len(recent_results) / 5)  # Max score for 5+ recent matches
    except:
        recent_results = []
        recency_score = 0.0
    
    features.update({
        'h2h_confidence': h2h_confidence,
        'h2h_sample_size': total_matches,
        'h2h_recency_score': recency_score,
        'h2h_dominance_factor': dominance_factor,
        'h2h_goal_expectation_home': h2h_home_goals,
        'h2h_goal_expectation_away': h2h_away_goals,
        'h2h_home_dominance': home_win_pct,
        'h2h_away_dominance': away_win_pct,
        'h2h_draw_tendency_strong': draw_pct,
        'h2h_total_goals_tendency': (h2h_home_goals + h2h_away_goals) / 2.5,  # Normalized to league avg
        'h2h_btts_likelihood': btts_pct
    })
    
    return features