"""
Constants used throughout the feature engineering package.
"""

# Team strength calculation constants
HOME_BOOST = 1.1
AWAY_REDUCTION = 0.9
SCORING_WEIGHT = 0.3
DEFENSIVE_WEIGHT = 0.3
WIN_RATE_WEIGHT = 0.2
FORM_WEIGHT = 0.2

# Attack strength calculation constants
HOME_VENUE_BOOST = 1.15
AWAY_VENUE_REDUCTION = 0.85
BASE_WEIGHT = 0.6
FORM_ADJUSTMENT_WEIGHT = 0.4

# Form calculation constants
RECENT_FORM_WEIGHT = 0.7
GOAL_DIFF_IMPACT = 0.2

# Head-to-head calculation constants
RECENCY_BASE_WEIGHT = 0.7
RECENCY_ADJUSTMENT = 0.2
EXPONENTIAL_DECAY = 0.5
GOAL_PATTERN_WEIGHT = 0.1

# Feature preparation constants
MIN_MATCHES_VALID = 5  # Lowered from 8 to 5 for testing purposes
SIGNIFICANT_GOAL_DIFF = 0.1

# String columns to exclude from numeric calculations
STRING_COLUMNS = [
    "Team",
    "Home Team",
    "Away Team",
    "correct_score",
    "form_data_valid_str",
    "btts"
]

# Default values for missing data
DEFAULT_LEAGUE_STATS = {
    "avg_goals_per_match": 2.5,
    "home_goals_per_match": 1.5,
    "away_goals_per_match": 1.0,
    "home_win_percentage": 50,
    "away_win_percentage": 30,
    "draw_percentage": 20,
    "both_teams_scored_percentage": 50
}

# Feature scaling constants
FORM_SCALING_FACTOR = 3.0  # Maximum points possible per game
POSITION_SCALING_FACTOR = 1.0  # For normalizing league positions
GOAL_RATIO_SCALING_FACTOR = 2.5  # Typical maximum goals ratio
