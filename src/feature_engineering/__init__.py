"""
Feature engineering package for the betting project.
This package handles all feature engineering and preparation functionality.
"""

from .core import engineer_features, prepare_features
from .strength import (
    calculate_team_strength,
    calculate_attack_strength,
    calculate_defense_strength,
    calculate_form
)
from .h2h import calculate_h2h_strength
from .adaptive_weighting import (
    calculate_adaptive_weights,
    apply_adaptive_weighting_to_features,
    enhance_h2h_features_for_early_season
)

from .utils import (
    validate_team_stats,
    check_form_data_validity,
    get_match_outcome_features,
    normalize_features,
    get_numeric_columns
)

__all__ = [
    # Core functionality
    'engineer_features',
    'prepare_features',
    
    # Strength calculations
    'calculate_team_strength',
    'calculate_attack_strength',
    'calculate_defense_strength',
    'calculate_form',
    
    # Head-to-head analysis
    'calculate_h2h_strength',
    
    # Adaptive weighting for early season
    'calculate_adaptive_weights',
    'apply_adaptive_weighting_to_features',
    'enhance_h2h_features_for_early_season',
    

    
    # Utility functions
    'validate_team_stats',
    'check_form_data_validity',
    'get_match_outcome_features',
    'normalize_features',
    'get_numeric_columns'
]
