"""
Head-to-head analysis functionality.
Handles calculation of head-to-head strength factors and historical patterns.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Optional
from .constants import (
    RECENCY_BASE_WEIGHT,
    RECENCY_ADJUSTMENT,
    EXPONENTIAL_DECAY,
    GOAL_PATTERN_WEIGHT,
    DEFAULT_LEAGUE_STATS
)

logger = logging.getLogger(__name__)

def calculate_h2h_strength(
    h2h_data: Optional[pd.Series],
    home_team: str,
    away_team: str,
    league_stats_dict: Optional[Dict[str, float]] = None
) -> Dict[str, float]:
    """
    Calculate head-to-head strength factors with enhanced recency bias and goal patterns.

    Args:
        h2h_data: Head-to-head statistics for the two teams
        home_team: Name of the home team
        away_team: Name of the away team
        league_stats_dict: Dictionary of league-wide statistics

    Returns:
        Dictionary of calculated head-to-head strength factors
    """
    # Use league averages if available, otherwise use balanced values - handle None and NaN values
    home_win_pct_raw = league_stats_dict.get("home_win_percentage", DEFAULT_LEAGUE_STATS["home_win_percentage"]) if league_stats_dict else DEFAULT_LEAGUE_STATS["home_win_percentage"]
    away_win_pct_raw = league_stats_dict.get("away_win_percentage", DEFAULT_LEAGUE_STATS["away_win_percentage"]) if league_stats_dict else DEFAULT_LEAGUE_STATS["away_win_percentage"]
    avg_goals_raw = league_stats_dict.get("avg_goals_per_match", DEFAULT_LEAGUE_STATS["avg_goals_per_match"]) if league_stats_dict else DEFAULT_LEAGUE_STATS["avg_goals_per_match"]
    draw_pct_raw = league_stats_dict.get("draw_percentage", DEFAULT_LEAGUE_STATS["draw_percentage"]) if league_stats_dict else DEFAULT_LEAGUE_STATS["draw_percentage"]

    # Handle NaN values by using defaults
    home_win_pct = home_win_pct_raw if home_win_pct_raw is not None and not pd.isna(home_win_pct_raw) else DEFAULT_LEAGUE_STATS["home_win_percentage"]
    away_win_pct = away_win_pct_raw if away_win_pct_raw is not None and not pd.isna(away_win_pct_raw) else DEFAULT_LEAGUE_STATS["away_win_percentage"]
    avg_goals = avg_goals_raw if avg_goals_raw is not None and not pd.isna(avg_goals_raw) else DEFAULT_LEAGUE_STATS["avg_goals_per_match"]
    draw_pct = draw_pct_raw if draw_pct_raw is not None and not pd.isna(draw_pct_raw) else DEFAULT_LEAGUE_STATS["draw_percentage"]

    league_based_values = {
        "home_advantage": float(home_win_pct) / 100,
        "away_advantage": float(away_win_pct) / 100,
        "historical_edge": 0.0,  # Neutral starting point
        "goal_pattern": (float(avg_goals) / 2.5 - 1),
        "draw_tendency": float(draw_pct) / 100
    }

    if h2h_data is None or pd.isna(h2h_data).any():
        return league_based_values

    try:
        # Extract win percentages
        home_wins = h2h_data.get("home_win_percentage", 50) / 100
        away_wins = h2h_data.get("away_win_percentage", 30) / 100
        draws = h2h_data.get("draw_percentage", 20) / 100

        # Calculate recent form with exponential decay
        try:
            recent_results_raw = h2h_data.get("recent_results", "[]")
            if isinstance(recent_results_raw, str):
                recent_results = eval(recent_results_raw) if recent_results_raw.strip() else []
            else:
                recent_results = recent_results_raw if recent_results_raw is not None else []
        except:
            recent_results = []

        total_recent = min(5, len(recent_results))

        if total_recent > 0:
            recent_stats = _analyze_recent_matches(recent_results, total_recent)
            recent_home_ratio = recent_stats["home_ratio"]
            recent_away_ratio = recent_stats["away_ratio"]
            recent_draw_ratio = recent_stats["draw_ratio"]
            goal_pattern = recent_stats["goal_pattern"]
        else:
            recent_home_ratio = recent_away_ratio = recent_draw_ratio = 1/3
            goal_pattern = 0

        # Combine historical and recent performance with dynamic weighting
        recency_weight = _calculate_recency_weight(total_recent)
        historical_weight = 1 - recency_weight

        # Calculate final values
        return {
            "home_advantage": historical_weight * home_wins + recency_weight * recent_home_ratio,
            "away_advantage": historical_weight * away_wins + recency_weight * recent_away_ratio,
            "historical_edge": _calculate_historical_edge(
                home_wins, away_wins,
                recent_home_ratio, recent_away_ratio,
                goal_pattern
            ),
            "goal_pattern": goal_pattern,
            "draw_tendency": historical_weight * draws + recency_weight * recent_draw_ratio
        }

    except Exception as e:
        logger.warning(f"Error processing h2h data: {str(e)}. Using league averages.")
        return league_based_values

def _analyze_recent_matches(
    recent_results: list,
    total_recent: int
) -> Dict[str, float]:
    """
    Analyze recent match results with exponential decay weighting.

    Args:
        recent_results: List of recent match results
        total_recent: Number of recent matches to analyze

    Returns:
        Dictionary containing analyzed statistics
    """
    recent_home_wins = 0
    recent_away_wins = 0
    recent_draws = 0
    goal_pattern = 0
    total_weight = 0

    for i, result in enumerate(recent_results[:total_recent]):
        # Apply exponential decay weight
        weight = np.exp(-EXPONENTIAL_DECAY * i)
        total_weight += weight

        home_goals = int(result[2])
        away_goals = int(result[4])

        if home_goals > away_goals:
            recent_home_wins += weight
        elif home_goals < away_goals:
            recent_away_wins += weight
        else:
            recent_draws += weight

        # Calculate goal pattern (tendency for high/low scoring games)
        total_goals = home_goals + away_goals
        goal_pattern += weight * (total_goals / 2.5 - 1)

    # Normalize values
    return {
        "home_ratio": recent_home_wins / total_weight,
        "away_ratio": recent_away_wins / total_weight,
        "draw_ratio": recent_draws / total_weight,
        "goal_pattern": goal_pattern / total_weight
    }

def _calculate_recency_weight(total_recent: int) -> float:
    """
    Calculate recency weight based on number of recent matches.

    Args:
        total_recent: Number of recent matches

    Returns:
        Calculated recency weight
    """
    return RECENCY_BASE_WEIGHT + RECENCY_ADJUSTMENT * (1 - np.exp(-total_recent / 3))

def _calculate_historical_edge(
    home_wins: float,
    away_wins: float,
    recent_home_ratio: float,
    recent_away_ratio: float,
    goal_pattern: float
) -> float:
    """
    Calculate historical edge with goal pattern influence.

    Args:
        home_wins: Historical home win percentage
        away_wins: Historical away win percentage
        recent_home_ratio: Recent home win ratio
        recent_away_ratio: Recent away win ratio
        goal_pattern: Goal pattern factor

    Returns:
        Calculated historical edge
    """
    historical_diff = home_wins - away_wins
    recent_diff = recent_home_ratio - recent_away_ratio
    
    # Combine historical and recent differences with goal pattern influence
    edge = (historical_diff + recent_diff) / 2
    return edge * (1 + GOAL_PATTERN_WEIGHT * goal_pattern)
