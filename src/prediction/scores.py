"""
Module for handling score predictions and related calculations.

This module implements both traditional Poisson-based score prediction and the Dixon-Coles model,
blending them together for more accurate predictions.
"""

import logging
import numpy as np
from scipy.stats import poisson
from typing import Dict, Optional, Tuple, Union, Any

from .constants import (
    DEFAULT_DRAW_ADJUSTMENT,
    H2H_MULTIPLIER,
    GOAL_PATTERN_MULTIPLIER,
    HIGH_SCORING_REDUCTION,
    LARGE_GOAL_DIFF_FACTOR,
    STRENGTH_RATIO_BOOST,
    STRENGTH_RATIO_REDUCTION,
    BASE_CORRELATION_MULTIPLIER,
    LOW_SCORING_DRAW_BOOST,
    HIGH_SCORING_DRAW_BOOST,
    CLOSE_SCORE_BOOST,
    DEFAULT_DEFENSE_STRENGTH,
    DEFAULT_HOME_ADVANTAGE,
    DEFAULT_RHO,
    MAX_GOALS_TO_CONSIDER,
    HIGH_SCORING_THRESHOLD,
    MAX_GOAL_DIFFERENCE,
    GOAL_PATTERN_ADJUSTMENT
)

logger = logging.getLogger(__name__)

def predict_correct_scores(
    expected_goals: Tuple[float, float],
    over_under_probs: Optional[Dict[str, float]] = None,
    team_strengths: Optional[Dict[str, float]] = None,
    h2h_strength: Optional[Dict[str, float]] = None,
    max_goals: int = MAX_GOALS_TO_CONSIDER,
    blend_weight: float = 0.7,
) -> Dict[str, float]:
    """
    Enhanced correct score prediction using h2h strength calculations and ML insights.

    Args:
        expected_goals: Tuple containing expected goals for home and away teams
        over_under_probs: Dictionary containing over/under probabilities from ML model
        team_strengths: Dictionary containing team attack/defense parameters
        h2h_strength: Dictionary containing head-to-head strength factors
        max_goals: Maximum number of goals to consider
        blend_weight: Weight for Dixon-Coles model (vs original model)

    Returns:
        Dictionary mapping score strings (e.g., "2-1") to their probabilities
    """
    logger.debug(f"Starting score prediction with expected goals: {expected_goals}")
    home_expected, away_expected = expected_goals

    # Adjust expected goals based on ML model and h2h strength
    home_expected, away_expected = _adjust_expected_goals(
        home_expected, away_expected,
        over_under_probs, h2h_strength
    )

    # Get draw adjustment from h2h strength or use default
    draw_adjustment = h2h_strength["draw_tendency"] if h2h_strength else DEFAULT_DRAW_ADJUSTMENT

    # Get team strengths or use defaults
    team_strengths = _get_team_strengths(
        team_strengths, home_expected, away_expected
    )

    # Calculate probabilities using both methods
    dc_probabilities = calculate_dixon_coles_probabilities(
        team_strengths["attack_home"],
        team_strengths["defense_home"],
        team_strengths["attack_away"],
        team_strengths["defense_away"],
        team_strengths["home_advantage"],
        team_strengths["rho"],
        max_goals,
    )

    traditional_probabilities = _calculate_traditional_probabilities(
        home_expected, away_expected,
        draw_adjustment, over_under_probs,
        h2h_strength, max_goals
    )

    # Blend and normalize probabilities
    final_probabilities = _blend_probabilities(
        dc_probabilities,
        traditional_probabilities,
        blend_weight
    )

    logger.info("Score prediction completed")
    return final_probabilities

def dixon_coles_adjustment(
    home_goals: int,
    away_goals: int,
    rho: float
) -> float:
    """
    Calculate the Dixon-Coles adjustment term for low-scoring games.

    Args:
        home_goals: Number of goals scored by home team
        away_goals: Number of goals scored by away team
        rho: Correlation parameter for low-scoring games

    Returns:
        Adjustment factor for the probability calculation
    """
    if home_goals == 0 and away_goals == 0:
        return 1 - rho
    elif home_goals == 0 and away_goals == 1:
        return 1 + rho
    elif home_goals == 1 and away_goals == 0:
        return 1 + rho
    elif home_goals == 1 and away_goals == 1:
        return 1 - rho
    else:
        return 1.0

def calculate_dixon_coles_probabilities(
    attack_home: float,
    defense_home: float,
    attack_away: float,
    defense_away: float,
    home_advantage: float,
    rho: float,
    max_goals: int = MAX_GOALS_TO_CONSIDER,
) -> Dict[str, float]:
    """
    Calculate match probabilities using Dixon-Coles model.

    Args:
        attack_home: Home team's attack strength
        defense_home: Home team's defense strength
        attack_away: Away team's attack strength
        defense_away: Away team's defense strength
        home_advantage: Home advantage parameter
        rho: Correlation parameter for low-scoring games
        max_goals: Maximum number of goals to consider

    Returns:
        Dictionary mapping score strings (e.g., "2-1") to their probabilities
    """
    logger.debug("Starting Dixon-Coles probability calculation")
    
    # Calculate expected goals
    home_expected = np.exp(attack_home + defense_away + home_advantage)
    away_expected = np.exp(attack_away + defense_home)
    
    logger.debug(f"Dixon-Coles expected goals - Home: {home_expected:.2f}, Away: {away_expected:.2f}")

    probabilities = _calculate_dc_probabilities(
        home_expected, away_expected,
        rho, max_goals
    )

    logger.debug("Dixon-Coles probability calculation completed")
    return probabilities

def _adjust_expected_goals(
    home_expected: float,
    away_expected: float,
    over_under_probs: Optional[Dict[str, float]],
    h2h_strength: Optional[Dict[str, float]]
) -> Tuple[float, float]:
    """Adjust expected goals based on ML model and h2h strength."""
    # Adjust based on ML model's over/under predictions
    if over_under_probs:
        logger.debug("Adjusting expected goals based on over/under probabilities")
        over_2_5_prob = over_under_probs.get("Over 2.5", 0.5)
        implied_total = -np.log(1 - over_2_5_prob) * 2.5

        # Maintain ratio between home and away expected goals
        total_xg = home_expected + away_expected
        if total_xg > 0:
            ratio = home_expected / total_xg
            home_expected = implied_total * ratio
            away_expected = implied_total * (1 - ratio)

    # Adjust based on h2h strength
    if h2h_strength:
        logger.debug("Applying h2h strength adjustments")
        home_multiplier = 1 + (h2h_strength["historical_edge"] * H2H_MULTIPLIER)
        away_multiplier = 1 - (h2h_strength["historical_edge"] * H2H_MULTIPLIER)

        goal_pattern_adjustment = h2h_strength["goal_pattern"] * GOAL_PATTERN_MULTIPLIER
        home_expected *= home_multiplier * (1 + goal_pattern_adjustment)
        away_expected *= away_multiplier * (1 + goal_pattern_adjustment)

    return home_expected, away_expected

def _get_team_strengths(
    team_strengths: Optional[Dict[str, float]],
    home_expected: float,
    away_expected: float
) -> Dict[str, float]:
    """Get team strengths or use defaults."""
    if team_strengths is None:
        return {
            "attack_home": np.log(home_expected),
            "defense_home": DEFAULT_DEFENSE_STRENGTH,
            "attack_away": np.log(away_expected),
            "defense_away": DEFAULT_DEFENSE_STRENGTH,
            "home_advantage": DEFAULT_HOME_ADVANTAGE,
            "rho": DEFAULT_RHO,
        }
    return team_strengths

def _calculate_traditional_probabilities(
    home_expected: float,
    away_expected: float,
    draw_adjustment: float,
    over_under_probs: Optional[Dict[str, float]],
    h2h_strength: Optional[Dict[str, float]],
    max_goals: int
) -> Dict[str, float]:
    """Calculate traditional Poisson-based probabilities."""
    probabilities = {}
    strength_ratio = max(home_expected, away_expected) / (min(home_expected, away_expected) + 0.0001)
    total_expected = home_expected + away_expected
    correlation_base = BASE_CORRELATION_MULTIPLIER * (
        1 - abs(home_expected - away_expected) / (total_expected + 0.0001)
    )
    high_scoring_threshold = np.ceil(total_expected)

    for home_goals in range(max_goals + 1):
        for away_goals in range(max_goals + 1):
            correlation_factor = _calculate_correlation_factor(
                home_goals, away_goals,
                home_expected, away_expected,
                draw_adjustment, correlation_base,
                total_expected, strength_ratio,
                high_scoring_threshold
            )

            # Apply additional adjustments
            correlation_factor = _apply_additional_adjustments(
                correlation_factor,
                home_goals, away_goals,
                total_expected, over_under_probs,
                h2h_strength
            )

            # Calculate final probability
            home_prob = poisson.pmf(home_goals, home_expected)
            away_prob = poisson.pmf(away_goals, away_expected)
            probabilities[f"{home_goals}-{away_goals}"] = (
                home_prob * away_prob * correlation_factor
            )

    # Normalize probabilities
    total_prob = sum(probabilities.values())
    return {k: v / total_prob for k, v in probabilities.items()}

def _calculate_correlation_factor(
    home_goals: int,
    away_goals: int,
    home_expected: float,
    away_expected: float,
    draw_adjustment: float,
    correlation_base: float,
    total_expected: float,
    strength_ratio: float,
    high_scoring_threshold: float
) -> float:
    """Calculate correlation factor for a specific score."""
    correlation_factor = 1.0

    # Draw adjustments
    if home_goals == away_goals:
        correlation_factor *= 1 + draw_adjustment
        if total_expected < HIGH_SCORING_THRESHOLD:
            correlation_factor += correlation_base + LOW_SCORING_DRAW_BOOST
        else:
            correlation_factor += correlation_base + HIGH_SCORING_DRAW_BOOST
    elif abs(home_goals - away_goals) == 1:
        correlation_factor += correlation_base + CLOSE_SCORE_BOOST

    # Team strength adjustments
    if home_expected > away_expected:
        if home_goals > away_goals:
            correlation_factor *= STRENGTH_RATIO_BOOST * strength_ratio
        elif home_goals < away_goals:
            correlation_factor *= STRENGTH_RATIO_REDUCTION / strength_ratio
    elif away_expected > home_expected:
        if away_goals > home_goals:
            correlation_factor *= STRENGTH_RATIO_BOOST * strength_ratio
        elif away_goals < home_goals:
            correlation_factor *= STRENGTH_RATIO_REDUCTION / strength_ratio

    return correlation_factor

def _apply_additional_adjustments(
    correlation_factor: float,
    home_goals: int,
    away_goals: int,
    total_expected: float,
    over_under_probs: Optional[Dict[str, float]],
    h2h_strength: Optional[Dict[str, float]]
) -> float:
    """Apply additional adjustments to correlation factor."""
    total_goals = home_goals + away_goals

    # High-scoring game adjustment
    if total_goals > total_expected:
        reduction_factor = HIGH_SCORING_REDUCTION ** (total_goals - total_expected)
        correlation_factor *= reduction_factor

    # Goal difference plausibility
    if abs(home_goals - away_goals) > MAX_GOAL_DIFFERENCE:
        correlation_factor *= LARGE_GOAL_DIFF_FACTOR

    # Over/under adjustment
    if over_under_probs:
        if total_goals > HIGH_SCORING_THRESHOLD:
            correlation_factor *= over_under_probs.get("Over 2.5", 0.5)
        else:
            correlation_factor *= over_under_probs.get("Under 2.5", 0.5)

    # H2H goal pattern adjustment
    if h2h_strength and h2h_strength["goal_pattern"] != 0:
        if total_goals > total_expected:
            correlation_factor *= 1 + h2h_strength["goal_pattern"] * GOAL_PATTERN_ADJUSTMENT
        else:
            correlation_factor *= 1 - h2h_strength["goal_pattern"] * GOAL_PATTERN_ADJUSTMENT

    return correlation_factor

def _calculate_dc_probabilities(
    home_expected: float,
    away_expected: float,
    rho: float,
    max_goals: int
) -> Dict[str, float]:
    """Calculate Dixon-Coles probabilities."""
    probabilities = {}

    for home_goals in range(max_goals + 1):
        for away_goals in range(max_goals + 1):
            home_prob = poisson.pmf(home_goals, home_expected)
            away_prob = poisson.pmf(away_goals, away_expected)
            dc_adjustment = dixon_coles_adjustment(home_goals, away_goals, rho)
            probabilities[f"{home_goals}-{away_goals}"] = (
                home_prob * away_prob * dc_adjustment
            )

    # Normalize probabilities
    total_prob = sum(probabilities.values())
    return {k: v / total_prob for k, v in probabilities.items()}

def _blend_probabilities(
    dc_probabilities: Dict[str, float],
    traditional_probabilities: Dict[str, float],
    blend_weight: float
) -> Dict[str, float]:
    """Blend Dixon-Coles and traditional probabilities."""
    final_probabilities = {}
    all_scores = set(dc_probabilities.keys()) | set(traditional_probabilities.keys())

    for score in all_scores:
        dc_prob = dc_probabilities.get(score, 0)
        trad_prob = traditional_probabilities.get(score, 0)
        final_probabilities[score] = blend_weight * dc_prob + (1 - blend_weight) * trad_prob

    # Normalize probabilities
    total_final = sum(final_probabilities.values())
    return {k: v / total_final for k, v in final_probabilities.items()}
