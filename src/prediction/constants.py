"""
Constants used throughout the prediction package.
"""

# Prediction thresholds and weights
# Reduced model weight to fix prediction-xG inconsistency
# Model was heavily biased toward draws, so we prioritize xG-based Poisson calculations
MODEL_WEIGHT = 0.2
XG_WEIGHT = 0.8
MIN_PROBABILITY = 0.15
MAX_PROBABILITY = 0.75

# Expected goals calculation weights
SEASON_WEIGHT = 0.4
RECENT_FORM_WEIGHT = 0.6
H2H_WEIGHT = 0.2
FORM_IMPACT_WEIGHT = 0.4
POINTS_DIFF_HOME_WEIGHT = 0.02  # Reduced from 0.2 to prevent extreme multipliers
POINTS_DIFF_AWAY_WEIGHT = 0.01  # Reduced from 0.1 to prevent extreme multipliers

# Team strength adjustments (reduced for more realistic xG)
STRONG_HOME_BOOST = 1.20
NORMAL_HOME_BOOST = 1.10
NORMAL_AWAY_REDUCTION = 0.85
BASE_AWAY_ADJUSTMENT = 0.90
AWAY_STRENGTH_FACTOR = 0.3
STRENGTH_ADJUSTMENT_FACTOR = 0.1  # Factor for adjusting probabilities based on team strength differences
FORM_ADJUSTMENT_FACTOR = 0.05     # Factor for adjusting probabilities based on form differences

# Expected goals ranges
STRONG_ATTACK_THRESHOLD = 1.2
MAX_HOME_XG_STRONG = 3.0
MAX_HOME_XG_NORMAL = 2.5
MAX_AWAY_XG_STRONG = 2.5
MAX_AWAY_XG_NORMAL = 2.2
MIN_HOME_XG_STRONG = 0.5  # Reduced from 1.0 to allow realistic low values
MIN_HOME_XG_NORMAL = 0.3  # Reduced from 0.5 to allow realistic low values
MIN_AWAY_XG_STRONG = 0.3  # Reduced from 0.8 to allow realistic low values
MIN_AWAY_XG_NORMAL = 0.2  # Reduced from 0.3 to allow realistic low values

# Score prediction constants
DEFAULT_DRAW_ADJUSTMENT = 0.25
H2H_MULTIPLIER = 0.2
GOAL_PATTERN_MULTIPLIER = 0.15
HIGH_SCORING_REDUCTION = 0.9
LARGE_GOAL_DIFF_FACTOR = 0.8
STRENGTH_RATIO_BOOST = 1.1
STRENGTH_RATIO_REDUCTION = 0.9
HIGH_SCORING_THRESHOLD = 3.5     # Threshold for considering a game high-scoring
MAX_GOAL_DIFFERENCE = 5          # Maximum plausible goal difference
GOAL_PATTERN_ADJUSTMENT = 0.1    # Adjustment factor for h2h goal patterns

# Correlation factors
BASE_CORRELATION_MULTIPLIER = 0.1
LOW_SCORING_DRAW_BOOST = 0.15
HIGH_SCORING_DRAW_BOOST = 0.08
CLOSE_SCORE_BOOST = 0.05

# Dixon-Coles model defaults
DEFAULT_DEFENSE_STRENGTH = -0.1
DEFAULT_HOME_ADVANTAGE = 0.3
DEFAULT_RHO = -0.1

# Probability adjustment constants
HISTORICAL_WEIGHT = 0.2
SOFTMAX_TEMPERATURE = 1.5
DEFAULT_BLEND_WEIGHT = 0.7
BALANCED_MIN_PROB = 0.4
BALANCED_MAX_PROB = 0.6
CLOSE_TO_THRESHOLD = 0.3
ADJUSTMENT_MULTIPLIER = 0.1

# Double chance constants
MIN_DOUBLE_CHANCE_PROB = 0.3
MAX_DOUBLE_CHANCE_PROB = 0.9

# Score probability constants
SCORE_PROB_TOLERANCE = 0.01

# Probability difference thresholds
STRONG_PROBABILITY_DIFF = 0.25    # Threshold for strong probability difference
MEDIUM_PROBABILITY_DIFF = 0.15    # Threshold for medium probability difference
BALANCED_PROBABILITY_THRESHOLD = 0.45  # Threshold for balanced probability distribution

# Risk assessment constants
RISK_SCORES = {
    "Low": 1,
    "Medium": 2,
    "High": 3
}
LOW_RISK_THRESHOLD = 1.5     # Threshold for low risk assessment
MEDIUM_RISK_THRESHOLD = 2.5  # Threshold for medium risk assessment

# Excel styling constants
EXCEL_STYLES = {
    'header': {
        'font': {'bold': True, 'color': 'FFFFFF'},
        'fill': {'start_color': '4F81BD', 'end_color': '4F81BD', 'fill_type': 'solid'},
        'alignment': {'horizontal': 'center', 'vertical': 'center', 'wrap_text': True}
    },
    'title': {
        'font': {'size': 16, 'bold': True},
        'alignment': {'horizontal': 'center'}
    },
    'confidence_colors': {
        'High': {'fill': {'start_color': 'C6EFCE', 'end_color': 'C6EFCE', 'fill_type': 'solid'}},
        'Medium': {'fill': {'start_color': 'FFEB9C', 'end_color': 'FFEB9C', 'fill_type': 'solid'}},
        'Low': {'fill': {'start_color': 'FFC7CE', 'end_color': 'FFC7CE', 'fill_type': 'solid'}}
    },
    'risk_colors': {
        'Low Risk': {'fill': {'start_color': 'C6EFCE', 'end_color': 'C6EFCE', 'fill_type': 'solid'}},
        'Medium Risk': {'fill': {'start_color': 'FFEB9C', 'end_color': 'FFEB9C', 'fill_type': 'solid'}},
        'High Risk': {'fill': {'start_color': 'FFC7CE', 'end_color': 'FFC7CE', 'fill_type': 'solid'}}
    },
    'alternating_row': {
        'fill': {'start_color': 'F0F0F0', 'end_color': 'F0F0F0', 'fill_type': 'solid'}
    }
}

# Excel column headers
EXCEL_HEADERS = {
    'main': [
        "Home Team",
        "Away Team",
        "Three-Way Prediction",
        "Home Win",
        "Draw",
        "Away Win",
        "Confidence Level",
        "Risk Level",
        "Double Chance Prediction",
        "Home or Draw",
        "Away or Draw",
        "Home or Away",
        "BTTS Prediction",
        "BTTS Yes",
        "BTTS No",
        "Over/Under 1.5 Prediction",
        "Over 1.5",
        "Under 1.5",
        "Over/Under 2.5 Prediction",
        "Over 2.5",
        "Under 2.5",
        "Over/Under 3.5 Prediction",
        "Over 3.5",
        "Under 3.5",
        "Expected Goals Home",
        "Expected Goals Away",
        "Most Likely Score",
        "Score Probability",
        "Form Data Validity"
    ],
    'analysis': [
        "Match",
        "Prediction Confidence",
        "Risk Assessment",
        "Top 3 Likely Scores",
        "BTTS Probability",
        "Home Clean Sheet",
        "Away Clean Sheet",
        "Expected Total Goals",
        "Notes"
    ]
}

# Prediction types
PREDICTION_TYPES = {
    'three_way': ['Home', 'Draw', 'Away'],
    'double_chance': ['Home or Draw', 'Away or Draw', 'Home or Away'],
    'btts': ['No', 'Yes'],
    'over_under': ['Under', 'Over']
}

# Form validation
MIN_MATCHES_FOR_VALID_FORM = 5  # Lowered from 8 to 5 for testing purposes

# Output formatting
OUTPUT_SECTIONS = [
    'main_predictions',
    'confidence_levels',
    'score_predictions',
    'value_analysis',
    'risk_assessment'
]

# Market-specific constants
BTTS_MARKET = {
    'name': 'btts',
    'outcomes': ['No', 'Yes'],
    'default_probabilities': {'No': 0.5, 'Yes': 0.5}
}

THREE_WAY_MARKET = {
    'name': 'three_way',
    'outcomes': ['Home', 'Draw', 'Away'],
    'default_probabilities': {'Home': 0.4, 'Draw': 0.2, 'Away': 0.4}
}

DOUBLE_CHANCE_MARKET = {
    'name': 'double_chance',
    'outcomes': ['Home or Draw', 'Away or Draw', 'Home or Away'],
    'default_probabilities': {
        'Home or Draw': 0.6,
        'Away or Draw': 0.6,
        'Home or Away': 0.8
    }
}

# Confidence thresholds
CONFIDENCE_THRESHOLDS = {
    'high': 0.7,
    'medium': 0.5,
    'low': 0.3
}

# Risk assessment thresholds
RISK_THRESHOLDS = {
    'high': 0.7,
    'medium': 0.4,
    'low': 0.2
}

# Expected goals (xG) thresholds
XG_THRESHOLDS = {
    'high_scoring': 2.5,
    'low_scoring': 1.5,
    'very_low_scoring': 1.0
}

# Score prediction settings
MAX_GOALS_TO_CONSIDER = 9
MIN_PROBABILITY_FOR_SCORE = 0.01

# Output formatting templates
PREDICTION_FORMAT = {
    'main_predictions': "Main Predictions:",
    'confidence_levels': "Confidence Levels:",
    'score_predictions': "Score Predictions:",
    'value_analysis': "Value Analysis:",
    'risk_assessment': "Risk Assessment:"
}

# Default league statistics
DEFAULT_LEAGUE_STATS = {
    'home_goals_per_match': 1.5,
    'away_goals_per_match': 1.0,
    'home_win_percentage': 45,
    'away_win_percentage': 30,
    'draw_percentage': 25
}

# Significant thresholds
SIGNIFICANT_POINTS_DIFF = 0.1
SIGNIFICANT_FORM_DIFF = 0.2
SIGNIFICANT_STRENGTH_DIFF = 0.15
