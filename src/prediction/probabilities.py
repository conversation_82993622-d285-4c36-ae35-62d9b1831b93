"""
Module for handling probability calculations and adjustments.

This module provides functions for blending, calibrating, and adjusting probabilities
using various methods including historical data and statistical techniques.
"""

import logging
import numpy as np
import pandas as pd
from scipy.stats import poisson
from sklearn.calibration import CalibratedClassifierCV
import tensorflow as tf
from typing import Dict, Optional, Any, Union, List, Tuple

from .constants import (
    MODEL_WEIGHT,
    XG_WEIGHT,
    MIN_PROBABILITY,
    MAX_PROBABILITY,
    BALANCED_MIN_PROB,
    BALANCED_MAX_PROB,
    CLOSE_TO_THRESHOLD,
    ADJUSTMENT_MULTIPLIER,
    HISTORICAL_WEIGHT,
    SOFTMAX_TEMPERATURE,
    DEFAULT_BLEND_WEIGHT
)

logger = logging.getLogger(__name__)

def blend_predictions(
    model_probs: Dict[str, float],
    calc_probs: Dict[str, float],
    blend_weight: float = DEFAULT_BLEND_WEIGHT,
) -> Dict[str, float]:
    """
    Blend model predictions with calculated probabilities.

    Args:
        model_probs: Probabilities from the model
        calc_probs: Calculated probabilities from expected goals
        blend_weight: Weight to give to model probabilities (0-1)

    Returns:
        Dictionary of blended probabilities
    """
    logger.debug("Starting probability blending")
    logger.debug(f"Model probabilities: {model_probs}")
    logger.debug(f"Calculated probabilities: {calc_probs}")

    try:
        blended_probs = {}
        for key in model_probs:
            if key in calc_probs:
                # Ensure probabilities are within valid range before blending
                model_prob = np.clip(model_probs[key], MIN_PROBABILITY, MAX_PROBABILITY)
                calc_prob = np.clip(calc_probs[key], MIN_PROBABILITY, MAX_PROBABILITY)
                
                blended_probs[key] = (
                    blend_weight * model_prob + (1 - blend_weight) * calc_prob
                )
            else:
                logger.warning(f"Key '{key}' not found in calculated probabilities")
                blended_probs[key] = model_probs[key]

        # Normalize probabilities
        total = sum(blended_probs.values())
        if total > 0:
            normalized_probs = {k: v / total for k, v in blended_probs.items()}
        else:
            logger.warning("Sum of probabilities is 0, using uniform distribution")
            normalized_probs = {k: 1.0/len(blended_probs) for k in blended_probs}

        logger.debug(f"Blended probabilities: {normalized_probs}")
        return normalized_probs

    except Exception as e:
        logger.error(f"Error in probability blending: {str(e)}")
        return model_probs

def calibrate_probabilities(
    model: Union[tf.keras.Sequential, Any],
    X: pd.DataFrame,
    prob_dict: Dict[str, float],
    recent_history: Optional[pd.DataFrame] = None,
) -> Dict[str, float]:
    """
    Calibrate probabilities using Platt scaling and historical performance.

    Args:
        model: The trained model (neural network or sklearn model)
        X: Input features
        prob_dict: Dictionary of initial probabilities
        recent_history: Recent prediction history for additional calibration

    Returns:
        Dictionary of calibrated probabilities
    """
    logger.debug("Starting probability calibration")
    logger.debug(f"Initial probabilities: {prob_dict}")

    try:
        # Apply model-specific calibration
        calibrated_probs = _apply_model_calibration(model, X, prob_dict)

        # Ensure probabilities are within valid range
        calibrated_probs = {
            k: np.clip(v, MIN_PROBABILITY, MAX_PROBABILITY)
            for k, v in calibrated_probs.items()
        }

        # Normalize after clipping
        total = sum(calibrated_probs.values())
        if total > 0:
            calibrated_probs = {k: v / total for k, v in calibrated_probs.items()}
        else:
            logger.warning("Sum of probabilities is 0, using uniform distribution")
            calibrated_probs = {k: 1.0/len(calibrated_probs) for k in calibrated_probs}

        # Apply historical correction if available
        if recent_history is not None:
            logger.debug("Applying historical correction")
            calibrated_probs = adjust_probabilities_with_history(
                calibrated_probs, recent_history
            )

        logger.debug(f"Calibrated probabilities: {calibrated_probs}")
        return calibrated_probs

    except Exception as e:
        logger.error(f"Error in probability calibration: {str(e)}")
        return prob_dict

def adjust_probabilities_with_history(
    prob_dict: Dict[str, float],
    recent_history: pd.DataFrame
) -> Dict[str, float]:
    """
    Adjust probabilities based on recent prediction history.

    Args:
        prob_dict: Dictionary of calibrated probabilities
        recent_history: Recent prediction history

    Returns:
        Dictionary of adjusted probabilities
    """
    logger.debug("Starting historical probability adjustment")
    logger.debug(f"Initial probabilities: {prob_dict}")

    try:
        # Calculate historical accuracy for each outcome
        historical_accuracy = _calculate_historical_accuracy(prob_dict, recent_history)

        # Adjust probabilities based on historical accuracy
        adjusted_probs = _adjust_with_historical_accuracy(prob_dict, historical_accuracy)

        # Ensure probabilities are within valid range
        adjusted_probs = {
            k: np.clip(v, MIN_PROBABILITY, MAX_PROBABILITY)
            for k, v in adjusted_probs.items()
        }

        # Normalize probabilities
        total = sum(adjusted_probs.values())
        if total > 0:
            normalized_probs = {k: v / total for k, v in adjusted_probs.items()}
        else:
            logger.warning("Sum of probabilities is 0, using uniform distribution")
            normalized_probs = {k: 1.0/len(adjusted_probs) for k in adjusted_probs}

        logger.debug(f"Adjusted probabilities: {normalized_probs}")
        return normalized_probs

    except Exception as e:
        logger.error(f"Error in historical probability adjustment: {str(e)}")
        return prob_dict

def calculate_over_under_probs(
    expected_goals: Tuple[float, float],
    thresholds: List[float] = [1.5, 2.5, 3.5],
    bias_correction: float = 0.05,
) -> Dict[str, float]:
    """
    Calculate over/under probabilities with enhanced accuracy and multiple thresholds.

    Args:
        expected_goals: Tuple containing expected goals for home and away teams
        thresholds: List of goal thresholds to calculate probabilities for
        bias_correction: Bias correction factor for probability adjustments

    Returns:
        Dictionary of over/under probabilities for different thresholds
    """
    logger.debug(f"Calculating over/under probabilities for xG: {expected_goals}")
    home_xg, away_xg = expected_goals
    total_xg = home_xg + away_xg
    probs = {}

    try:
        # Ensure expected goals are non-negative
        home_xg = max(0, home_xg)
        away_xg = max(0, away_xg)
        total_xg = home_xg + away_xg

        # Dynamic max goals calculation based on total expected goals
        max_goals = max(10, int(total_xg * 2.5))

        for threshold in thresholds:
            logger.debug(f"Processing threshold: {threshold}")
            
            # Calculate base probabilities
            under_prob = _calculate_under_probability(home_xg, away_xg, threshold, max_goals)
            over_prob = 1 - under_prob

            # Apply adjustments
            under_prob, over_prob = _adjust_over_under_probs(
                under_prob, over_prob, total_xg, threshold, bias_correction
            )

            # Store results
            probs[f"Over {threshold}"] = over_prob
            probs[f"Under {threshold}"] = under_prob

        return probs

    except Exception as e:
        logger.error(f"Error calculating over/under probabilities: {str(e)}")
        return {k: 0.5 for k in [f"Over {t}" for t in thresholds] + [f"Under {t}" for t in thresholds]}

def calculate_double_chance_probabilities(
    three_way_probs: Dict[str, float]
) -> Dict[str, float]:
    """
    Calculate double chance probabilities from three-way probabilities.

    Args:
        three_way_probs: Three-way probability dictionary with Home, Draw, Away keys

    Returns:
        Dictionary of double chance probabilities
    """
    logger.debug(f"Calculating double chance probabilities from: {three_way_probs}")

    try:
        # Extract probabilities with safety checks
        home_prob = np.clip(three_way_probs.get("Home", 0), MIN_PROBABILITY, MAX_PROBABILITY)
        draw_prob = np.clip(three_way_probs.get("Draw", 0), MIN_PROBABILITY, MAX_PROBABILITY)
        away_prob = np.clip(three_way_probs.get("Away", 0), MIN_PROBABILITY, MAX_PROBABILITY)

        # Calculate double chance probabilities
        double_chance_probs = {
            "Home or Draw": min(MAX_PROBABILITY, home_prob + draw_prob),
            "Away or Draw": min(MAX_PROBABILITY, away_prob + draw_prob),
            "Home or Away": min(MAX_PROBABILITY, home_prob + away_prob)
        }

        # Ensure minimum probability
        double_chance_probs = {
            k: max(v, MIN_PROBABILITY) for k, v in double_chance_probs.items()
        }

        # Normalize if needed
        total = sum(double_chance_probs.values())
        if total > 0:
            double_chance_probs = {k: v / total for k, v in double_chance_probs.items()}

        logger.debug(f"Double chance probabilities: {double_chance_probs}")
        return double_chance_probs

    except Exception as e:
        logger.error(f"Error calculating double chance probabilities: {str(e)}")
        return {
            "Home or Draw": 0.33,
            "Away or Draw": 0.33,
            "Home or Away": 0.34
        }

def _apply_model_calibration(
    model: Union[tf.keras.Sequential, Any],
    X: pd.DataFrame,
    prob_dict: Dict[str, float]
) -> Dict[str, float]:
    """Apply model-specific probability calibration."""
    try:
        if isinstance(model, tf.keras.Sequential):
            # For neural networks, apply temperature scaling
            logger.debug("Applying temperature scaling for neural network")
            logits = np.log(np.array(list(prob_dict.values())) + 1e-10)  # Add small constant to avoid log(0)
            scaled_probs = np.exp(logits / SOFTMAX_TEMPERATURE)
            scaled_probs = scaled_probs / np.sum(scaled_probs)
            return dict(zip(prob_dict.keys(), scaled_probs))
        else:
            # For sklearn models, use existing calibrated classifier if available
            logger.debug("Using calibrated classifier for non-neural network model")
            if hasattr(model, 'predict_proba'):
                calibrated_probs = model.predict_proba(X)[0]
                return dict(zip(prob_dict.keys(), calibrated_probs))
            else:
                logger.warning("Model does not support probability prediction")
                return prob_dict

    except Exception as e:
        logger.error(f"Error in model calibration: {str(e)}")
        return prob_dict

def _calculate_historical_accuracy(
    prob_dict: Dict[str, float],
    recent_history: pd.DataFrame
) -> Dict[str, float]:
    """Calculate historical accuracy for each outcome."""
    historical_accuracy = {}
    try:
        for outcome in prob_dict.keys():
            if outcome in recent_history:
                predicted_probs = recent_history[f"Predicted_{outcome}"]
                actual_outcomes = recent_history[f"Actual_{outcome}"]
                # Only consider predictions with sufficient confidence
                confident_predictions = predicted_probs > 0.5
                if np.any(confident_predictions):
                    accuracy = np.mean(actual_outcomes[confident_predictions])
                    historical_accuracy[outcome] = accuracy
                    logger.debug(f"Historical accuracy for {outcome}: {accuracy:.3f}")
                else:
                    historical_accuracy[outcome] = 0.5
                    logger.debug(f"No confident predictions for {outcome}, using 0.5")
    except Exception as e:
        logger.error(f"Error calculating historical accuracy: {str(e)}")
        historical_accuracy = {k: 0.5 for k in prob_dict.keys()}
    return historical_accuracy

def _adjust_with_historical_accuracy(
    prob_dict: Dict[str, float],
    historical_accuracy: Dict[str, float]
) -> Dict[str, float]:
    """Adjust probabilities using historical accuracy."""
    adjusted_probs = {}
    try:
        for outcome, prob in prob_dict.items():
            if outcome in historical_accuracy:
                # Apply historical adjustment with bounds
                historical_adj = historical_accuracy[outcome]
                adjusted_prob = (
                    (1 - HISTORICAL_WEIGHT) * prob + 
                    HISTORICAL_WEIGHT * historical_adj
                )
                adjusted_probs[outcome] = np.clip(
                    adjusted_prob, MIN_PROBABILITY, MAX_PROBABILITY
                )
            else:
                logger.warning(f"No historical data for outcome: {outcome}")
                adjusted_probs[outcome] = prob
    except Exception as e:
        logger.error(f"Error adjusting with historical accuracy: {str(e)}")
        adjusted_probs = prob_dict
    return adjusted_probs

def _calculate_under_probability(
    home_xg: float,
    away_xg: float,
    threshold: float,
    max_goals: int
) -> float:
    """Calculate under probability using Poisson distribution."""
    try:
        under_prob = 0
        for home_goals in range(max_goals + 1):
            for away_goals in range(max_goals + 1):
                if home_goals + away_goals < threshold:
                    home_prob = poisson.pmf(home_goals, home_xg)
                    away_prob = poisson.pmf(away_goals, away_xg)
                    under_prob += home_prob * away_prob
        return np.clip(under_prob, MIN_PROBABILITY, MAX_PROBABILITY)
    except Exception as e:
        logger.error(f"Error calculating under probability: {str(e)}")
        return 0.5

def _adjust_over_under_probs(
    under_prob: float,
    over_prob: float,
    total_xg: float,
    threshold: float,
    bias_correction: float
) -> Tuple[float, float]:
    """Apply adjustments to over/under probabilities."""
    try:
        # Calculate threshold distance and dynamic adjustment
        threshold_distance = abs(total_xg - threshold)
        if threshold_distance < CLOSE_TO_THRESHOLD:
            # Smaller adjustment for close cases
            adjustment = bias_correction * (threshold_distance / CLOSE_TO_THRESHOLD)
        else:
            # Larger adjustment for clear cases
            adjustment = bias_correction * min(1.0, threshold_distance * ADJUSTMENT_MULTIPLIER)

        # Apply adjustments based on expected goals
        if total_xg > threshold:
            under_prob = np.clip(under_prob - adjustment, MIN_PROBABILITY, MAX_PROBABILITY)
        else:
            over_prob = np.clip(over_prob - adjustment, MIN_PROBABILITY, MAX_PROBABILITY)

        # Ensure probabilities sum to 1
        total = under_prob + over_prob
        if total > 0:
            under_prob /= total
            over_prob /= total
        else:
            under_prob = over_prob = 0.5

        return under_prob, over_prob

    except Exception as e:
        logger.error(f"Error adjusting over/under probabilities: {str(e)}")
        return 0.5, 0.5
