"""
Module for validating and adjusting prediction probabilities.

This module provides functions to validate and calibrate prediction probabilities,
ensuring they fall within acceptable ranges and maintain logical consistency.
"""

import logging
from typing import Dict, Tuple, Any, List, Optional, Union
import numpy as np

from .constants import (
    MIN_PROBABILITY,
    MAX_PROBABILITY,
    BALANCED_MIN_PROB,
    BALANCED_MAX_PROB,
    MIN_DOUBLE_CHANCE_PROB,
    MAX_DOUBLE_CHANCE_PROB,
    DOUBLE_CHANCE_MARKET,
    SCORE_PROB_TOLERANCE
)

logger = logging.getLogger(__name__)

def validate_predictions(
    predictions: Dict[str, Dict[str, Union[str, Dict[str, float]]]],
    correct_scores: Dict[str, float],
    thresholds: Optional[Dict[str, float]] = None
) -> Tuple[Dict[str, Any], Dict[str, float], List[str]]:
    """
    Validate prediction probabilities and adjust if they fall outside acceptable ranges.

    Args:
        predictions: Dictionary containing all predictions
        correct_scores: Dictionary of correct score probabilities
        thresholds: Dictionary of threshold values for validation. If None, uses defaults.

    Returns:
        Tuple containing:
        - Validated predictions
        - Validated correct scores
        - List of warning messages
    """
    logger.debug("Starting prediction validation")
    warnings: List[str] = []
    validated_predictions = predictions.copy()
    
    # Use provided thresholds or defaults from constants
    default_thresholds = {
        "min_prob": MIN_PROBABILITY,
        "max_prob": MAX_PROBABILITY,
        "min_draw": BALANCED_MIN_PROB,
        "max_draw": BALANCED_MAX_PROB
    }
    thresholds = thresholds or default_thresholds

    try:
        # Validate main predictions
        for market_type, prediction in validated_predictions.items():
            if isinstance(prediction, dict) and "probabilities" in prediction:
                validated_predictions[market_type] = _validate_market_probabilities(
                    market_type,
                    prediction,
                    thresholds,
                    warnings
                )

        # Validate correct scores
        validated_scores = _validate_correct_scores(correct_scores, warnings)

        return validated_predictions, validated_scores, warnings

    except Exception as e:
        logger.error(f"Error in prediction validation: {str(e)}")
        return predictions, correct_scores, [f"Validation error: {str(e)}"]

def calibrate_double_chance_probabilities(
    prob_dict: Dict[str, float]
) -> Dict[str, float]:
    """
    Calibrate double chance probabilities ensuring logical consistency.

    Args:
        prob_dict: Dictionary of double chance probabilities

    Returns:
        Calibrated double chance probability dictionary
    """
    logger.debug("Starting double chance probability calibration")
    logger.debug(f"Initial probabilities: {prob_dict}")

    try:
        calibrated_probs = _calibrate_probabilities(prob_dict)
        logger.debug(f"Calibrated probabilities: {calibrated_probs}")
        return calibrated_probs

    except Exception as e:
        logger.error(f"Error in double chance probability calibration: {str(e)}")
        return prob_dict

def _validate_market_probabilities(
    market_type: str,
    prediction: Dict[str, Any],
    thresholds: Dict[str, float],
    warnings: List[str]
) -> Dict[str, Any]:
    """Validate probabilities for a specific market type."""
    logger.debug(f"Validating {market_type} probabilities")
    validated_prediction = prediction.copy()
    probs = prediction["probabilities"].copy()

    try:
        if market_type == "three_way":
            probs = _validate_three_way_probabilities(probs, thresholds, warnings)
        elif market_type == "double_chance":
            probs = _validate_double_chance_probabilities(probs, warnings)
        elif market_type.startswith("over_under"):
            probs = _validate_over_under_probabilities(probs, warnings)
        elif market_type == "btts":
            probs = _validate_btts_probabilities(probs, warnings)

        # Update prediction with validated probabilities
        validated_prediction["probabilities"] = probs
        
        # Update prediction string if needed
        if "prediction" in validated_prediction:
            max_prob = max(probs.values())
            validated_prediction["prediction"] = [k for k, v in probs.items() if v == max_prob][0]

        return validated_prediction

    except Exception as e:
        logger.error(f"Error validating {market_type} probabilities: {str(e)}")
        return prediction

def _validate_three_way_probabilities(
    probs: Dict[str, float],
    thresholds: Dict[str, float],
    warnings: List[str]
) -> Dict[str, float]:
    """Validate and adjust three-way probabilities."""
    validated_probs = {}

    # Check for extreme probabilities
    for outcome, prob in probs.items():
        if prob < thresholds["min_prob"]:
            warning = f"Very low probability for {outcome}: {prob:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs[outcome] = thresholds["min_prob"]
        elif prob > thresholds["max_prob"]:
            warning = f"Very high probability for {outcome}: {prob:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs[outcome] = thresholds["max_prob"]
        else:
            validated_probs[outcome] = prob

    # Specific check for draw probabilities
    if "Draw" in validated_probs:
        if validated_probs["Draw"] < thresholds["min_draw"]:
            warning = f"Draw probability too low: {validated_probs['Draw']:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs["Draw"] = thresholds["min_draw"]
        elif validated_probs["Draw"] > thresholds["max_draw"]:
            warning = f"Draw probability too high: {validated_probs['Draw']:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs["Draw"] = thresholds["max_draw"]

    # Renormalize after adjustments
    total = sum(validated_probs.values())
    validated_probs = {k: v / total for k, v in validated_probs.items()}

    return validated_probs

def _validate_double_chance_probabilities(
    probs: Dict[str, float],
    warnings: List[str]
) -> Dict[str, float]:
    """Validate and adjust double chance probabilities."""
    validated_probs = {}

    # Check each probability
    for outcome, prob in probs.items():
        if prob < MIN_DOUBLE_CHANCE_PROB:
            warning = f"Low double chance probability for {outcome}: {prob:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs[outcome] = MIN_DOUBLE_CHANCE_PROB
        elif prob > MAX_DOUBLE_CHANCE_PROB:
            warning = f"High double chance probability for {outcome}: {prob:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs[outcome] = MAX_DOUBLE_CHANCE_PROB
        else:
            validated_probs[outcome] = prob

    # Normalize probabilities
    total = sum(validated_probs.values())
    validated_probs = {k: v / total for k, v in validated_probs.items()}

    return validated_probs

def _validate_over_under_probabilities(
    probs: Dict[str, float],
    warnings: List[str]
) -> Dict[str, float]:
    """Validate and adjust over/under probabilities."""
    validated_probs = {}

    # Ensure probabilities sum to 1 and are within bounds
    for outcome, prob in probs.items():
        if prob < MIN_PROBABILITY:
            warning = f"Low over/under probability for {outcome}: {prob:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs[outcome] = MIN_PROBABILITY
        elif prob > MAX_PROBABILITY:
            warning = f"High over/under probability for {outcome}: {prob:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs[outcome] = MAX_PROBABILITY
        else:
            validated_probs[outcome] = prob

    # Normalize probabilities
    total = sum(validated_probs.values())
    validated_probs = {k: v / total for k, v in validated_probs.items()}

    return validated_probs

def _validate_btts_probabilities(
    probs: Dict[str, float],
    warnings: List[str]
) -> Dict[str, float]:
    """Validate and adjust BTTS probabilities."""
    validated_probs = {}

    # Ensure probabilities sum to 1 and are within bounds
    for outcome, prob in probs.items():
        if prob < MIN_PROBABILITY:
            warning = f"Low BTTS probability for {outcome}: {prob:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs[outcome] = MIN_PROBABILITY
        elif prob > MAX_PROBABILITY:
            warning = f"High BTTS probability for {outcome}: {prob:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_probs[outcome] = MAX_PROBABILITY
        else:
            validated_probs[outcome] = prob

    # Normalize probabilities
    total = sum(validated_probs.values())
    validated_probs = {k: v / total for k, v in validated_probs.items()}

    return validated_probs

def _validate_correct_scores(
    correct_scores: Dict[str, float],
    warnings: List[str]
) -> Dict[str, float]:
    """Validate and adjust correct score probabilities."""
    logger.debug("Validating correct scores")
    validated_scores = {}

    # Validate individual probabilities
    for score, prob in correct_scores.items():
        if prob < 0:
            warning = f"Negative probability for score {score}: {prob:.3f}"
            warnings.append(warning)
            logger.warning(warning)
            validated_scores[score] = 0
        else:
            validated_scores[score] = prob

    # Check total probability
    total_prob = sum(validated_scores.values())
    if abs(1 - total_prob) > SCORE_PROB_TOLERANCE:
        warning = f"Correct score probabilities sum to {total_prob:.3f}"
        warnings.append(warning)
        logger.warning(warning)
        # Renormalize correct scores
        validated_scores = {k: v / total_prob for k, v in validated_scores.items()}
        logger.debug("Correct scores renormalized")

    return validated_scores

def _calibrate_probabilities(prob_dict: Dict[str, float]) -> Dict[str, float]:
    """Calibrate double chance probabilities."""
    # Get original probabilities
    home_or_draw = prob_dict.get("Home or Draw", 0)
    away_or_draw = prob_dict.get("Away or Draw", 0)
    home_or_away = prob_dict.get("Home or Away", 0)

    # Apply soft constraints while maintaining relative relationships
    total = home_or_draw + away_or_draw + home_or_away
    if total > 0:
        scale = len(DOUBLE_CHANCE_MARKET["outcomes"]) / total
        home_or_draw = home_or_draw * scale
        away_or_draw = away_or_draw * scale
        home_or_away = home_or_away * scale

    # Ensure each probability is within acceptable range
    home_or_draw = np.clip(home_or_draw, MIN_DOUBLE_CHANCE_PROB, MAX_DOUBLE_CHANCE_PROB)
    away_or_draw = np.clip(away_or_draw, MIN_DOUBLE_CHANCE_PROB, MAX_DOUBLE_CHANCE_PROB)
    home_or_away = np.clip(home_or_away, MIN_DOUBLE_CHANCE_PROB, MAX_DOUBLE_CHANCE_PROB)

    # Final normalization
    total = home_or_draw + away_or_draw + home_or_away
    if total > 0:
        return {
            "Home or Draw": home_or_draw / total,
            "Away or Draw": away_or_draw / total,
            "Home or Away": home_or_away / total,
        }
    else:
        return {
            "Home or Draw": 0.33,
            "Away or Draw": 0.33,
            "Home or Away": 0.34
        }
