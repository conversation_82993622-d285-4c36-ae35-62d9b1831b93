"""
Module for analyzing prediction confidence and generating summaries.

This module provides functions to analyze prediction confidence levels,
assess risks, and generate comprehensive summaries of predictions.
"""

import logging
import numpy as np
from typing import Dict, Any, Optional, List, Union, Tuple

from .constants import (
    CONFIDENCE_THRESHOLDS,
    STRONG_PROBABILITY_DIFF,
    MEDIUM_PROBABILITY_DIFF,
    BALANCED_PROBABILITY_THRESHOLD,
    RISK_SCORES,
    LOW_RISK_THRESHOLD,
    MEDIUM_RISK_THRESHOLD,
    MIN_PROBABILITY,
    MAX_PROBABILITY
)

logger = logging.getLogger(__name__)

def log_prediction_details(
    predictions: Dict[str, Dict[str, Union[str, Dict[str, float]]]],
    match_info: Dict[str, Any],
    confidence_analysis: Optional[Dict[str, Dict[str, Any]]] = None,
    risk_assessment: Optional[Dict[str, Dict[str, Any]]] = None
) -> None:
    """
    Log detailed prediction information for analysis and debugging purposes.
    
    Args:
        predictions: Dictionary containing all prediction types and their details
        match_info: Dictionary containing match metadata and context
        confidence_analysis: Optional dictionary containing confidence analysis results
        risk_assessment: Optional dictionary containing risk assessment results
    """
    try:
        logger.info(f"Prediction details for match: {match_info.get('home_team', 'Unknown')} vs {match_info.get('away_team', 'Unknown')}")
        
        # Log main predictions
        for pred_type, pred_info in predictions.items():
            logger.info(f"{pred_type.upper()} Prediction:")
            logger.info(f"  Predicted outcome: {pred_info.get('prediction', 'Unknown')}")
            logger.info(f"  Probabilities: {pred_info.get('probabilities', {})}")
        
        # Log confidence analysis if available
        if confidence_analysis:
            logger.info("Confidence Analysis:")
            for market, conf_info in confidence_analysis.items():
                logger.info(f"  {market}:")
                logger.info(f"    Confidence Level: {conf_info.get('confidence_level', 'Unknown')}")
                logger.info(f"    Max Probability: {conf_info.get('max_probability', 0.0):.3f}")
                logger.info(f"    Prediction Strength: {conf_info.get('prediction_strength', 'Unknown')}")
        
        # Log risk assessment if available
        if risk_assessment:
            logger.info("Risk Assessment:")
            for market, risk_info in risk_assessment.items():
                if market == "overall":
                    logger.info(f"  Overall Risk Level: {risk_info.get('risk_level', 'Unknown')}")
                    logger.info(f"  Risk Score: {risk_info.get('risk_score', 0.0):.3f}")
                else:
                    logger.info(f"  {market}:")
                    logger.info(f"    Risk Level: {risk_info.get('risk_level', 'Unknown')}")
                    if 'factors' in risk_info:
                        logger.info("    Risk Factors:")
                        for factor, value in risk_info['factors'].items():
                            logger.info(f"      {factor}: {value}")
    
    except Exception as e:
        logger.error(f"Error logging prediction details: {str(e)}")

def analyze_prediction_confidence(
    predictions: Dict[str, Dict[str, Union[str, Dict[str, float]]]],
    thresholds: Optional[Dict[str, float]] = None
) -> Dict[str, Dict[str, Any]]:
    """
    Analyze the confidence level of predictions based on probability distributions.
    """
    logger.debug("Starting prediction confidence analysis")
    thresholds = thresholds or CONFIDENCE_THRESHOLDS
    confidence_analysis = {}

    try:
        if not predictions or not isinstance(predictions, dict):
            logger.warning("No valid predictions provided for confidence analysis")
            return {}

        for pred_type, pred_info in predictions.items():
            if not isinstance(pred_info, dict) or "probabilities" not in pred_info:
                logger.warning(f"No probabilities found for {pred_type}")
                continue

            confidence_analysis[pred_type] = _analyze_single_prediction(
                pred_type, pred_info, thresholds
            )

        return confidence_analysis

    except Exception as e:
        logger.error(f"Error in confidence analysis: {str(e)}")
        return {}

def assess_prediction_risk(
    predictions: Dict[str, Dict[str, Union[str, Dict[str, float]]]],
    confidence_analysis: Dict[str, Dict[str, Any]]
) -> Dict[str, Dict[str, Any]]:
    """
    Assess the risk level of predictions based on various factors.
    """
    logger.debug("Starting prediction risk assessment")
    risk_assessment = {}

    try:
        if not predictions or not confidence_analysis:
            logger.warning("Missing predictions or confidence analysis for risk assessment")
            return {"overall": {"risk_level": "High", "risk_score": 1.0}}

        # Assess risks for each market type
        for market in predictions:
            if market in confidence_analysis:
                if market == "three_way":
                    risk_assessment[market] = _assess_three_way_risk(
                        predictions[market],
                        confidence_analysis[market]
                    )
                elif market.startswith("over_under"):
                    risk_assessment[market] = _assess_over_under_risk(
                        confidence_analysis[market],
                        predictions[market]
                    )
                elif market == "btts":
                    risk_assessment[market] = _assess_btts_risk(
                        confidence_analysis[market],
                        predictions[market]
                    )
                elif market == "double_chance":
                    risk_assessment[market] = _assess_double_chance_risk(
                        confidence_analysis[market],
                        predictions[market]
                    )

        # Calculate overall risk with weighted factors
        risk_assessment["overall"] = _calculate_overall_risk(
            risk_assessment,
            predictions,
            confidence_analysis
        )

        return risk_assessment

    except Exception as e:
        logger.error(f"Error in risk assessment: {str(e)}")
        return {"overall": {"risk_level": "High", "risk_score": 1.0}}

def _analyze_single_prediction(
    pred_type: str,
    pred_info: Dict[str, Any],
    thresholds: Dict[str, float]
) -> Dict[str, Any]:
    """Analyze a single prediction's confidence level."""
    try:
        probs = pred_info.get("probabilities", {})
        if not probs:
            return _get_default_confidence_result()

        max_prob = max(probs.values())
        logger.debug(f"Maximum probability for {pred_type}: {max_prob:.3f}")

        # Calculate probability difference based on market type
        prob_difference = _calculate_probability_difference(pred_type, probs)

        # Determine confidence level with market-specific adjustments
        confidence = _determine_confidence_level(
            max_prob, prob_difference, pred_type, thresholds
        )

        # Calculate prediction strength
        strength = _calculate_prediction_strength(
            prob_difference, max_prob, pred_type
        )

        return {
            "confidence_level": confidence,
            "max_probability": max_prob,
            "probability_difference": prob_difference,
            "prediction_strength": strength,
        }

    except Exception as e:
        logger.error(f"Error analyzing prediction for {pred_type}: {str(e)}")
        return _get_default_confidence_result()

def _get_default_confidence_result() -> Dict[str, Any]:
    """Get default confidence analysis result."""
    return {
        "confidence_level": "Very Low",
        "max_probability": 0.0,
        "probability_difference": 0.0,
        "prediction_strength": "Weak"
    }

def _calculate_probability_difference(
    pred_type: str,
    probs: Dict[str, float]
) -> float:
    """Calculate probability difference based on market type."""
    sorted_probs = sorted(probs.values(), reverse=True)
    
    if len(sorted_probs) < 2:
        return 0.0

    if pred_type == "three_way":
        return sorted_probs[0] - sorted_probs[1]
    elif pred_type.startswith("over_under") or pred_type == "btts":
        return abs(sorted_probs[0] - sorted_probs[1])
    elif pred_type == "double_chance":
        return sorted_probs[0] - sorted_probs[-1]
    
    return 0.0

def _determine_confidence_level(
    max_prob: float,
    prob_difference: float,
    pred_type: str,
    thresholds: Dict[str, float]
) -> str:
    """Determine confidence level with market-specific adjustments."""
    # Base confidence from probability
    base_confidence = (
        "High" if max_prob >= thresholds["high"]
        else "Medium" if max_prob >= thresholds["medium"]
        else "Low" if max_prob >= thresholds["low"]
        else "Very Low"
    )

    # Adjust based on market type and probability difference
    if pred_type == "three_way":
        if prob_difference < MEDIUM_PROBABILITY_DIFF:
            return _lower_confidence_level(base_confidence)
    elif pred_type.startswith("over_under"):
        if abs(max_prob - 0.5) < 0.1:
            return _lower_confidence_level(base_confidence)
    
    return base_confidence

def _lower_confidence_level(confidence: str) -> str:
    """Lower the confidence level by one step."""
    levels = ["Very Low", "Low", "Medium", "High"]
    current_index = levels.index(confidence)
    return levels[max(0, current_index - 1)]

def _calculate_prediction_strength(
    prob_difference: float,
    max_prob: float,
    pred_type: str
) -> str:
    """Calculate prediction strength based on market type."""
    if pred_type == "three_way":
        if prob_difference > STRONG_PROBABILITY_DIFF and max_prob > 0.5:
            return "Strong"
    elif pred_type.startswith("over_under"):
        if abs(max_prob - 0.5) > 0.2:
            return "Strong"
    elif pred_type == "btts":
        if abs(max_prob - 0.5) > 0.25:
            return "Strong"
    
    return "Weak"

def _assess_three_way_risk(
    prediction: Dict[str, Any],
    confidence: Dict[str, Any]
) -> Dict[str, Any]:
    """Assess risk for three-way prediction."""
    try:
        conf_level = confidence.get("confidence_level", "Low")
        max_prob = confidence.get("max_probability", 0.0)
        prob_diff = confidence.get("probability_difference", 0.0)
        
        # Calculate risk factors
        risk_factors = {
            "prediction_confidence": conf_level,
            "probability_margin": max_prob,
            "probability_difference": prob_diff,
            "prediction_balance": "Balanced" if prob_diff < 0.1 else "Clear Favorite"
        }
        
        # Determine risk level based on multiple factors
        if conf_level == "High" and prob_diff > 0.15:
            risk_level = "Low"
        elif conf_level == "Low" or prob_diff < 0.1:
            risk_level = "High"
        else:
            risk_level = "Medium"

        return {
            "risk_level": risk_level,
            "factors": risk_factors
        }

    except Exception as e:
        logger.error(f"Error assessing three-way risk: {str(e)}")
        return {
            "risk_level": "High",
            "factors": {
                "prediction_confidence": "Low",
                "probability_margin": 0.0,
                "probability_difference": 0.0,
                "prediction_balance": "Unknown"
            }
        }

def _assess_over_under_risk(
    confidence: Dict[str, Any],
    prediction: Dict[str, Any]
) -> Dict[str, Any]:
    """Assess risk for over/under prediction."""
    try:
        conf_level = confidence.get("confidence_level", "Low")
        max_prob = confidence.get("max_probability", 0.0)
        
        # Calculate risk level based on multiple factors
        risk_factors = {
            "prediction_confidence": conf_level,
            "probability_margin": max_prob,
            "probability_distribution": "Balanced" if abs(max_prob - 0.5) < 0.1 else "Skewed"
        }
        
        # Determine risk level
        if conf_level == "High" and max_prob > 0.7:
            risk_level = "Low"
        elif conf_level == "Low" or max_prob < 0.55:
            risk_level = "High"
        else:
            risk_level = "Medium"

        return {
            "risk_level": risk_level,
            "factors": risk_factors
        }

    except Exception as e:
        logger.error(f"Error assessing over/under risk: {str(e)}")
        return {
            "risk_level": "High",
            "factors": {
                "prediction_confidence": "Low",
                "probability_margin": 0.0,
                "probability_distribution": "Unknown"
            }
        }

def _assess_btts_risk(
    confidence: Dict[str, Any],
    prediction: Dict[str, Any]
) -> Dict[str, Any]:
    """Assess risk for BTTS prediction."""
    try:
        conf_level = confidence.get("confidence_level", "Low")
        max_prob = confidence.get("max_probability", 0.0)
        
        risk_factors = {
            "prediction_confidence": conf_level,
            "probability_margin": max_prob,
            "probability_distribution": "Balanced" if abs(max_prob - 0.5) < 0.15 else "Skewed"
        }
        
        if conf_level == "High" and max_prob > 0.65:
            risk_level = "Low"
        elif conf_level == "Low" or max_prob < 0.55:
            risk_level = "High"
        else:
            risk_level = "Medium"

        return {
            "risk_level": risk_level,
            "factors": risk_factors
        }

    except Exception as e:
        logger.error(f"Error assessing BTTS risk: {str(e)}")
        return {
            "risk_level": "High",
            "factors": {
                "prediction_confidence": "Low",
                "probability_margin": 0.0,
                "probability_distribution": "Unknown"
            }
        }

def _assess_double_chance_risk(
    confidence: Dict[str, Any],
    prediction: Dict[str, Any]
) -> Dict[str, Any]:
    """Assess risk for double chance prediction."""
    try:
        conf_level = confidence.get("confidence_level", "Low")
        max_prob = confidence.get("max_probability", 0.0)
        prob_diff = confidence.get("probability_difference", 0.0)
        
        risk_factors = {
            "prediction_confidence": conf_level,
            "probability_margin": max_prob,
            "probability_difference": prob_diff
        }
        
        if conf_level == "High" and prob_diff > 0.2:
            risk_level = "Low"
        elif conf_level == "Low" or prob_diff < 0.1:
            risk_level = "High"
        else:
            risk_level = "Medium"

        return {
            "risk_level": risk_level,
            "factors": risk_factors
        }

    except Exception as e:
        logger.error(f"Error assessing double chance risk: {str(e)}")
        return {
            "risk_level": "High",
            "factors": {
                "prediction_confidence": "Low",
                "probability_margin": 0.0,
                "probability_difference": 0.0
            }
        }

def _calculate_overall_risk(
    risk_assessment: Dict[str, Dict[str, Any]],
    predictions: Dict[str, Dict[str, Union[str, Dict[str, float]]]],
    confidence_analysis: Dict[str, Dict[str, Any]]
) -> Dict[str, Any]:
    """Calculate overall risk level with weighted factors."""
    try:
        # Define market weights
        market_weights = {
            "three_way": 1.5,
            "over_under_2_5": 1.2,
            "over_under_1_5": 1.0,
            "over_under_3_5": 1.0,
            "btts": 1.0,
            "double_chance": 0.8
        }

        weighted_scores = []
        for market, weight in market_weights.items():
            if market in risk_assessment:
                risk_level = risk_assessment[market]["risk_level"]
                risk_score = RISK_SCORES[risk_level]
                
                # Additional weight based on confidence
                if market in confidence_analysis:
                    conf_level = confidence_analysis[market]["confidence_level"]
                    conf_weight = 1.2 if conf_level == "High" else 1.0
                    weighted_scores.append(risk_score * weight * conf_weight)

        if not weighted_scores:
            return {
                "risk_level": "High",
                "risk_score": 1.0
            }

        average_risk = float(np.mean(weighted_scores))
        
        overall_risk = (
            "Low" if average_risk < LOW_RISK_THRESHOLD
            else "Medium" if average_risk < MEDIUM_RISK_THRESHOLD
            else "High"
        )

        return {
            "risk_level": overall_risk,
            "risk_score": average_risk
        }

    except Exception as e:
        logger.error(f"Error calculating overall risk: {str(e)}")
        return {
            "risk_level": "High",
            "risk_score": 1.0,
        }

def generate_prediction_summary(
    predictions: Dict[str, Dict[str, Union[str, Dict[str, float]]]],
    correct_scores: Dict[str, float],
    confidence_analysis: Dict[str, Dict[str, Any]],
    value_ratings: Optional[Dict[str, Dict[str, Any]]] = None,
    risk_assessment: Optional[Dict[str, Dict[str, Any]]] = None,
) -> Dict[str, Any]:
    """Generate a comprehensive summary of predictions."""
    logger.debug("Generating prediction summary")
    summary = {
        "main_predictions": {},
        "confidence_levels": {},
        "score_predictions": {},
        "value_analysis": {},
        "risk_assessment": {},
    }

    try:
        if predictions:
            summary["main_predictions"] = _prepare_main_predictions(predictions)
        if confidence_analysis:
            summary["confidence_levels"] = confidence_analysis
        if correct_scores:
            summary["score_predictions"] = _prepare_score_predictions(correct_scores)
        if value_ratings:
            summary["value_analysis"] = value_ratings
        if risk_assessment is None and predictions and confidence_analysis:
            risk_assessment = assess_prediction_risk(predictions, confidence_analysis)
        if risk_assessment:
            summary["risk_assessment"] = risk_assessment

        logger.debug("Prediction summary generated successfully")
        return summary

    except Exception as e:
        logger.error(f"Error generating prediction summary: {str(e)}")
        return {
            "main_predictions": {},
            "confidence_levels": {},
            "score_predictions": {},
            "value_analysis": {},
            "risk_assessment": {"overall": {"risk_level": "High", "risk_score": 1.0}},
        }

def _prepare_main_predictions(
    predictions: Dict[str, Dict[str, Union[str, Dict[str, float]]]]
) -> Dict[str, Dict[str, Any]]:
    """Prepare main predictions section of summary."""
    try:
        return {
            pred_type: {
                "prediction": pred_info.get("prediction", "Unknown"),
                "probabilities": pred_info.get("probabilities", {}),
            }
            for pred_type, pred_info in predictions.items()
            if isinstance(pred_info, dict)
        }
    except Exception as e:
        logger.error(f"Error preparing main predictions: {str(e)}")
        return {}

def _prepare_score_predictions(correct_scores: Dict[str, float]) -> Dict[str, Any]:
    """Prepare score predictions section of summary."""
    try:
        if not correct_scores:
            return {
                "most_likely_scores": {},
                "expected_total_goals": 0.0,
            }

        top_scores = sorted(
            correct_scores.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]

        expected_goals = sum(
            (float(score.split("-")[0]) + float(score.split("-")[1])) * prob
            for score, prob in correct_scores.items()
        )

        return {
            "most_likely_scores": dict(top_scores),
            "expected_total_goals": expected_goals,
        }

    except Exception as e:
        logger.error(f"Error preparing score predictions: {str(e)}")
        return {
            "most_likely_scores": {},
            "expected_total_goals": 0.0,
        }
