"""
Model training package for the betting project.
This package handles all model training, evaluation, and analysis functionality.
"""

from .core import train_model
from .neural_network import create_three_way_model, get_callbacks
from .feature_analysis import analyze_feature_importance, analyze_neural_network_importance
from .cross_validation import perform_cross_validation
from .visualization import (
    plot_confusion_matrix,
    plot_training_history,
    plot_class_distribution,
    plot_learning_curves,
    plot_calibration_curve
)

__all__ = [
    # Core functionality
    'train_model',
    
    # Neural network functionality
    'create_three_way_model',
    'get_callbacks',
    
    # Analysis functionality
    'analyze_feature_importance',
    'analyze_neural_network_importance',
    'perform_cross_validation',
    
    # Visualization functionality
    'plot_confusion_matrix',
    'plot_training_history',
    'plot_class_distribution',
    'plot_learning_curves',
    'plot_calibration_curve'
]
