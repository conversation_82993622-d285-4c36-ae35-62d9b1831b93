"""
Feature importance analysis functionality for different model types.
"""

import pandas as pd
import numpy as np
import logging
import seaborn as sns
import matplotlib.pyplot as plt
import os
import tensorflow as tf
from sklearn.inspection import permutation_importance
from sklearn.calibration import CalibratedClassifierCV
from typing import Optional, List, Dict, Any, Union
from utils import get_image_directory

logger = logging.getLogger(__name__)

def analyze_neural_network_importance(
    model: tf.keras.Sequential,
    X: pd.DataFrame,
    feature_names: List[str],
    pred_type: str,
) -> Optional[pd.DataFrame]:
    """
    Analyze feature importance for neural network models using permutation importance.

    Args:
        model: Trained neural network model
        X: Input features
        feature_names: List of feature names
        pred_type: Type of prediction being made

    Returns:
        DataFrame containing feature importances, or None if analysis fails
    """
    try:
        def scoring_fn(estimator: tf.keras.Sequential, X: np.ndarray, y: np.ndarray = None) -> float:
            """Custom scoring function that accepts the required arguments."""
            y_pred = estimator.predict(X, verbose=0)
            # For multi-class, use the max probability as a confidence score
            if len(y_pred.shape) > 1 and y_pred.shape[1] > 1:
                return np.mean(np.max(y_pred, axis=1))
            return np.mean(y_pred)

        # Create dummy y for permutation importance (required but not used in our scoring)
        dummy_y = np.zeros(len(X))

        results = permutation_importance(
            model,
            X,
            y=dummy_y,
            n_repeats=5,  # Reduced for faster computation
            random_state=42,
            scoring=scoring_fn,
        )

        importance = results.importances_mean
        feature_importance = pd.DataFrame(
            {"feature": feature_names, "importance": importance}
        ).sort_values("importance", ascending=False)

        _log_and_visualize_importance(feature_importance, pred_type, "Neural Network")
        return feature_importance

    except Exception as e:
        logger.error(f"Error in neural network feature importance analysis: {str(e)}")
        return None

def analyze_feature_importance(
    model: Union[CalibratedClassifierCV, Any],
    X: pd.DataFrame,
    y: np.ndarray,
    feature_names: List[str],
    pred_type: str,
    label_encoders: Dict
) -> Optional[pd.DataFrame]:
    """
    Analyze feature importance for tree-based models.

    Args:
        model: Trained model (e.g., RandomForestClassifier or CalibratedClassifierCV)
        X: Feature DataFrame for testing
        y: Encoded target variable for testing
        feature_names: List of feature names
        pred_type: Type of prediction
        label_encoders: Dictionary of label encoders

    Returns:
        DataFrame containing feature importances, or None if analysis fails
    """
    try:
        if isinstance(model, tf.keras.Sequential):
            return None

        # Handle CalibratedClassifierCV
        if isinstance(model, CalibratedClassifierCV):
            # Get the base estimator's feature importances
            # Note: In newer sklearn versions, use 'estimator' instead of 'base_estimator'
            base_estimator = getattr(model, 'estimator', getattr(model, 'base_estimator', None))
            if base_estimator is None:
                # Fallback: try to get from calibrated_classifiers_
                if hasattr(model, 'calibrated_classifiers_') and len(model.calibrated_classifiers_) > 0:
                    base_estimator = model.calibrated_classifiers_[0].estimator

            if base_estimator is not None and hasattr(base_estimator, 'feature_importances_'):
                importance = base_estimator.feature_importances_
            else:
                # If base estimator doesn't have feature_importances_, use permutation importance
                results = permutation_importance(
                    model,
                    X,
                    y,
                    n_repeats=5,  # Reduced for faster computation
                    random_state=42
                )
                importance = results.importances_mean
        else:
            # For other models that have feature_importances_ attribute
            if hasattr(model, 'feature_importances_'):
                importance = model.feature_importances_
            else:
                # Use permutation importance as fallback
                results = permutation_importance(
                    model,
                    X,
                    y,
                    n_repeats=5,  # Reduced for faster computation
                    random_state=42
                )
                importance = results.importances_mean

        feature_importance = pd.DataFrame(
            {"feature": feature_names, "importance": importance}
        ).sort_values("importance", ascending=False)

        _log_and_visualize_importance(feature_importance, pred_type)
        _analyze_zero_importance_features(feature_importance)
        
        return feature_importance

    except Exception as e:
        logger.error(f"Error in feature importance analysis: {str(e)}")
        return None

def _log_and_visualize_importance(
    feature_importance: pd.DataFrame,
    pred_type: str,
    model_type: str = "Tree-based"
) -> None:
    """Helper function to log and visualize feature importance."""
    try:
        logger.info(f"{model_type} Feature Importances:")
        logger.info(feature_importance.head(10))

        for feat, imp in feature_importance.values:
            logger.info(f"{feat}: {imp:.4f}")

        plt.figure(figsize=(12, 6))
        sns.barplot(x="importance", y="feature", data=feature_importance.head(20))
        plt.title(f"Feature Importance for {pred_type} ({model_type})")
        plt.tight_layout()
        
        # Ensure the image directory exists
        image_dir = get_image_directory()
        os.makedirs(image_dir, exist_ok=True)
        
        plt.savefig(
            os.path.join(image_dir, f"feature_importance_{pred_type}.png")
        )
        plt.close()
    except Exception as e:
        logger.error(f"Error in logging and visualizing importance: {str(e)}")

def _analyze_zero_importance_features(feature_importance: pd.DataFrame) -> None:
    """Helper function to analyze features with zero importance."""
    try:
        zero_importance = feature_importance[feature_importance["importance"] == 0]
        if not zero_importance.empty:
            logger.warning(
                f"Features with zero importance: {zero_importance['feature'].tolist()}"
            )
            logger.warning(
                "Consider removing these features or investigating why they have no importance."
            )
    except Exception as e:
        logger.error(f"Error analyzing zero importance features: {str(e)}")
