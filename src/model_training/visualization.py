"""
Visualization functionality for model analysis and evaluation.
"""

import matplotlib.pyplot as plt
import seaborn as sns
import os
import logging
from sklearn.metrics import confusion_matrix
import pandas as pd
from typing import List, Dict, Any
from utils import get_image_directory

logger = logging.getLogger(__name__)

def plot_confusion_matrix(
    y_true: List[str],
    y_pred: List[str],
    pred_type: str
) -> None:
    """
    Create and save confusion matrix visualization.

    Args:
        y_true: True labels
        y_pred: Predicted labels
        pred_type: Type of prediction (e.g., 'three_way', 'over_under_2_5')
    """
    try:
        cm = confusion_matrix(y_true, y_pred)
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt="d", cmap="Blues")
        plt.title(f"Confusion Matrix - {pred_type}")
        plt.ylabel("True Label")
        plt.xlabel("Predicted Label")
        plt.savefig(
            os.path.join(get_image_directory(), f"confusion_matrix_{pred_type}.png")
        )
        plt.close()
    except Exception as e:
        logger.error(f"Error creating confusion matrix plot: {str(e)}")

def plot_feature_importance(
    feature_importance: pd.DataFrame,
    pred_type: str,
    model_type: str = "Model"
) -> None:
    """
    Create and save feature importance visualization.

    Args:
        feature_importance: DataFrame containing feature names and importance scores
        pred_type: Type of prediction
        model_type: Type of model (e.g., 'Neural Network', 'Random Forest')
    """
    try:
        plt.figure(figsize=(12, 6))
        sns.barplot(x="importance", y="feature", data=feature_importance.head(20))
        plt.title(f"Feature Importance for {pred_type} ({model_type})")
        plt.tight_layout()
        plt.savefig(
            os.path.join(get_image_directory(), f"feature_importance_{pred_type}.png")
        )
        plt.close()
    except Exception as e:
        logger.error(f"Error creating feature importance plot: {str(e)}")

def plot_training_history(
    history: Dict[str, List[float]],
    pred_type: str
) -> None:
    """
    Plot training history for neural network models.

    Args:
        history: Dictionary containing training metrics
        pred_type: Type of prediction
    """
    try:
        plt.figure(figsize=(12, 4))

        # Plot training & validation accuracy
        plt.subplot(1, 2, 1)
        plt.plot(history['accuracy'], label='Training Accuracy')
        plt.plot(history['val_accuracy'], label='Validation Accuracy')
        plt.title('Model Accuracy')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy')
        plt.legend()

        # Plot training & validation loss
        plt.subplot(1, 2, 2)
        plt.plot(history['loss'], label='Training Loss')
        plt.plot(history['val_loss'], label='Validation Loss')
        plt.title('Model Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()

        plt.tight_layout()
        plt.savefig(
            os.path.join(get_image_directory(), f"training_history_{pred_type}.png")
        )
        plt.close()
    except Exception as e:
        logger.error(f"Error creating training history plot: {str(e)}")

def plot_class_distribution(
    y: pd.Series,
    pred_type: str,
    title_suffix: str = "Training Data"
) -> None:
    """
    Plot class distribution for a prediction type.

    Args:
        y: Series containing class labels
        pred_type: Type of prediction
        title_suffix: Suffix for plot title (e.g., 'Training Data', 'Test Data')
    """
    try:
        plt.figure(figsize=(10, 6))
        class_dist = y.value_counts(normalize=True)
        sns.barplot(x=class_dist.index, y=class_dist.values)
        plt.title(f"Class Distribution - {pred_type} ({title_suffix})")
        plt.xlabel("Class")
        plt.ylabel("Proportion")
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(
            os.path.join(get_image_directory(), f"class_distribution_{pred_type}_{title_suffix.lower().replace(' ', '_')}.png")
        )
        plt.close()
    except Exception as e:
        logger.error(f"Error creating class distribution plot: {str(e)}")

def plot_learning_curves(
    train_scores: List[float],
    val_scores: List[float],
    train_sizes: List[int],
    pred_type: str
) -> None:
    """
    Plot learning curves showing model performance vs training size.

    Args:
        train_scores: List of training scores
        val_scores: List of validation scores
        train_sizes: List of training set sizes
        pred_type: Type of prediction
    """
    try:
        plt.figure(figsize=(10, 6))
        plt.plot(train_sizes, train_scores, label='Training Score')
        plt.plot(train_sizes, val_scores, label='Cross-validation Score')
        plt.xlabel('Training Examples')
        plt.ylabel('Score')
        plt.title(f'Learning Curves - {pred_type}')
        plt.legend(loc='best')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(
            os.path.join(get_image_directory(), f"learning_curves_{pred_type}.png")
        )
        plt.close()
    except Exception as e:
        logger.error(f"Error creating learning curves plot: {str(e)}")

def plot_calibration_curve(
    prob_true: List[float],
    prob_pred: List[float],
    pred_type: str
) -> None:
    """
    Plot calibration curve showing predicted vs actual probabilities.

    Args:
        prob_true: True probabilities
        prob_pred: Predicted probabilities
        pred_type: Type of prediction
    """
    try:
        plt.figure(figsize=(10, 6))
        plt.plot([0, 1], [0, 1], 'k--', label='Perfectly Calibrated')
        plt.plot(prob_pred, prob_true, label='Model')
        plt.xlabel('Mean Predicted Probability')
        plt.ylabel('True Probability')
        plt.title(f'Calibration Curve - {pred_type}')
        plt.legend(loc='best')
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(
            os.path.join(get_image_directory(), f"calibration_curve_{pred_type}.png")
        )
        plt.close()
    except Exception as e:
        logger.error(f"Error creating calibration curve plot: {str(e)}")
