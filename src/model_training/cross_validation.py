"""
Cross-validation functionality for model evaluation.
"""

import numpy as np
import pandas as pd
import logging
import tensorflow as tf
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score
from imblearn.over_sampling import SMOT<PERSON>
from typing import Optional, Dict, Any
from .constants import (
    N_SPLITS,
    SMOTE_K_NEIGHBORS,
    MIN_SAMPLES_FOR_SMOTE,
    RF_N_ESTIMATORS,
    RF_MAX_DEPTH,
    RF_MIN_SAMPLES_SPLIT,
    RF_MIN_SAMPLES_LEAF,
    STRING_COLUMNS
)
from .neural_network import get_callbacks

logger = logging.getLogger(__name__)

def perform_cross_validation(
    model: Any,
    X: pd.DataFrame,
    y: pd.Series,
    n_splits: int = N_SPLITS,
    pred_type: Optional[str] = None,
    label_encoders: Optional[Dict] = None
) -> None:
    """
    Perform time series cross-validation.

    Args:
        model: Trained model
        X: Feature DataFrame
        y: Target variable
        n_splits: Number of splits for cross-validation
        pred_type: Type of prediction being made
        label_encoders: Dictionary of label encoders for each prediction type
    """
    logger.info(f"Performing {n_splits}-fold time series cross-validation")

    # Remove string columns
    numeric_columns = [col for col in X.columns if col not in STRING_COLUMNS]
    X_numeric = X[numeric_columns]

    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X_numeric)
    X_scaled = pd.DataFrame(X_scaled, columns=numeric_columns)

    tscv = TimeSeriesSplit(n_splits=n_splits)
    scores = []

    if isinstance(model, tf.keras.Sequential):
        _perform_neural_network_cv(model, X_scaled, y, tscv, scores)
    else:
        _perform_tree_based_cv(model, X_scaled, y, tscv, scores, pred_type)

    logger.info(f"\nCross-validation scores: {scores}")
    logger.info(f"Mean CV score: {np.mean(scores):.4f} (+/- {np.std(scores) * 2:.4f})")

def _perform_neural_network_cv(
    model: tf.keras.Sequential,
    X_scaled: pd.DataFrame,
    y: pd.Series,
    tscv: TimeSeriesSplit,
    scores: list
) -> None:
    """Handle cross-validation for neural network models."""
    num_classes = model.layers[-1].get_config()["units"]
    encoder = label_encoders.get(pred_type, LabelEncoder())
    y_encoded = encoder.fit_transform(y)
    y_cat = tf.keras.utils.to_categorical(y_encoded, num_classes=num_classes)

    for train_index, val_index in tscv.split(X_scaled):
        X_train, X_val = X_scaled.iloc[train_index], X_scaled.iloc[val_index]
        y_train, y_val = y_cat[train_index], y_cat[val_index]

        fold_model = tf.keras.models.clone_model(model)
        fold_model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.0005),
            loss="categorical_crossentropy",
            metrics=["accuracy"],
        )

        # Calculate class weights
        y_train_classes = np.argmax(y_train, axis=1)
        class_weights = _calculate_class_weights(y_train_classes, encoder)

        # Train with early stopping and learning rate reduction
        fold_model.fit(
            X_train,
            y_train,
            epochs=200,
            batch_size=32,
            validation_data=(X_val, y_val),
            class_weight=class_weights,
            callbacks=get_callbacks(),
            verbose=0,
        )

        # Evaluate
        y_pred = np.argmax(fold_model.predict(X_val), axis=1)
        score = accuracy_score(np.argmax(y_val, axis=1), y_pred)
        scores.append(score)

def _perform_tree_based_cv(
    model: Any,
    X_scaled: pd.DataFrame,
    y: pd.Series,
    tscv: TimeSeriesSplit,
    scores: list,
    pred_type: str
) -> None:
    """Handle cross-validation for tree-based models."""
    for train_index, val_index in tscv.split(X_scaled):
        X_train, X_val = X_scaled.iloc[train_index], X_scaled.iloc[val_index]
        y_train, y_val = y.iloc[train_index], y.iloc[val_index]

        # Calculate class weights
        class_weights = _calculate_prediction_weights(y_train, pred_type)

        # Create new model with calculated weights
        new_model = RandomForestClassifier(
            n_estimators=RF_N_ESTIMATORS,
            max_depth=RF_MAX_DEPTH,
            min_samples_split=RF_MIN_SAMPLES_SPLIT,
            min_samples_leaf=RF_MIN_SAMPLES_LEAF,
            class_weight=class_weights,
            max_features="sqrt",
            random_state=42,
            bootstrap=True,
            oob_score=True,
        )

        _fit_model_with_smote(new_model, X_train, y_train)
        
        y_pred = new_model.predict(X_val)
        score = accuracy_score(y_val, y_pred)
        scores.append(score)

        _log_fold_results(y_train, class_weights, score)

def _calculate_class_weights(
    y_train_classes: np.ndarray,
    encoder: Any
) -> Dict:
    """Calculate class weights for neural network training."""
    class_counts = np.bincount(y_train_classes)
    total_samples = len(y_train_classes)
    class_weights = {
        i: total_samples / (len(class_counts) * count)
        for i, count in enumerate(class_counts)
    }

    if "Draw" in encoder.classes_:
        draw_idx = np.where(encoder.classes_ == "Draw")[0][0]
        class_weights[draw_idx] *= 2.5

    return class_weights

def _calculate_prediction_weights(
    y_train: pd.Series,
    pred_type: str
) -> Dict:
    """Calculate class weights based on prediction type."""
    class_counts = y_train.value_counts()
    total_samples = len(y_train)
    base_weights = {
        class_label: total_samples / (len(class_counts) * count)
        for class_label, count in class_counts.items()
    }

    if pred_type.startswith("over_under"):
        if "Under" in base_weights:
            base_weights["Under"] *= 2.0
    elif pred_type == "btts":
        if "No" in base_weights:
            base_weights["No"] *= 1.5

    return base_weights

def _fit_model_with_smote(
    model: Any,
    X_train: pd.DataFrame,
    y_train: pd.Series
) -> None:
    """Fit model with SMOTE if applicable."""
    class_counts = y_train.value_counts()
    min_samples = min(class_counts)
    
    if len(np.unique(y_train)) > 1 and min_samples >= MIN_SAMPLES_FOR_SMOTE:
        try:
            k_neighbors = min(SMOTE_K_NEIGHBORS, min_samples - 1)
            smote = SMOTE(
                random_state=42,
                sampling_strategy="auto",
                k_neighbors=k_neighbors,
            )
            X_train_resampled, y_train_resampled = smote.fit_resample(X_train, y_train)
            model.fit(X_train_resampled, y_train_resampled)
        except Exception as e:
            logger.warning(f"SMOTE failed, falling back to original data: {str(e)}")
            model.fit(X_train, y_train)
    else:
        model.fit(X_train, y_train)

def _log_fold_results(
    y_train: pd.Series,
    class_weights: Dict,
    score: float
) -> None:
    """Log the results of each fold."""
    logger.info(f"\nFold Results:")
    logger.info(f"Class distribution in training: {y_train.value_counts(normalize=True)}")
    logger.info(f"Applied class weights: {class_weights}")
    logger.info(f"Fold accuracy: {score:.4f}")
