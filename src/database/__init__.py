"""
Database Module

This module provides database functionality for the sports betting project,
including connection management, data migration, and query utilities.
"""

from .config import (
    DB_PATH,
    SQLITE_SETTINGS,
    TABLES,
    get_sqlite_connection_string,
    ensure_directories,
    get_database_info
)

from .utils import (
    DatabaseManager,
    QueryBuilder,
    query_to_dataframe,
    get_available_leagues,
    get_available_seasons,
    db
)

__all__ = [
    # Configuration
    'DB_PATH',
    'SQLITE_SETTINGS', 
    'TABLES',
    'get_sqlite_connection_string',
    'ensure_directories',
    'get_database_info',
    
    # Utils
    'DatabaseManager',
    'QueryBuilder',
    'query_to_dataframe',
    'get_available_leagues',
    'get_available_seasons',
    'db'
]
