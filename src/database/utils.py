"""
Database Utilities

This module provides utility functions for database operations,
including connection management, queries, and maintenance tasks.
"""

import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Union
from contextlib import contextmanager
from datetime import datetime
import os
import shutil

from .config import (
    SQLITE_SETTINGS,
    TABLES,
    get_sqlite_connection_string,
    ensure_directories,
    DB_BACKUP_DIR,
    DB_PATH
)

logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    Database manager for handling connections and operations
    """
    
    def __init__(self):
        ensure_directories()
        self.db_path = get_sqlite_connection_string()
    
    @contextmanager
    def get_connection(self):
        """
        Context manager for database connections with optimized settings
        """
        conn = None
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=SQLITE_SETTINGS['timeout'],
                check_same_thread=SQLITE_SETTINGS['check_same_thread']
            )
            
            # Apply SQLite optimizations
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA journal_mode = {SQLITE_SETTINGS['journal_mode']}")
            cursor.execute(f"PRAGMA synchronous = {SQLITE_SETTINGS['synchronous']}")
            cursor.execute(f"PRAGMA cache_size = {SQLITE_SETTINGS['cache_size']}")
            cursor.execute(f"PRAGMA temp_store = {SQLITE_SETTINGS['temp_store']}")
            
            # Enable foreign keys
            cursor.execute("PRAGMA foreign_keys = ON")
            
            yield conn
            conn.commit()
            
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[tuple]:
        """
        Execute a SELECT query and return results
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
    
    def execute_script(self, script: str) -> None:
        """
        Execute a SQL script
        """
        with self.get_connection() as conn:
            conn.executescript(script)
    
    def get_table_info(self, table_name: str) -> List[Dict]:
        """
        Get information about a table's structure
        """
        query = f"PRAGMA table_info({table_name})"
        results = self.execute_query(query)
        
        columns = []
        for row in results:
            columns.append({
                'column_id': row[0],
                'name': row[1],
                'type': row[2],
                'not_null': bool(row[3]),
                'default_value': row[4],
                'primary_key': bool(row[5])
            })
        return columns
    
    def get_all_tables(self) -> List[str]:
        """
        Get list of all tables in the database
        """
        query = "SELECT name FROM sqlite_master WHERE type='table'"
        results = self.execute_query(query)
        return [row[0] for row in results]
    
    def get_table_count(self, table_name: str) -> int:
        """
        Get record count for a table
        """
        query = f"SELECT COUNT(*) FROM {table_name}"
        result = self.execute_query(query)
        return result[0][0] if result else 0
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive database statistics
        """
        stats = {
            'database_path': self.db_path,
            'database_exists': os.path.exists(self.db_path),
            'database_size': 0,
            'tables': {},
            'total_records': 0
        }
        
        if not stats['database_exists']:
            return stats
        
        # Get database file size
        stats['database_size'] = os.path.getsize(self.db_path)
        
        # Get table statistics
        tables = self.get_all_tables()
        for table in tables:
            count = self.get_table_count(table)
            stats['tables'][table] = count
            stats['total_records'] += count
        
        return stats
    
    def backup_database(self, backup_name: Optional[str] = None) -> str:
        """
        Create a backup of the database
        """
        if not os.path.exists(self.db_path):
            raise FileNotFoundError("Database file does not exist")
        
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"sports_data_backup_{timestamp}.db"
        
        backup_path = DB_BACKUP_DIR / backup_name
        shutil.copy2(self.db_path, backup_path)
        
        logger.info(f"Database backed up to: {backup_path}")
        return str(backup_path)
    
    def restore_database(self, backup_path: str) -> None:
        """
        Restore database from backup
        """
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Backup file does not exist: {backup_path}")
        
        # Create a backup of current database before restoring
        if os.path.exists(self.db_path):
            current_backup = self.backup_database("pre_restore_backup.db")
            logger.info(f"Current database backed up to: {current_backup}")
        
        shutil.copy2(backup_path, self.db_path)
        logger.info(f"Database restored from: {backup_path}")
    
    def vacuum_database(self) -> None:
        """
        Optimize database by reclaiming unused space
        """
        with self.get_connection() as conn:
            conn.execute("VACUUM")
        logger.info("Database vacuumed successfully")
    
    def analyze_database(self) -> None:
        """
        Update statistics for query optimization
        """
        with self.get_connection() as conn:
            conn.execute("ANALYZE")
        logger.info("Database analysis completed")

class QueryBuilder:
    """
    Helper class for building common queries
    """
    
    @staticmethod
    def get_league_results(league_name: str, season: Optional[str] = None) -> str:
        """
        Build query for league match results
        """
        query = f"""
        SELECT match_date, home_team, away_team, home_goals, away_goals, score
        FROM {TABLES['MATCH_RESULTS']}
        WHERE league_name = ?
        """
        if season:
            query += " AND season = ?"
        query += " ORDER BY match_date DESC"
        return query
    
    @staticmethod
    def get_team_stats(league_name: str, team_name: str, season: Optional[str] = None) -> str:
        """
        Build query for team statistics
        """
        query = f"""
        SELECT *
        FROM {TABLES['TEAM_DETAILED_STATS']}
        WHERE league_name = ? AND team_name = ?
        """
        if season:
            query += " AND season = ?"
        return query
    
    @staticmethod
    def get_head_to_head(league_name: str, team_a: str, team_b: str) -> str:
        """
        Build query for head-to-head statistics
        """
        return f"""
        SELECT *
        FROM {TABLES['HEAD_TO_HEAD_STATS']}
        WHERE league_name = ? 
        AND ((team_a = ? AND team_b = ?) OR (team_a = ? AND team_b = ?))
        """
    
    @staticmethod
    def get_league_table(league_name: str, season: Optional[str] = None) -> str:
        """
        Build query for league table
        """
        query = f"""
        SELECT position, team_name, matches_played, wins, draws, losses,
               goals_for, goals_against, goal_difference, points
        FROM {TABLES['LEAGUE_TABLES']}
        WHERE league_name = ?
        """
        if season:
            query += " AND season = ?"
        query += " ORDER BY position ASC"
        return query

def query_to_dataframe(db_manager: DatabaseManager, query: str, params: Optional[tuple] = None) -> pd.DataFrame:
    """
    Execute query and return results as pandas DataFrame
    """
    with db_manager.get_connection() as conn:
        return pd.read_sql_query(query, conn, params=params)

def get_available_leagues(db_manager: DatabaseManager) -> List[str]:
    """
    Get list of all available leagues in the database
    """
    query = f"SELECT DISTINCT league_name FROM {TABLES['MATCH_RESULTS']} ORDER BY league_name"
    results = db_manager.execute_query(query)
    return [row[0] for row in results]

def get_available_seasons(db_manager: DatabaseManager, league_name: Optional[str] = None) -> List[str]:
    """
    Get list of all available seasons, optionally filtered by league
    """
    query = f"SELECT DISTINCT season FROM {TABLES['MATCH_RESULTS']}"
    params = None
    
    if league_name:
        query += " WHERE league_name = ?"
        params = (league_name,)
    
    query += " ORDER BY season DESC"
    results = db_manager.execute_query(query, params)
    return [row[0] for row in results if row[0]]  # Filter out None values

# Convenience instance
db = DatabaseManager()
