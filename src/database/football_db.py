#!/usr/bin/env python3
"""
Football Database Utility Module

This module provides easy access to the centralized SQLite database,
replacing the CSV file system with efficient database operations.
"""

import sqlite3
import pandas as pd
import os
from typing import List, Dict, Optional, Tuple
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class FootballDatabase:
    """Main database interface for football betting data."""
    
    def __init__(self, db_path='data/football_betting.db'):
        self.db_path = db_path
        self.conn = None
        self._team_mapping_cache = {}  # Cache for team name mappings
        
    def connect(self):
        """Create database connection."""
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"Database not found: {self.db_path}. Run create_database.py first.")
        
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row  # Enable column access by name
        
    def disconnect(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
    
    def execute_query(self, query: str, params: tuple = ()) -> pd.DataFrame:
        """Execute query and return results as DataFrame."""
        if not self.conn:
            self.connect()
        
        return pd.read_sql_query(query, self.conn, params=params)
    
    def execute_scalar(self, query: str, params: tuple = ()):
        """Execute query and return single value."""
        if not self.conn:
            self.connect()

        cursor = self.conn.execute(query, params)
        result = cursor.fetchone()
        return result[0] if result else None

    def execute_statement(self, statement: str, params: tuple = ()):
        """Execute a non-query statement (CREATE, INSERT, UPDATE, DELETE)."""
        if not self.conn:
            self.connect()

        cursor = self.conn.execute(statement, params)
        self.conn.commit()
        return cursor.rowcount
    
    # League-related methods
    def get_leagues(self) -> pd.DataFrame:
        """Get all leagues."""
        return self.execute_query("SELECT * FROM leagues ORDER BY league_name")
    
    def get_league_id(self, league_name: str) -> Optional[int]:
        """Get league ID by name."""
        return self.execute_scalar(
            "SELECT league_id FROM leagues WHERE league_name = ?",
            (league_name,)
        )
    
    def get_league_stats(self, league_name: str) -> pd.DataFrame:
        """Get statistics for a specific league."""
        return self.execute_query('''
            SELECT ls.stat_name, ls.stat_value
            FROM league_stats ls
            JOIN leagues l ON ls.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY ls.stat_name
        ''', (league_name,))
    
    # Team-related methods
    def get_teams(self, league_name: str = None) -> pd.DataFrame:
        """Get teams, optionally filtered by league."""
        if league_name:
            return self.execute_query('''
                SELECT DISTINCT t.team_id, t.team_name, l.league_name
                FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE l.league_name = ?
                ORDER BY t.team_name
            ''', (league_name,))
        else:
            return self.execute_query('''
                SELECT DISTINCT t.team_id, t.team_name, l.league_name
                FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                ORDER BY l.league_name, t.team_name
            ''')
    
    def get_team_stats(self, team_name: str, league_name: str = None) -> pd.DataFrame:
        """Get statistics for a specific team using automated team name mapping."""
        if league_name:
            # Try to get canonical team name from mapping table
            canonical_name = self.get_canonical_team_name(team_name, league_name)

            # Search using canonical name
            result = self.execute_query('''
                SELECT ts.*, t.team_name, l.league_name
                FROM team_stats ts
                JOIN teams t ON ts.team_id = t.team_id
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            ''', (canonical_name, league_name))

            if not result.empty:
                return result

            # Fallback: try all mapped variations for this team
            mapped_variations = self.execute_query('''
                SELECT DISTINCT variation_name
                FROM team_name_mappings tnm
                JOIN leagues l ON tnm.league_id = l.league_id
                WHERE l.league_name = ? AND tnm.canonical_name = ?
            ''', (league_name, canonical_name))

            if not mapped_variations.empty:
                for variation in mapped_variations['variation_name']:
                    result = self.execute_query('''
                        SELECT ts.*, t.team_name, l.league_name
                        FROM team_stats ts
                        JOIN teams t ON ts.team_id = t.team_id
                        JOIN leagues l ON t.league_id = l.league_id
                        WHERE t.team_name = ? AND l.league_name = ?
                    ''', (variation, league_name))

                    if not result.empty:
                        return result

        # Final fallback: exact match without league constraint
        result = self.execute_query('''
            SELECT ts.*, t.team_name, l.league_name
            FROM team_stats ts
            JOIN teams t ON ts.team_id = t.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE t.team_name = ?
        ''', (team_name,))

        return result
    
    def get_league_table(self, league_name: str) -> pd.DataFrame:
        """Get league table/standings for a specific league."""
        return self.execute_query('''
            SELECT
                lt.position,
                t.team_name,
                lt.matches_played as MP,
                lt.wins as W,
                lt.draws as D,
                lt.losses as L,
                lt.goals_for as GF,
                lt.goals_against as GA,
                lt.goal_difference as GD,
                lt.points as Pts
            FROM league_table lt
            JOIN teams t ON lt.team_id = t.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY lt.position
        ''', (league_name,))
    
    # Match-related methods
    def get_match_results(self, league_name: str, limit: int = None) -> pd.DataFrame:
        """Get match results for a specific league."""
        query = '''
            SELECT
                mr.match_id,
                mr.match_date,
                ht.team_name as home_team,
                mr.home_score,
                mr.away_score,
                at.team_name as away_team,
                mr.result_code
            FROM match_results mr
            JOIN teams ht ON mr.home_team_id = ht.team_id
            JOIN teams at ON mr.away_team_id = at.team_id
            JOIN leagues l ON mr.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY mr.match_id DESC
        '''

        if limit:
            query += f" LIMIT {limit}"

        return self.execute_query(query, (league_name,))
    
    def get_team_matches(self, team_name: str, league_name: str = None, limit: int = None) -> pd.DataFrame:
        """Get matches for a specific team with comprehensive fuzzy name matching."""
        if league_name:
            # Get all team names that actually appear in match results for this league
            match_teams_df = self.execute_query('''
                SELECT DISTINCT team_name FROM (
                    SELECT ht.team_name
                    FROM match_results mr
                    JOIN teams ht ON mr.home_team_id = ht.team_id
                    JOIN leagues l ON mr.league_id = l.league_id
                    WHERE l.league_name = ?
                    UNION
                    SELECT at.team_name
                    FROM match_results mr
                    JOIN teams at ON mr.away_team_id = at.team_id
                    JOIN leagues l ON mr.league_id = l.league_id
                    WHERE l.league_name = ?
                )
            ''', (league_name, league_name))

            if not match_teams_df.empty:
                candidate_names = match_teams_df['team_name'].tolist()
                best_match = find_best_team_match(team_name, candidate_names)

                query = '''
                    SELECT
                        mr.match_date,
                        ht.team_name as home_team,
                        mr.home_score,
                        mr.away_score,
                        at.team_name as away_team,
                        mr.result_code,
                        CASE
                            WHEN ht.team_name = ? THEN 'Home'
                            WHEN at.team_name = ? THEN 'Away'
                        END as venue
                    FROM match_results mr
                    JOIN teams ht ON mr.home_team_id = ht.team_id
                    JOIN teams at ON mr.away_team_id = at.team_id
                    JOIN leagues l ON mr.league_id = l.league_id
                    WHERE (ht.team_name = ? OR at.team_name = ?) AND l.league_name = ?
                    ORDER BY mr.match_date DESC
                '''

                params = [best_match, best_match, best_match, best_match, league_name]

                if limit:
                    query += f" LIMIT {limit}"

                return self.execute_query(query, tuple(params))

        # Fallback: try fuzzy matching across all match results without league constraint
        all_match_teams_df = self.execute_query('''
            SELECT DISTINCT team_name FROM (
                SELECT ht.team_name
                FROM match_results mr
                JOIN teams ht ON mr.home_team_id = ht.team_id
                UNION
                SELECT at.team_name
                FROM match_results mr
                JOIN teams at ON mr.away_team_id = at.team_id
            )
        ''')

        if not all_match_teams_df.empty:
            candidate_names = all_match_teams_df['team_name'].tolist()
            best_match = find_best_team_match(team_name, candidate_names)

            query = '''
                SELECT
                    mr.match_date,
                    ht.team_name as home_team,
                    mr.home_score,
                    mr.away_score,
                    at.team_name as away_team,
                    mr.result_code,
                    CASE
                        WHEN ht.team_name = ? THEN 'Home'
                        WHEN at.team_name = ? THEN 'Away'
                    END as venue
                FROM match_results mr
                JOIN teams ht ON mr.home_team_id = ht.team_id
                JOIN teams at ON mr.away_team_id = at.team_id
                JOIN leagues l ON mr.league_id = l.league_id
                WHERE (ht.team_name = ? OR at.team_name = ?)
                ORDER BY mr.match_date DESC
            '''

            params = [best_match, best_match, best_match, best_match]

            if limit:
                query += f" LIMIT {limit}"

            return self.execute_query(query, tuple(params))

        return pd.DataFrame()
    
    # Head-to-head methods
    def get_head_to_head_stats(self, team1: str, team2: str, league_name: str = None) -> pd.DataFrame:
        """Get head-to-head statistics between two teams with comprehensive fuzzy name matching."""
        if league_name:
            # Get all team names that actually appear in H2H stats for this league
            h2h_teams_df = self.execute_query('''
                SELECT DISTINCT team_name FROM (
                    SELECT ht.team_name
                    FROM head_to_head_stats h2h
                    JOIN teams ht ON h2h.home_team_id = ht.team_id
                    JOIN leagues l ON h2h.league_id = l.league_id
                    WHERE l.league_name = ?
                    UNION
                    SELECT at.team_name
                    FROM head_to_head_stats h2h
                    JOIN teams at ON h2h.away_team_id = at.team_id
                    JOIN leagues l ON h2h.league_id = l.league_id
                    WHERE l.league_name = ?
                )
            ''', (league_name, league_name))

            if not h2h_teams_df.empty:
                candidate_names = h2h_teams_df['team_name'].tolist()
                best_match1 = find_best_team_match(team1, candidate_names)
                best_match2 = find_best_team_match(team2, candidate_names)

                query = '''
                    SELECT
                        h2h.*,
                        ht.team_name as home_team,
                        at.team_name as away_team,
                        l.league_name
                    FROM head_to_head_stats h2h
                    JOIN teams ht ON h2h.home_team_id = ht.team_id
                    JOIN teams at ON h2h.away_team_id = at.team_id
                    JOIN leagues l ON h2h.league_id = l.league_id
                    WHERE ((ht.team_name = ? AND at.team_name = ?) OR
                           (ht.team_name = ? AND at.team_name = ?))
                      AND l.league_name = ?
                '''

                return self.execute_query(query, (best_match1, best_match2, best_match2, best_match1, league_name))

        # Fallback: try fuzzy matching across all H2H data without league constraint
        all_h2h_teams_df = self.execute_query('''
            SELECT DISTINCT team_name FROM (
                SELECT ht.team_name
                FROM head_to_head_stats h2h
                JOIN teams ht ON h2h.home_team_id = ht.team_id
                UNION
                SELECT at.team_name
                FROM head_to_head_stats h2h
                JOIN teams at ON h2h.away_team_id = at.team_id
            )
        ''')

        if not all_h2h_teams_df.empty:
            candidate_names = all_h2h_teams_df['team_name'].tolist()
            best_match1 = find_best_team_match(team1, candidate_names)
            best_match2 = find_best_team_match(team2, candidate_names)

            query = '''
                SELECT
                    h2h.*,
                    ht.team_name as home_team,
                    at.team_name as away_team,
                    l.league_name
                FROM head_to_head_stats h2h
                JOIN teams ht ON h2h.home_team_id = ht.team_id
                JOIN teams at ON h2h.away_team_id = at.team_id
                JOIN leagues l ON h2h.league_id = l.league_id
                WHERE ((ht.team_name = ? AND at.team_name = ?) OR
                       (ht.team_name = ? AND at.team_name = ?))
            '''

            return self.execute_query(query, (best_match1, best_match2, best_match2, best_match1))

        return pd.DataFrame()
    
    def get_all_h2h_stats(self, league_name: str) -> pd.DataFrame:
        """Get all head-to-head statistics for a league."""
        return self.execute_query('''
            SELECT 
                h2h.*,
                ht.team_name as home_team,
                at.team_name as away_team
            FROM head_to_head_stats h2h
            JOIN teams ht ON h2h.home_team_id = ht.team_id
            JOIN teams at ON h2h.away_team_id = at.team_id
            JOIN leagues l ON h2h.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY ht.team_name, at.team_name
        ''', (league_name,))
    
    # Analysis methods
    def get_team_form(self, team_name: str, league_name: str, last_n_matches: int = 5) -> pd.DataFrame:
        """Get recent form for a team with comprehensive fuzzy name matching."""
        # Get all team names that actually appear in match results for this league
        match_teams_df = self.execute_query('''
            SELECT DISTINCT team_name FROM (
                SELECT ht.team_name
                FROM match_results mr
                JOIN teams ht ON mr.home_team_id = ht.team_id
                JOIN leagues l ON mr.league_id = l.league_id
                WHERE l.league_name = ?
                UNION
                SELECT at.team_name
                FROM match_results mr
                JOIN teams at ON mr.away_team_id = at.team_id
                JOIN leagues l ON mr.league_id = l.league_id
                WHERE l.league_name = ?
            )
        ''', (league_name, league_name))

        if not match_teams_df.empty:
            candidate_names = match_teams_df['team_name'].tolist()
            best_match = find_best_team_match(team_name, candidate_names)

            result = self.execute_query('''
            SELECT DISTINCT
                mr.match_date,
                ht.team_name as home_team,
                mr.home_score,
                mr.away_score,
                at.team_name as away_team,
                CASE
                    WHEN ht.team_name = ? THEN 'Home'
                    WHEN at.team_name = ? THEN 'Away'
                END as venue,
                CASE
                    WHEN (ht.team_name = ? AND mr.home_score > mr.away_score) OR
                         (at.team_name = ? AND mr.away_score > mr.home_score) THEN 'W'
                    WHEN mr.home_score = mr.away_score THEN 'D'
                    ELSE 'L'
                END as result
            FROM match_results mr
            JOIN teams ht ON mr.home_team_id = ht.team_id
            JOIN teams at ON mr.away_team_id = at.team_id
            JOIN leagues l ON mr.league_id = l.league_id
            WHERE (ht.team_name = ? OR at.team_name = ?) AND l.league_name = ?
            AND mr.home_score IS NOT NULL AND mr.away_score IS NOT NULL
            ORDER BY
                -- Football season order: Aug-Dec (early season), Jan-May (late season)
                -- Late season months (Jan-May) should come first as they are more recent
                CASE
                    WHEN substr(mr.match_date, instr(mr.match_date, ' ') + 1, 3) IN ('Jan', 'Feb', 'Mar', 'Apr', 'May') THEN 1
                    WHEN substr(mr.match_date, instr(mr.match_date, ' ') + 1, 3) IN ('Aug', 'Sep', 'Oct', 'Nov', 'Dec') THEN 2
                    ELSE 3
                END,
                -- Within each season period, sort by month order
                CASE substr(mr.match_date, instr(mr.match_date, ' ') + 1, 3)
                    WHEN 'May' THEN 1
                    WHEN 'Apr' THEN 2
                    WHEN 'Mar' THEN 3
                    WHEN 'Feb' THEN 4
                    WHEN 'Jan' THEN 5
                    WHEN 'Dec' THEN 6
                    WHEN 'Nov' THEN 7
                    WHEN 'Oct' THEN 8
                    WHEN 'Sep' THEN 9
                    WHEN 'Aug' THEN 10
                    ELSE 11
                END,
                -- Sort by day within the month (descending for most recent first)
                CAST(substr(mr.match_date, 1, instr(mr.match_date, ' ') - 1) AS INTEGER) DESC
            LIMIT ?
        ''', (best_match, best_match, best_match, best_match, best_match, best_match, league_name, last_n_matches))

            return result

        # Return empty DataFrame if no teams found in league
        return pd.DataFrame()



    def get_league_top_scorers(self, league_name: str, limit: int = 10) -> pd.DataFrame:
        """Get top scoring teams in a league."""
        return self.execute_query('''
            SELECT 
                t.team_name,
                ts.goals_scored_all,
                ts.goals_scored_per_match_all,
                ts.total_played
            FROM team_stats ts
            JOIN teams t ON ts.team_id = t.team_id
            JOIN leagues l ON t.league_id = l.league_id
            WHERE l.league_name = ?
            ORDER BY ts.goals_scored_all DESC
            LIMIT ?
        ''', (league_name, limit))
    
    def get_database_summary(self) -> Dict:
        """Get summary statistics about the database."""
        summary = {}
        
        # Count records in each table
        tables = ['leagues', 'teams', 'league_stats', 'team_stats', 
                 'league_table', 'match_results', 'head_to_head_stats']
        
        for table in tables:
            count = self.execute_scalar(f'SELECT COUNT(*) FROM {table}')
            summary[table] = count
        
        # Database file size
        if os.path.exists(self.db_path):
            summary['database_size_mb'] = os.path.getsize(self.db_path) / (1024 * 1024)
        
        # League with most teams
        league_teams = self.execute_query('''
            SELECT l.league_name, COUNT(t.team_id) as team_count
            FROM leagues l
            LEFT JOIN teams t ON l.league_id = t.league_id
            GROUP BY l.league_id, l.league_name
            ORDER BY team_count DESC
            LIMIT 1
        ''')
        
        if not league_teams.empty:
            summary['most_teams_league'] = league_teams.iloc[0]['league_name']
            summary['most_teams_count'] = league_teams.iloc[0]['team_count']
        
        return summary

    def create_team_mapping_table(self):
        """Create a table to store team name mappings."""
        self.execute_statement('''
            CREATE TABLE IF NOT EXISTS team_name_mappings (
                mapping_id INTEGER PRIMARY KEY AUTOINCREMENT,
                league_id INTEGER,
                canonical_name TEXT,
                variation_name TEXT,
                source_table TEXT,
                confidence_score REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (league_id) REFERENCES leagues (league_id),
                UNIQUE(league_id, variation_name, source_table)
            )
        ''')

        # Create index for fast lookups
        self.execute_statement('''
            CREATE INDEX IF NOT EXISTS idx_team_mappings_lookup
            ON team_name_mappings (league_id, variation_name)
        ''')

    def get_canonical_team_name(self, team_name: str, league_name: str) -> str:
        """Get the canonical team name using the mapping table."""
        # Check cache first
        cache_key = f"{league_name}:{team_name}"
        if cache_key in self._team_mapping_cache:
            return self._team_mapping_cache[cache_key]

        # Get league_id
        league_id = self.execute_scalar(
            "SELECT league_id FROM leagues WHERE league_name = ?",
            (league_name,)
        )

        if not league_id:
            return team_name

        # Look up canonical name in mapping table
        canonical_name = self.execute_scalar('''
            SELECT canonical_name
            FROM team_name_mappings
            WHERE league_id = ? AND variation_name = ?
            ORDER BY confidence_score DESC
            LIMIT 1
        ''', (league_id, team_name))

        if canonical_name:
            self._team_mapping_cache[cache_key] = canonical_name
            return canonical_name

        # Fallback: try fuzzy matching
        all_mappings = self.execute_query('''
            SELECT DISTINCT canonical_name, variation_name, confidence_score
            FROM team_name_mappings
            WHERE league_id = ?
        ''', (league_id,))

        if not all_mappings.empty:
            candidate_names = all_mappings['variation_name'].tolist()
            best_match = find_best_team_match(team_name, candidate_names)

            if best_match != team_name:
                # Find canonical name for the best match
                canonical_name = self.execute_scalar('''
                    SELECT canonical_name
                    FROM team_name_mappings
                    WHERE league_id = ? AND variation_name = ?
                    ORDER BY confidence_score DESC
                    LIMIT 1
                ''', (league_id, best_match))

                if canonical_name:
                    self._team_mapping_cache[cache_key] = canonical_name
                    return canonical_name

        # Cache and return original name if no mapping found
        self._team_mapping_cache[cache_key] = team_name
        return team_name

def normalize_team_name(team_name: str, league_name: str = None, context: str = 'database') -> str:
    """
    Normalize team names by finding the best match in the database.
    Handles variations like 'AF Elbasani' vs 'Af Elbasani'.
    """
    if not team_name:
        return team_name

    with get_database() as db:
        # First try exact match with league
        if league_name:
            result = db.execute_scalar("""
                SELECT team_name
                FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE t.team_name = ? AND l.league_name = ?
            """, (team_name, league_name))
            if result:
                return result

        # Try exact match without league
        result = db.execute_scalar(
            "SELECT team_name FROM teams WHERE team_name = ? LIMIT 1",
            (team_name,)
        )
        if result:
            return result

        # If no exact match, try fuzzy matching within the league
        if league_name:
            # Get all team names in the league for fuzzy matching
            teams_df = db.execute_query("""
                SELECT DISTINCT t.team_name
                FROM teams t
                JOIN leagues l ON t.league_id = l.league_id
                WHERE l.league_name = ?
            """, (league_name,))

            if not teams_df.empty:
                team_names = teams_df['team_name'].tolist()
                best_match = find_best_team_match(team_name, team_names)
                if best_match:
                    return best_match

    # Fallback to original name if not found in database
    return team_name

def find_best_team_match(target_name: str, candidate_names: list) -> str:
    """
    Find the best matching team name from a list of candidates.
    Uses sophisticated matching for complex team name variations.
    """
    if not target_name or not candidate_names:
        return target_name

    target_clean = clean_team_name(target_name)
    best_match = target_name
    best_score = 0

    for candidate in candidate_names:
        candidate_clean = clean_team_name(candidate)
        score = calculate_name_similarity(target_clean, candidate_clean)

        if score > best_score:
            best_score = score
            best_match = candidate

    # Only return match if similarity is high enough
    if best_score >= 0.7:  # 70% similarity threshold
        return best_match

    return target_name

def clean_team_name(name: str) -> str:
    """
    Clean and normalize team name for comparison.
    Removes common prefixes, suffixes, and normalizes characters.
    """
    if not name:
        return ""

    # Convert to lowercase and strip
    clean = name.lower().strip()

    # Remove common prefixes
    prefixes = ['ks ', 'kf ', 'fc ', 'ac ', 'sc ', 'cf ', 'cd ', 'ud ', 'sd ', 'ad ']
    for prefix in prefixes:
        if clean.startswith(prefix):
            clean = clean[len(prefix):].strip()
            break

    # Remove common suffixes and location indicators
    suffixes = [' fc', ' sc', ' ac', ' cf', ' cd', ' ud', ' sd', ' ad']
    for suffix in suffixes:
        if clean.endswith(suffix):
            clean = clean[:-len(suffix)].strip()
            break

    # Handle location abbreviations (like "R." for "Rrogozhinë")
    location_mappings = {
        ' r.': ' rrogozhinë',
        ' t.': ' tirana',
        ' k.': ' korçë',
        ' s.': ' shkodër',
        ' d.': ' durrës',
        ' b.': ' ballsh'
    }

    for abbrev, full in location_mappings.items():
        clean = clean.replace(abbrev, full)

    # Normalize special characters
    char_mappings = {
        'ç': 'c', 'ë': 'e', 'ï': 'i', 'ö': 'o', 'ü': 'u',
        'á': 'a', 'é': 'e', 'í': 'i', 'ó': 'o', 'ú': 'u',
        'à': 'a', 'è': 'e', 'ì': 'i', 'ò': 'o', 'ù': 'u',
        '√´': '', '√¨': 'e', '√¢': 'a'  # Handle encoding issues
    }

    for special, normal in char_mappings.items():
        clean = clean.replace(special, normal)

    # Remove extra spaces
    clean = ' '.join(clean.split())

    return clean

def calculate_name_similarity(name1: str, name2: str) -> float:
    """
    Calculate similarity score between two team names.
    Returns a score between 0 and 1.
    """
    if not name1 or not name2:
        return 0.0

    # Exact match
    if name1 == name2:
        return 1.0

    # Check if one is contained in the other
    if name1 in name2 or name2 in name1:
        # Calculate containment ratio
        shorter = min(len(name1), len(name2))
        longer = max(len(name1), len(name2))
        return shorter / longer

    # Word-based similarity
    words1 = set(name1.split())
    words2 = set(name2.split())

    if not words1 or not words2:
        return 0.0

    # Calculate Jaccard similarity (intersection over union)
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))

    return intersection / union if union > 0 else 0.0

# Convenience functions for common operations
def get_database() -> FootballDatabase:
    """Get a database instance."""
    return FootballDatabase()

def query_to_dataframe(db: FootballDatabase, query: str, params: tuple = ()) -> pd.DataFrame:
    """Execute query and return DataFrame."""
    return db.execute_query(query, params)

def get_available_leagues() -> List[str]:
    """Get list of available leagues."""
    with get_database() as db:
        leagues_df = db.get_leagues()
        return leagues_df['league_name'].tolist()

def get_league_teams(league_name: str) -> List[str]:
    """Get list of teams in a league."""
    with get_database() as db:
        teams_df = db.get_teams(league_name)
        return teams_df['team_name'].tolist()

# Query builder class for complex queries
class QueryBuilder:
    """Helper class to build complex SQL queries."""
    
    @staticmethod
    def get_team_performance_query(league_name: str, min_matches: int = 5) -> str:
        """Build query for team performance analysis."""
        return '''
            SELECT 
                t.team_name,
                ts.total_played,
                ts.total_wins,
                ts.total_draws,
                ts.total_losses,
                ts.points_per_game,
                ts.goals_scored_per_match_all,
                ts.goals_conceded_per_match_all,
                (ts.goals_scored_per_match_all - ts.goals_conceded_per_match_all) as goal_difference_per_match,
                lt.position,
                lt.points
            FROM team_stats ts
            JOIN teams t ON ts.team_id = t.team_id
            JOIN leagues l ON t.league_id = l.league_id
            LEFT JOIN league_table lt ON t.team_id = lt.team_id
            WHERE l.league_name = ? AND ts.total_played >= ?
            ORDER BY lt.position
        '''
    
    @staticmethod
    def get_h2h_summary_query(league_name: str) -> str:
        """Build query for H2H summary statistics."""
        return '''
            SELECT 
                COUNT(*) as total_matchups,
                AVG(total_matches) as avg_matches_per_h2h,
                AVG(home_win_percentage) as avg_home_win_pct,
                AVG(away_win_percentage) as avg_away_win_pct,
                AVG(draw_percentage) as avg_draw_pct,
                AVG(btts_percentage) as avg_btts_pct
            FROM head_to_head_stats h2h
            JOIN leagues l ON h2h.league_id = l.league_id
            WHERE l.league_name = ?
        '''
    
    @staticmethod
    def get_recent_form_query(team_name: str, league_name: str, days: int = 30) -> str:
        """Build query for recent team form."""
        return '''
            SELECT 
                COUNT(*) as matches_played,
                SUM(CASE 
                    WHEN (ht.team_name = ? AND mr.home_score > mr.away_score) OR 
                         (at.team_name = ? AND mr.away_score > mr.home_score) THEN 1 
                    ELSE 0 
                END) as wins,
                SUM(CASE WHEN mr.home_score = mr.away_score THEN 1 ELSE 0 END) as draws,
                SUM(CASE 
                    WHEN (ht.team_name = ? AND mr.home_score < mr.away_score) OR 
                         (at.team_name = ? AND mr.away_score < mr.home_score) THEN 1 
                    ELSE 0 
                END) as losses
            FROM match_results mr
            JOIN teams ht ON mr.home_team_id = ht.team_id
            JOIN teams at ON mr.away_team_id = at.team_id
            JOIN leagues l ON mr.league_id = l.league_id
            WHERE (ht.team_name = ? OR at.team_name = ?) 
            AND l.league_name = ?
            AND mr.home_score IS NOT NULL 
            AND mr.away_score IS NOT NULL
            AND date(mr.match_date) >= date('now', '-{} days')
        '''.format(days)