"""
Database Configuration

This module contains database connection settings and configuration
for the sports betting data project.
"""

import os
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent.parent
DATA_DIR = PROJECT_ROOT / 'data'
LOGS_DIR = PROJECT_ROOT / 'logs'

# Database settings
DB_PATH = DATA_DIR / 'sports_data.db'
DB_BACKUP_DIR = DATA_DIR / 'backups'

# SQLite specific settings
SQLITE_SETTINGS = {
    'database_path': str(DB_PATH),
    'timeout': 30.0,  # Connection timeout in seconds
    'check_same_thread': False,  # Allow SQLite to be used from multiple threads
    'journal_mode': 'WAL',  # Write-Ahead Logging for better performance
    'synchronous': 'NORMAL',  # Balance between safety and performance
    'cache_size': -64000,  # 64MB cache (negative value means KB)
    'temp_store': 'MEMORY',  # Store temporary tables in memory
}

# Logging settings
LOGGING_CONFIG = {
    'log_dir': str(LOGS_DIR),
    'log_file': str(LOGS_DIR / 'database.log'),
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# Data processing settings
DATA_SETTINGS = {
    'current_season': '2024-2025',
    'current_season_year_start': 2024,
    'raw_data_dir': str(DATA_DIR / 'raw'),
    'batch_size': 1000,  # Records to process in batches
    'enable_backup': True,
    'backup_before_migration': True
}

# Table names
TABLES = {
    'LEAGUE_TABLES': 'league_tables',
    'MATCH_RESULTS': 'match_results', 
    'HEAD_TO_HEAD_STATS': 'head_to_head_stats',
    'LEAGUE_SUMMARY_STATS': 'league_summary_stats',
    'TEAM_DETAILED_STATS': 'team_detailed_stats'
}

def get_sqlite_connection_string():
    """Get SQLite connection string with optimized settings"""
    return str(DB_PATH)

def ensure_directories():
    """Ensure all required directories exist"""
    directories = [DATA_DIR, LOGS_DIR, DB_BACKUP_DIR]
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def get_database_info():
    """Get information about database status"""
    info = {
        'database_exists': DB_PATH.exists(),
        'database_path': str(DB_PATH),
        'database_size': DB_PATH.stat().st_size if DB_PATH.exists() else 0,
        'raw_data_exists': (DATA_DIR / 'raw').exists(),
        'logs_dir': str(LOGS_DIR),
        'backup_dir': str(DB_BACKUP_DIR)
    }
    return info

# Environment-specific overrides
if os.getenv('ENVIRONMENT') == 'production':
    SQLITE_SETTINGS['journal_mode'] = 'WAL'
    LOGGING_CONFIG['level'] = 'WARNING'
elif os.getenv('ENVIRONMENT') == 'development':
    LOGGING_CONFIG['level'] = 'DEBUG'