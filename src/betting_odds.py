#!/usr/bin/env python3
"""
Betting Odds Integration Module

This module provides integration with various betting odds APIs to fetch
real-time betting market data for football matches.

Supported APIs:
- The Odds API (odds-api.com)
- RapidAPI Sports Odds
- BetFair Exchange API
- Custom bookmaker APIs

Usage:
    from betting_odds import BettingOddsProvider
    
    provider = BettingOddsProvider(api_key="your_api_key")
    odds = provider.get_match_odds("Premier League", "Liverpool", "Arsenal")
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class BettingOddsProvider:
    """Main class for fetching betting odds from various providers."""
    
    def __init__(self, api_key: str = None, provider: str = "odds_api"):
        """
        Initialize the betting odds provider.
        
        Args:
            api_key: API key for the chosen provider
            provider: Provider name ("odds_api", "rapidapi", "betfair")
        """
        self.api_key = api_key
        self.provider = provider
        self.base_urls = {
            "odds_api": "https://api.the-odds-api.com/v4",
            "rapidapi": "https://api-football-v1.p.rapidapi.com/v3",
            "betfair": "https://api.betfair.com/exchange/betting/rest/v1.0"
        }
        self.cache = {}
        self.cache_duration = 300  # 5 minutes
        
    def get_match_odds(self, league: str, team1: str, team2: str) -> Optional[Dict]:
        """
        Get betting odds for a specific match.
        
        Args:
            league: League name
            team1: Home team name
            team2: Away team name
            
        Returns:
            Dictionary containing betting odds or None if not available
        """
        cache_key = f"{league}_{team1}_{team2}"
        
        # Check cache first
        if self._is_cached(cache_key):
            return self.cache[cache_key]['data']
        
        try:
            if self.provider == "odds_api":
                odds = self._fetch_odds_api(league, team1, team2)
            elif self.provider == "rapidapi":
                odds = self._fetch_rapidapi(league, team1, team2)
            elif self.provider == "betfair":
                odds = self._fetch_betfair(league, team1, team2)
            else:
                logger.error(f"Unsupported provider: {self.provider}")
                return None
            
            # Cache the result
            if odds:
                self._cache_result(cache_key, odds)
            
            return odds
            
        except Exception as e:
            logger.error(f"Error fetching odds for {team1} vs {team2}: {e}")
            return None
    
    def _fetch_odds_api(self, league: str, team1: str, team2: str) -> Optional[Dict]:
        """Fetch odds from The Odds API."""
        if not self.api_key:
            logger.warning("No API key provided for The Odds API")
            return None
        
        # Map league names to The Odds API sport keys
        sport_mapping = {
            "ENGLAND_PREMIER_LEAGUE": "soccer_epl",
            "SPAIN_LA_LIGA": "soccer_spain_la_liga",
            "GERMANY_BUNDESLIGA": "soccer_germany_bundesliga",
            "ITALY_SERIE_A": "soccer_italy_serie_a",
            "FRANCE_LIGUE_1": "soccer_france_ligue_one",
            # Add more mappings as needed
        }
        
        sport_key = sport_mapping.get(league)
        if not sport_key:
            logger.warning(f"League {league} not supported by The Odds API")
            return None
        
        url = f"{self.base_urls['odds_api']}/sports/{sport_key}/odds"
        params = {
            "apiKey": self.api_key,
            "regions": "uk,us,eu",
            "markets": "h2h,totals,btts",
            "oddsFormat": "decimal",
            "dateFormat": "iso"
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # Find the specific match
            for game in data:
                home_team = game.get('home_team', '')
                away_team = game.get('away_team', '')
                
                if self._teams_match(team1, home_team) and self._teams_match(team2, away_team):
                    return self._parse_odds_api_response(game)
            
            logger.info(f"No odds found for {team1} vs {team2} in {league}")
            return None
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed: {e}")
            return None
    
    def _fetch_rapidapi(self, league: str, team1: str, team2: str) -> Optional[Dict]:
        """Fetch odds from RapidAPI."""
        # TODO: Implement RapidAPI integration
        logger.warning("RapidAPI integration not yet implemented")
        return None
    
    def _fetch_betfair(self, league: str, team1: str, team2: str) -> Optional[Dict]:
        """Fetch odds from BetFair API."""
        # TODO: Implement BetFair integration
        logger.warning("BetFair API integration not yet implemented")
        return None
    
    def _parse_odds_api_response(self, game_data: Dict) -> Dict:
        """Parse The Odds API response into standardized format."""
        try:
            bookmakers = game_data.get('bookmakers', [])
            if not bookmakers:
                return None
            
            # Get average odds across bookmakers
            h2h_odds = []
            totals_odds = []
            btts_odds = []
            
            for bookmaker in bookmakers:
                for market in bookmaker.get('markets', []):
                    if market['key'] == 'h2h':
                        h2h_odds.append(market['outcomes'])
                    elif market['key'] == 'totals':
                        totals_odds.append(market['outcomes'])
                    elif market['key'] == 'btts':
                        btts_odds.append(market['outcomes'])
            
            # Calculate average odds
            result = {
                'home_win_odds': self._average_odds(h2h_odds, 'home'),
                'draw_odds': self._average_odds(h2h_odds, 'draw'),
                'away_win_odds': self._average_odds(h2h_odds, 'away'),
                'over_2_5_odds': self._average_odds(totals_odds, 'over'),
                'under_2_5_odds': self._average_odds(totals_odds, 'under'),
                'btts_yes_odds': self._average_odds(btts_odds, 'yes'),
                'btts_no_odds': self._average_odds(btts_odds, 'no'),
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'source': 'The Odds API',
                'bookmaker_count': len(bookmakers)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error parsing odds data: {e}")
            return None
    
    def _average_odds(self, odds_list: List, outcome_type: str) -> Optional[float]:
        """Calculate average odds for a specific outcome."""
        if not odds_list:
            return None
        
        values = []
        for odds_group in odds_list:
            for outcome in odds_group:
                if outcome.get('name', '').lower() == outcome_type.lower():
                    values.append(float(outcome.get('price', 0)))
        
        return round(sum(values) / len(values), 2) if values else None
    
    def _teams_match(self, team1: str, team2: str) -> bool:
        """Check if team names match (with fuzzy matching)."""
        # Simple matching - can be improved with fuzzy string matching
        team1_clean = team1.lower().replace(' ', '').replace('_', '')
        team2_clean = team2.lower().replace(' ', '').replace('_', '')
        
        return team1_clean in team2_clean or team2_clean in team1_clean
    
    def _is_cached(self, cache_key: str) -> bool:
        """Check if data is cached and still valid."""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        return (time.time() - cache_time) < self.cache_duration
    
    def _cache_result(self, cache_key: str, data: Dict) -> None:
        """Cache the result with timestamp."""
        self.cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
    
    def get_supported_leagues(self) -> List[str]:
        """Get list of supported leagues for the current provider."""
        if self.provider == "odds_api":
            return [
                "ENGLAND_PREMIER_LEAGUE",
                "SPAIN_LA_LIGA", 
                "GERMANY_BUNDESLIGA",
                "ITALY_SERIE_A",
                "FRANCE_LIGUE_1"
            ]
        return []


# Example usage and testing
def test_betting_odds():
    """Test function for betting odds integration."""
    provider = BettingOddsProvider()
    
    # Test without API key (should return None)
    odds = provider.get_match_odds("ENGLAND_PREMIER_LEAGUE", "Liverpool", "Arsenal")
    print(f"Odds without API key: {odds}")
    
    # Test with mock data
    mock_odds = {
        'home_win_odds': 2.10,
        'draw_odds': 3.40,
        'away_win_odds': 3.20,
        'over_2_5_odds': 1.85,
        'under_2_5_odds': 1.95,
        'btts_yes_odds': 1.70,
        'btts_no_odds': 2.15,
        'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'source': 'Mock Data',
        'bookmaker_count': 5
    }
    
    print(f"Mock odds data: {json.dumps(mock_odds, indent=2)}")


if __name__ == "__main__":
    test_betting_odds()