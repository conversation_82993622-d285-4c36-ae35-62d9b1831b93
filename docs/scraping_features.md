# Scraping Features and Command-Line Interface

This document outlines the command-line interface for `src/scrapers/main.py`, allowing for flexible data scraping operations.

## Argument Structure

The script uses a combination of scope-defining arguments and operation flags.

### League/Match Specification (Optional - for targeting):

*   `--league <LEAGUE_NAME>`: Target a specific league. If omitted, operations apply to all configured leagues.
    *   Example: `--league "ENGLAND_PREMIER_LEAGUE"`
*   `--team <TEAM_NAME>`: (Primarily for team stats, or filtering H2H if a broader H2H operation is specified for the league, or for targeted H2H scrape of a team's matches if no other operation is specified). Requires `--league`.
    *   Example: `--league "ENGLAND_PREMIER_LEAGUE" --team "Arsenal"`
*   `--match <MATCHUP_STRING>`: (For specific H2H matchup, e.g., "TeamA vs TeamB"). Requires `--league`.
    *   Example: `--league "ENGLAND_PREMIER_LEAGUE" --match "Arsenal vs Chelsea"`

### Data Operation Flags:

These flags determine what data is scraped and how existing data is handled. If no operation flags are specified, the default behavior is a full overwrite of all data types for the specified scope (all leagues or a single league).

*   `--force-all-data`: (Boolean) Overwrite ALL data types (league stats, team stats, league table, historical match results, H2H stats, and recent results derived from H2H) for the specified scope. This is the most forceful overwrite.
*   `--refresh-league-data`: (Boolean) Overwrite league stats, team stats, and league table for the specified scope.
*   `--refresh-results`: (Boolean) Overwrite historical match results for the specified scope.
*   `--refresh-h2h`: (Boolean) Overwrite H2H stats and their derived recent results for the specified scope.
*   `--incremental-results`: (Boolean) Incrementally update historical match results for the specified scope. This will only add new results since the last scrape.
*   `--incremental-h2h`: (Boolean) Incrementally update H2H stats and their derived recent results for the specified scope. This will only add H2H data for matchups not already present in the existing H2H file.
*   `--incremental-all-updatable`: (Boolean) Convenience flag to run both `--incremental-results` and `--incremental-h2h` for the specified scope.

## How Flags Map to User Requests:

1.  **Rescrape ALL data for ALL stats at once (Overwrite all leagues):**
    *   Command: `python src/scrapers/main.py`
    *   Or explicitly: `python src/scrapers/main.py --force-all-data`
    *   *(Note: For a true fresh start, ensure `checkpoint.pkl` is deleted if resuming a previous full run is not desired).*

2.  **Rescrape ALL data for INDIVIDUAL STATS TYPES for ALL leagues (Overwrite):**
    *   H2H stats only (all leagues): `python src/scrapers/main.py --refresh-h2h`
    *   League stats, team stats, table only (all leagues): `python src/scrapers/main.py --refresh-league-data`
    *   Results stats only (all leagues): `python src/scrapers/main.py --refresh-results`
    *   For a specific league: Add `--league "LEAGUE_NAME"` to the above commands.
        *   Example: `python src/scrapers/main.py --league "ENGLAND_PREMIER_LEAGUE" --refresh-h2h`

3.  **Update (INCREMENTALLY) existing data with new data for ALL applicable stat types for ALL leagues at once:**
    *   Command: `python src/scrapers/main.py --incremental-all-updatable`
    *   This will run incremental updates for results and H2H for all leagues.

4.  **Update (INCREMENTALLY) existing data with new data for INDIVIDUAL STATS TYPES for ALL leagues:**
    *   H2H stats only (all leagues): `python src/scrapers/main.py --incremental-h2h`
    *   Results stats only (all leagues): `python src/scrapers/main.py --incremental-results`
    *   For a specific league: Add `--league "LEAGUE_NAME"` to the above commands.
        *   Example: `python src/scrapers/main.py --league "ENGLAND_PREMIER_LEAGUE" --incremental-results`

## Default Behavior (No Operation Flags):

*   If only `python src/scrapers/main.py` is run: Full overwrite of all data for all leagues (your request #1).
*   If `python src/scrapers/main.py --league "LEAGUE_NAME"` is run: Full overwrite of all data for that specific league.

## Targeted Scrapes (No Global Operation Flags):

If no global operation flags (`--force-all-data`, `--refresh-...`, `--incremental-...`) are specified, but scope arguments like `--team` or `--match` are used with `--league`:

*   `python src/scrapers/main.py --league "L_NAME" --team "T_NAME"`:
    *   This will scrape and **overwrite** the team stats for "T_NAME" in the `L_NAME_team_stats.csv` file.
    *   It will also scrape H2H data for all matchups involving "T_NAME" in league "L_NAME". This operation will **overwrite** these specific matchups if they exist in `L_NAME_head_to_head_stats.csv` and `L_NAME_recent_results.csv`. If the files don't exist or the matchups are new, they will be created/added. Data for other matchups not involving "T_NAME" will be preserved if the files already exist.
*   `python src/scrapers/main.py --league "L_NAME" --match "TeamA vs TeamB"`:
    *   This will scrape H2H data for the "TeamA vs TeamB" matchup in league "L_NAME". This operation will **overwrite** this specific matchup if it exists in `L_NAME_head_to_head_stats.csv` and `L_NAME_recent_results.csv`. If the files don't exist or the matchup is new, it will be created/added. Data for other matchups will be preserved if the files already exist.

## Checkpoint System:

*   A `checkpoint.pkl` file is used to allow resumption of long-running **full overwrite scrapes for all leagues** (`python src/scrapers/main.py` or `python src/scrapers/main.py --force-all-data` without `--league`).
*   If such a run is interrupted, it can be resumed from the last processed league.
*   The checkpoint file is automatically removed upon successful completion of a full all-league overwrite run.
*   Targeted operations (e.g., specific league, specific data type refreshes, incremental updates) generally do not use or affect the multi-league checkpoint file. If a targeted operation on a single league (specified by `--league`) completes successfully, and a checkpoint file exists that was specifically for that league (e.g., from a previously failed targeted run for that same league), it might be removed.

## Previous Incremental Features (Now Integrated/Superseded)

The previous specific flags like `--incremental-results-league <LEAGUE_NAME>` and `--incremental-h2h-league <LEAGUE_NAME>` have been superseded by the more general `--incremental-results` and `--incremental-h2h` flags, which can be combined with `--league <LEAGUE_NAME>` for league-specific incremental updates.

The underlying logic for incremental updates remains:
*   **Incremental Results**: Reads existing results, determines the last date, and scrapes new results from that date onwards, appending them.
*   **Incremental H2H**: Reads existing H2H matchups, identifies new matchups from the configuration, scrapes them, and appends them. Recent results from these new H2H scrapes are also appended.

## Considerations for Future Enhancements (Still Relevant)

*   **Deduplication for `recent_results.csv`**: The current append logic for recent results (derived from H2H) might lead to duplicates if the same past match appears in the "recent results" section of multiple H2H pages scraped incrementally over time. Future work could involve adding a deduplication step.
*   **H2H Header Management for Appending**: H2H stats can have dynamic columns. When appending, new data might introduce new columns. Pandas handles this by filling missing values with NaN, which is the current behavior.