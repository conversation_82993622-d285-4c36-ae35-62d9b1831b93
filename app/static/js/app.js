// Football Database Web Interface JavaScript

// Global variables
let currentChart = null;

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Initialize tooltips
    initializeTooltips();
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize table sorting
    initializeTableSorting();
    
    // Initialize lazy loading
    initializeLazyLoading();
    
    // Initialize animations
    initializeAnimations();
}

// Tooltip initialization
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Search functionality
function initializeSearch() {
    const searchInputs = document.querySelectorAll('input[type="search"]');
    
    searchInputs.forEach(input => {
        // Add search icon animation
        input.addEventListener('focus', function() {
            const icon = this.parentElement.querySelector('.fa-search');
            if (icon) {
                icon.style.color = '#007bff';
            }
        });
        
        input.addEventListener('blur', function() {
            const icon = this.parentElement.querySelector('.fa-search');
            if (icon) {
                icon.style.color = '';
            }
        });
        
        // Add real-time search for local filtering
        input.addEventListener('input', debounce(function() {
            const searchTerm = this.value.toLowerCase();
            filterLocalContent(searchTerm);
        }, 300));
    });
}

// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Filter local content (for leagues page, etc.)
function filterLocalContent(searchTerm) {
    const cards = document.querySelectorAll('.league-card, .team-card');
    let visibleCount = 0;
    
    cards.forEach(card => {
        const text = card.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });
    
    // Show/hide no results message
    const noResults = document.getElementById('noResults');
    if (noResults) {
        noResults.style.display = visibleCount === 0 ? 'block' : 'none';
    }
}

// Table sorting functionality
function initializeTableSorting() {
    const tables = document.querySelectorAll('table.sortable');
    
    tables.forEach(table => {
        const headers = table.querySelectorAll('th[data-sort]');
        
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.innerHTML += ' <i class="fas fa-sort text-muted"></i>';
            
            header.addEventListener('click', function() {
                const column = this.dataset.sort;
                const type = this.dataset.type || 'string';
                sortTable(table, column, type);
            });
        });
    });
}

// Sort table function
function sortTable(table, column, type) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(table.querySelectorAll('th')).findIndex(th => th.dataset.sort === column);
    
    if (columnIndex === -1) return;
    
    const isAscending = !table.dataset.sortDirection || table.dataset.sortDirection === 'desc';
    table.dataset.sortDirection = isAscending ? 'asc' : 'desc';
    
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        let comparison = 0;
        
        if (type === 'number') {
            comparison = parseFloat(aValue) - parseFloat(bValue);
        } else {
            comparison = aValue.localeCompare(bValue);
        }
        
        return isAscending ? comparison : -comparison;
    });
    
    // Update sort icons
    table.querySelectorAll('th i').forEach(icon => {
        icon.className = 'fas fa-sort text-muted';
    });
    
    const currentHeader = table.querySelector(`th[data-sort="${column}"] i`);
    currentHeader.className = `fas fa-sort-${isAscending ? 'up' : 'down'} text-primary`;
    
    // Reorder rows
    rows.forEach(row => tbody.appendChild(row));
}

// Lazy loading for images and content
function initializeLazyLoading() {
    const lazyElements = document.querySelectorAll('.lazy');
    
    if ('IntersectionObserver' in window) {
        const lazyObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    
                    if (element.dataset.src) {
                        element.src = element.dataset.src;
                        element.classList.remove('lazy');
                    }
                    
                    if (element.dataset.load) {
                        loadContent(element);
                    }
                    
                    lazyObserver.unobserve(element);
                }
            });
        });
        
        lazyElements.forEach(element => lazyObserver.observe(element));
    }
}

// Animation initialization
function initializeAnimations() {
    // Fade in animation for cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Number counter animation
    animateNumbers();
}

// Animate numbers (for statistics)
function animateNumbers() {
    const numberElements = document.querySelectorAll('.animate-number');
    
    numberElements.forEach(element => {
        const target = parseInt(element.textContent.replace(/,/g, ''));
        const duration = 2000;
        const start = performance.now();
        
        function updateNumber(currentTime) {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(progress * target);
            element.textContent = current.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }
        
        requestAnimationFrame(updateNumber);
    });
}

// Chart utilities
function createChart(canvasId, type, data, options = {}) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) return null;
    
    const ctx = canvas.getContext('2d');
    
    // Destroy existing chart if it exists
    if (currentChart) {
        currentChart.destroy();
    }
    
    const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    };
    
    const chartOptions = { ...defaultOptions, ...options };
    
    currentChart = new Chart(ctx, {
        type: type,
        data: data,
        options: chartOptions
    });
    
    return currentChart;
}

// API utilities
async function fetchData(url) {
    try {
        showLoading();
        const response = await fetch(url);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        hideLoading();
        return data;
    } catch (error) {
        hideLoading();
        showError('Failed to fetch data: ' + error.message);
        return null;
    }
}

// Loading states
function showLoading() {
    const loadingElements = document.querySelectorAll('.loading-target');
    loadingElements.forEach(element => {
        element.classList.add('loading');
    });
    
    // Show loading spinner if it exists
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.display = 'block';
    }
}

function hideLoading() {
    const loadingElements = document.querySelectorAll('.loading-target');
    loadingElements.forEach(element => {
        element.classList.remove('loading');
    });
    
    // Hide loading spinner
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.display = 'none';
    }
}

// Error handling
function showError(message) {
    const errorContainer = document.getElementById('errorContainer');
    if (errorContainer) {
        errorContainer.innerHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    } else {
        console.error(message);
        alert(message);
    }
}

// Success messages
function showSuccess(message) {
    const successContainer = document.getElementById('successContainer');
    if (successContainer) {
        successContainer.innerHTML = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
}

// Utility functions
function formatNumber(num) {
    return num.toLocaleString();
}

function formatPercentage(num, decimals = 1) {
    return num.toFixed(decimals) + '%';
}

function formatCurrency(num, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(num);
}

// Export functions for global use
window.footballDB = {
    createChart,
    fetchData,
    showLoading,
    hideLoading,
    showError,
    showSuccess,
    formatNumber,
    formatPercentage,
    formatCurrency
};