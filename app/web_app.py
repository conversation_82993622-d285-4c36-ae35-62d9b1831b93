#!/usr/bin/env python3
"""
Football Database Web Interface


A Flask web application providing a clean GUI to explore the football database.
Features league browsing, team analysis, head-to-head comparisons, and more.

"""
from flask import Flask, render_template, request, jsonify, redirect, url_for
import sys
import os
import pandas as pd
import json
from datetime import datetime

# Add src to path for database imports
sys.path.append('src')
from database.football_db import FootballDatabase, get_database, QueryBuilder, normalize_team_name

# Add prediction imports
sys.path.append('src')
from data_loading import load_data, get_available_leagues
from feature_engineering import prepare_features
from model_training import train_model
from prediction import (
    predict_match,
    analyze_prediction_confidence,
    assess_prediction_risk,
    generate_prediction_summary
)
try:
    from scrapers.config import LEAGUE_CONFIGS
except ImportError:
    LEAGUE_CONFIGS = {}

app = Flask(__name__)

@app.template_filter('format_date')
def format_date(date_str):
    if not date_str:
        return ""
    try:
        # Assuming the date is in a format like 'DD Mon'
        dt = datetime.strptime(date_str, '%d %b')
        now = datetime.now()
        if dt.month > now.month:
            dt = dt.replace(year=now.year - 1)
        else:
            dt = dt.replace(year=now.year)
        return dt.strftime('%d %b %Y')
    except (ValueError, TypeError):
        return date_str

app.secret_key = 'football_betting_secret_key_2024'

# Global database instance
db_path = 'data/football_betting.db'

@app.context_processor
def inject_leagues():
    """Inject leagues data into all templates for sidebar navigation"""
    try:
        with get_database() as db:
            leagues_df = db.get_leagues()
            # Sort leagues alphabetically and group by country/region
            leagues_list = sorted(leagues_df['league_name'].tolist())
            
            # Group leagues by country prefix for better organization
            grouped_leagues = {}
            for league in leagues_list:
                country = league.split('_')[0]
                if country not in grouped_leagues:
                    grouped_leagues[country] = []
                grouped_leagues[country].append(league)
            
            return dict(sidebar_leagues=grouped_leagues, all_leagues=leagues_list)
    except Exception as e:
        print(f"Error getting leagues for sidebar: {e}")
        return dict(sidebar_leagues={}, all_leagues=[])

def get_db_summary():
    try:
        with get_database() as db:
            return db.get_database_summary()
    except Exception as e:
        print(f"Error getting database summary: {e}")
        return {}

def safe_float(value, default=0.0):
    try:
        if pd.isna(value) or value is None:
            return default
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    try:
        if pd.isna(value) or value is None:
            return default
        return int(value)
    except (ValueError, TypeError):
        return default

@app.route('/')
def index():
    summary = get_db_summary()
    
    # Get sample leagues for quick access
    try:
        with get_database() as db:
            leagues = db.get_leagues()
            sample_leagues = leagues.head(10)['league_name'].tolist()
    except Exception as e:
        print(f"Error getting leagues: {e}")
        sample_leagues = []
    
    return render_template('index.html', 
                         summary=summary, 
                         sample_leagues=sample_leagues)

@app.route('/leagues')
def leagues():
    try:
        with get_database() as db:
            leagues_df = db.get_leagues()
            leagues_list = leagues_df.to_dict('records')
    except Exception as e:
        print(f"Error getting leagues: {e}")
        leagues_list = []
    
    return render_template('leagues.html', leagues=leagues_list)

@app.route('/league/<league_name>')
def league_detail(league_name):
    try:
        with get_database() as db:
            # Get league table
            table = db.get_league_table(league_name)
            table_data = []
            if not table.empty:
                for _, row in table.iterrows():
                    table_data.append({
                        'position': safe_int(row['position']),
                        'team_name': row['team_name'],
                        'MP': safe_int(row['MP']),
                        'W': safe_int(row['W']),
                        'D': safe_int(row['D']),
                        'L': safe_int(row['L']),
                        'GF': safe_int(row['GF']),
                        'GA': safe_int(row['GA']),
                        'GD': safe_int(row['GD']),
                        'Pts': safe_int(row['Pts'])
                    })
            
            # Get league statistics
            league_stats = db.get_league_stats(league_name)
            stats_data = {}
            if not league_stats.empty:
                for _, row in league_stats.iterrows():
                    stats_data[row['stat_name']] = safe_float(row['stat_value'])
            
            # Get top scorers
            top_scorers = db.get_league_top_scorers(league_name, 10)
            scorers_data = []
            if not top_scorers.empty:
                for _, row in top_scorers.iterrows():
                    scorers_data.append({
                        'team_name': row['team_name'],
                        'goals_scored_all': safe_int(row['goals_scored_all']),
                        'goals_scored_per_match_all': safe_float(row['goals_scored_per_match_all']),
                        'total_played': safe_int(row['total_played'])
                    })
            
            # Get recent matches
            recent_matches = db.get_match_results(league_name, limit=10)
            matches_data = []
            if not recent_matches.empty:
                for _, row in recent_matches.iterrows():
                    matches_data.append({
                        'match_date': row['match_date'],
                        'home_team': row['home_team'],
                        'away_team': row['away_team'],
                        'home_score': safe_int(row['home_score']) if pd.notna(row['home_score']) else None,
                        'away_score': safe_int(row['away_score']) if pd.notna(row['away_score']) else None,
                        'result_code': safe_int(row['result_code']) if pd.notna(row['result_code']) else None
                    })
            
            # Get teams for dropdown
            teams = db.get_teams(league_name)
            teams_list = teams['team_name'].tolist() if not teams.empty else []
            
    except Exception as e:
        print(f"Error getting league details for {league_name}: {e}")
        table_data = []
        stats_data = {}
        scorers_data = []
        matches_data = []
        teams_list = []
    
    return render_template('league_detail.html',
                         league_name=league_name,
                         table=table_data,
                         stats=stats_data,
                         top_scorers=scorers_data,
                         recent_matches=matches_data,
                         teams=teams_list)

@app.route('/team/<league_name>/<team_name>')
def team_detail(league_name, team_name):
    try:
        with get_database() as db:
            # Normalize team name for all database lookups
            normalized_team_name = normalize_team_name(team_name, league_name)
            
            # Get team statistics
            team_stats = db.get_team_stats(normalized_team_name, league_name)

            stats_data = {}
            if not team_stats.empty:
                stats = team_stats.iloc[0]
                stats_data = {
                    'total_played': safe_int(stats['total_played']),
                    'total_wins': safe_int(stats['total_wins']),
                    'total_draws': safe_int(stats['total_draws']),
                    'total_losses': safe_int(stats['total_losses']),
                    'points_per_game': safe_float(stats['points_per_game']),
                    'home_points_per_game': safe_float(stats['home_points_per_game']),
                    'away_points_per_game': safe_float(stats['away_points_per_game']),
                    'goals_scored_all': safe_int(stats['goals_scored_all']),
                    'goals_conceded_all': safe_int(stats['goals_conceded_all']),
                    'goals_scored_per_match_all': safe_float(stats['goals_scored_per_match_all']),
                    'goals_conceded_per_match_all': safe_float(stats['goals_conceded_per_match_all']),
                    'goals_scored_home': safe_int(stats['goals_scored_home']),
                    'goals_scored_away': safe_int(stats['goals_scored_away']),
                    'goals_conceded_home': safe_int(stats['goals_conceded_home']),
                    'goals_conceded_away': safe_int(stats['goals_conceded_away']),
                    'ppg_last_8': safe_float(stats['ppg_last_8']),
                    'avg_goals_scored_last_8': safe_float(stats['avg_goals_scored_last_8']),
                    'avg_goals_conceded_last_8': safe_float(stats['avg_goals_conceded_last_8'])
                }

            # Get recent form
            recent_form = db.get_team_form(normalized_team_name, league_name, 10)
            form_data = []
            if not recent_form.empty:
                for _, row in recent_form.iterrows():
                    form_data.append({
                        'match_date': row['match_date'],
                        'home_team': row['home_team'],
                        'away_team': row['away_team'],
                        'home_score': safe_int(row['home_score']) if pd.notna(row['home_score']) else None,
                        'away_score': safe_int(row['away_score']) if pd.notna(row['away_score']) else None,
                        'venue': row['venue'],
                        'result': row['result']
                    })

            # Get all teams for sidebar and H2H comparison
            teams = db.get_teams(league_name)
            teams_list = sorted(teams['team_name'].tolist()) if not teams.empty else []
            other_teams = [t for t in teams_list if t != team_name]

    except Exception as e:
        print(f"Error getting team details for {team_name}: {e}")
        stats_data = {}
        form_data = []
        teams_list = []
        other_teams = []

    return render_template('team_detail.html',
                         league_name=league_name,
                         team_name=team_name,
                         teams=teams_list,
                         stats=stats_data,
                         recent_form=form_data,
                         other_teams=other_teams)

@app.route('/h2h/<league_name>/<team1>/<team2>')
def head_to_head(league_name, team1, team2):
    try:
        with get_database() as db:
            # Normalize team names
            normalized_team1 = normalize_team_name(team1, league_name)
            normalized_team2 = normalize_team_name(team2, league_name)
            
            # Get H2H statistics
            h2h_stats = db.get_head_to_head_stats(normalized_team1, normalized_team2, league_name)
            h2h_data = {}
            if not h2h_stats.empty:
                h2h = h2h_stats.iloc[0]
                h2h_data = {
                    'matchup': h2h['matchup'],
                    'total_matches': safe_int(h2h['total_matches']),
                    'home_team': h2h['home_team'],
                    'away_team': h2h['away_team'],
                    'home_win_percentage': safe_float(h2h['home_win_percentage']),
                    'away_win_percentage': safe_float(h2h['away_win_percentage']),
                    'draw_percentage': safe_float(h2h['draw_percentage']),
                    'home_wins': safe_int(h2h['home_wins']),
                    'away_wins': safe_int(h2h['away_wins']),
                    'draws': safe_int(h2h['draws']),
                    'home_goals': safe_int(h2h['home_goals']),
                    'away_goals': safe_int(h2h['away_goals']),
                    'btts_percentage': safe_float(h2h['btts_percentage']),
                    'over_1_5_percentage': safe_float(h2h['over_1_5_percentage']),
                    'over_2_5_percentage': safe_float(h2h['over_2_5_percentage']),
                    'over_3_5_percentage': safe_float(h2h['over_3_5_percentage']),
                    'home_clean_sheet_percentage': safe_float(h2h['home_clean_sheet_percentage']),
                    'away_clean_sheet_percentage': safe_float(h2h['away_clean_sheet_percentage']),
                    'recent_results': h2h['recent_results']
                }
            
            # Get individual team stats
            team1_stats = db.get_team_stats(normalized_team1, league_name)
            team2_stats = db.get_team_stats(normalized_team2, league_name)
            
            team1_data = {}
            team2_data = {}
            
            if not team1_stats.empty:
                stats = team1_stats.iloc[0]
                team1_data = {
                    'goals_scored_per_match_all': safe_float(stats['goals_scored_per_match_all']),
                    'goals_conceded_per_match_all': safe_float(stats['goals_conceded_per_match_all']),
                    'points_per_game': safe_float(stats['points_per_game']),
                    'total_played': safe_int(stats['total_played'])
                }
            
            if not team2_stats.empty:
                stats = team2_stats.iloc[0]
                team2_data = {
                    'goals_scored_per_match_all': safe_float(stats['goals_scored_per_match_all']),
                    'goals_conceded_per_match_all': safe_float(stats['goals_conceded_per_match_all']),
                    'points_per_game': safe_float(stats['points_per_game']),
                    'total_played': safe_int(stats['total_played'])
                }
            
            # Get all teams for sidebar navigation
            teams = db.get_teams(league_name)
            teams_list = sorted(teams['team_name'].tolist()) if not teams.empty else []
            
    except Exception as e:
        print(f"Error getting H2H data for {team1} vs {team2}: {e}")
        h2h_data = {}
        team1_data = {}
        team2_data = {}
        teams_list = []
    
    return render_template('head_to_head.html',
                         league_name=league_name,
                         team1=team1,
                         team2=team2,
                         teams=teams_list,
                         h2h=h2h_data,
                         team1_stats=team1_data,
                         team2_stats=team2_data)

@app.route('/search')
def search():
    query = request.args.get('q', '').strip()
    results = {'leagues': [], 'teams': []}
    
    if query and len(query) >= 2:
        try:
            with get_database() as db:
                # Search leagues
                leagues_df = db.execute_query(
                    "SELECT league_name FROM leagues WHERE league_name LIKE ? LIMIT 10",
                    (f'%{query}%',)
                )
                results['leagues'] = leagues_df['league_name'].tolist()
                
                # Search teams
                teams_df = db.execute_query(
                    "SELECT t.team_name, l.league_name "
                    "FROM teams t "
                    "JOIN leagues l ON t.league_id = l.league_id "
                    "WHERE t.team_name LIKE ? "
                    "LIMIT 10",
                    (f'%{query}%',)
                )
                
                results['teams'] = teams_df.to_dict('records')
                
        except Exception as e:
            print(f"Error searching for '{query}': {e}")
    
    return render_template('search.html', query=query, results=results)

@app.route('/api/leagues')
def api_leagues():
    try:
        with get_database() as db:
            leagues = db.get_leagues()
            return jsonify(leagues['league_name'].tolist())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/teams/<league_name>')
def api_teams(league_name):
    try:
        with get_database() as db:
            teams = db.get_teams(league_name)
            return jsonify(teams['team_name'].tolist())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats/<league_name>/<team_name>')
def api_team_stats(league_name, team_name):
    try:
        with get_database() as db:
            stats = db.get_team_stats(team_name, league_name)
            if stats.empty:
                return jsonify({'error': 'Team not found'}), 404
            
            # Convert to dict and handle NaN values
            stats_dict = stats.iloc[0].to_dict()
            for key, value in stats_dict.items():
                if pd.isna(value):
                    stats_dict[key] = None
            
            return jsonify(stats_dict)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/predictions')
def predictions():
    """Main predictions page."""
    try:
        # Get available leagues for dropdown
        available_leagues = get_available_leagues()
        leagues_display = []
        for league in available_leagues:
            display_name = league.replace('_', ' ').title()
            leagues_display.append({'code': league, 'name': display_name})
        
        return render_template('predictions.html', leagues=leagues_display)
    except Exception as e:
        print(f"Error loading predictions page: {e}")
        return render_template('predictions.html', leagues=[])

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """API endpoint for making match predictions."""
    try:
        data = request.get_json()
        league_name = data.get('league')
        home_team = data.get('home_team')
        away_team = data.get('away_team')
        
        if not all([league_name, home_team, away_team]):
            return jsonify({'error': 'Missing required parameters'}), 400
        
        # Try to load league data, but handle failures gracefully
        try:
            league_config = LEAGUE_CONFIGS.get(league_name, {})
            data_tuple, _ = load_data(league_name, league_config)

            if not data_tuple:
                return jsonify({'error': f'No data available for {league_name}. This league may not have sufficient data for predictions.'}), 400

            results, team_stats, league_stats, h2h_stats, league_table = data_tuple

        except Exception as e:
            return jsonify({'error': f'Failed to load data for {league_name}: {str(e)}'}), 500

        # Use team name mapping to find the correct names in the loaded data
        with get_database() as db:
            # Get all possible variations of the team names
            home_variations = db.execute_query('''
                SELECT DISTINCT variation_name
                FROM team_name_mappings tnm
                JOIN leagues l ON tnm.league_id = l.league_id
                WHERE l.league_name = ? AND tnm.canonical_name = ?
            ''', (league_name, home_team))

            away_variations = db.execute_query('''
                SELECT DISTINCT variation_name
                FROM team_name_mappings tnm
                JOIN leagues l ON tnm.league_id = l.league_id
                WHERE l.league_name = ? AND tnm.canonical_name = ?
            ''', (league_name, away_team))

        # Find which variation exists in the loaded team stats
        available_teams = set(team_stats["Team"].values)

        # Try to find home team
        found_home_team = None
        home_search_names = [home_team] + (home_variations['variation_name'].tolist() if not home_variations.empty else [])
        for name in home_search_names:
            if name in available_teams:
                found_home_team = name
                break

        # Try to find away team
        found_away_team = None
        away_search_names = [away_team] + (away_variations['variation_name'].tolist() if not away_variations.empty else [])
        for name in away_search_names:
            if name in available_teams:
                found_away_team = name
                break

        if not found_home_team:
            return jsonify({'error': f'Home team "{home_team}" not found in available data. Available teams: {sorted(list(available_teams))[:10]}...'}), 400
        if not found_away_team:
            return jsonify({'error': f'Away team "{away_team}" not found in available data. Available teams: {sorted(list(available_teams))[:10]}...'}), 400

        # Use the found team names
        home_team = found_home_team
        away_team = found_away_team
        
        # Prepare features with better error handling
        try:
            mappings = get_column_mappings()
            combined_mapping = {**mappings["team_stats"], **mappings["h2h_stats"]}

            prepared_data = prepare_features(
                results, team_stats, league_stats, h2h_stats, league_table,
                combined_mapping, league_config.get('TEAM_NAME_MAPPING', {})
            )

            if prepared_data is None:
                return jsonify({'error': 'Feature preparation failed. This league may have insufficient data for predictions.'}), 400

        except Exception as e:
            # Check if it's a missing H2H data issue
            if "missing values" in str(e).lower() or "h2h" in str(e).lower():
                return jsonify({'error': f'Insufficient head-to-head data for {league_name}. This league may not have enough historical match data for reliable predictions.'}), 400
            else:
                return jsonify({'error': f'Feature preparation failed: {str(e)}'}), 500
        
        # Prepare training data
        X, y_dict = prepare_prediction_data(prepared_data)
        if X is None or y_dict is None:
            return jsonify({'error': 'Failed to prepare prediction data'}), 500
        
        label_encoders = prepared_data.attrs.get("label_encoders", {})
        
        # Train models
        models = train_model(X, y_dict, label_encoders)
        if not models:
            return jsonify({'error': 'Model training failed'}), 500
        
        # Make prediction
        avg_goals_per_match = float(
            league_stats[league_stats["Stat"] == "avg_goals_per_match"]["Value"].values[0]
        )
        
        pred_results, error_message, correct_scores = predict_match(
            models,
            home_team,
            away_team,
            team_stats,
            league_stats,
            h2h_stats,
            league_table,
            combined_mapping,
            models["three_way"]["feature_names"],
            avg_goals_per_match,
            label_encoders=label_encoders,
            bias_correction=0.05,
            log_features=False
        )
        
        if error_message:
            return jsonify({'error': error_message}), 500
        
        # Analyze confidence and risk
        predictions = pred_results.get("main_predictions", {})
        confidence_analysis = analyze_prediction_confidence(predictions)
        risk_assessment = assess_prediction_risk(predictions, confidence_analysis)
        
        # Format response
        response = {
            'success': True,
            'match': f"{home_team} vs {away_team}",
            'league': league_name,
            'predictions': predictions,
            'expected_goals': pred_results.get("expected_goals", {}),
            'correct_scores': dict(list(correct_scores.items())[:5]) if correct_scores else {},
            'confidence_analysis': confidence_analysis,
            'risk_assessment': risk_assessment
        }
        
        return jsonify(response)
        
    except Exception as e:
        print(f"Error in prediction API: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

def get_column_mappings():
    """Get column mappings for different data types."""
    return {
        "team_stats": {
            "points_per_game": "points_per_game",
            "goals_scored_per_match_home": "goals_scored_per_match_home",
            "goals_scored_per_match_away": "goals_scored_per_match_away",
            "goals_conceded_per_match_home": "goals_conceded_per_match_home",
            "goals_conceded_per_match_away": "goals_conceded_per_match_away",
            "total_home_wins": "total_home_wins",
            "total_home_played": "total_home_played",
            "total_away_wins": "total_away_wins",
            "total_away_played": "total_away_played",
            "total_home_draws": "total_home_draws",
            "total_away_draws": "total_away_draws",
            "ppg_last_8": "ppg_last_8",
            "avg_goals_scored_last_8": "avg_goals_scored_last_8",
            "avg_goals_conceded_last_8": "avg_goals_conceded_last_8",
        },
        "h2h_stats": {
            "team_a_win_percentage": "team_a_win_percentage",
            "team_b_win_percentage": "team_b_win_percentage",
            "draw_percentage": "draw_percentage",
            "total_matches": "total_matches",
            "team_a_goals": "team_a_goals",
            "team_b_goals": "team_b_goals",
            "btts_percentage": "btts_percentage",
        }
    }

def prepare_prediction_data(prepared_data):
    """Prepare X and y data for predictions."""
    columns_to_drop = [
        "result", "three_way", "double_chance",
        "over_under_1_5", "over_under_2_5", "over_under_3_5",
        "btts", "home_goals", "away_goals", "total_goals",
        "three_way_encoded", "double_chance_encoded",
        "over_under_1_5_encoded", "over_under_2_5_encoded",
        "over_under_3_5_encoded", "btts_encoded",
        "form_data_valid_str",
    ]

    X = prepared_data.drop(
        [col for col in columns_to_drop if col in prepared_data.columns],
        axis=1
    )

    # Build y_dict only with columns that exist
    y_dict = {}
    target_columns = {
        "three_way": "three_way",
        "double_chance": "double_chance",
        "over_under_1_5": "over_under_1_5",
        "over_under_2_5": "over_under_2_5",
        "over_under_3_5": "over_under_3_5",
        "btts": "btts",
    }

    for key, col_name in target_columns.items():
        if col_name in prepared_data.columns:
            y_dict[key] = prepared_data[col_name]

    return X, y_dict

@app.route('/api/betting-odds/<league_name>/<team1>/<team2>')
def api_betting_odds(league_name, team1, team2):
    """
    API endpoint for fetching live betting odds.
    Currently returns placeholder data - to be implemented with real betting API.
    """
    try:
        # TODO: Implement real betting odds API integration
        # For now, return that no odds are available
        return jsonify({
            'available': False,
            'message': 'Betting odds integration not yet implemented',
            'supported_leagues': [],
            'next_steps': [
                'Integrate with The Odds API (odds-api.com)',
                'Add API key configuration',
                'Implement odds caching system',
                'Add multiple bookmaker support'
            ]
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Check if database exists
    if not os.path.exists(db_path):
        print(f"Database not found: {db_path}")
        print("Please run: python create_database.py")
        exit(1)
    
    print("Starting Football Database Web Interface...")
    print(f"Database: {db_path}")
    print("Access the web interface at: http://localhost:5010")
    
    app.run(debug=True, host='0.0.0.0', port=5010)