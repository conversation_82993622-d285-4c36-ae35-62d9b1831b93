{% extends "base.html" %}

{% block title %}Leagues - Football Database{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-trophy me-2"></i>Football Leagues</h1>
        <p class="lead">Browse all {{ leagues|length }} leagues in our database</p>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" class="form-control" id="leagueSearch" placeholder="Search leagues...">
        </div>
    </div>
    <div class="col-md-6">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-primary active" onclick="showAll()">All</button>
            <button type="button" class="btn btn-outline-primary" onclick="filterByRegion('ENGLAND')">England</button>
            <button type="button" class="btn btn-outline-primary" onclick="filterByRegion('SPAIN')">Spain</button>
            <button type="button" class="btn btn-outline-primary" onclick="filterByRegion('GERMANY')">Germany</button>
            <button type="button" class="btn btn-outline-primary" onclick="filterByRegion('ITALY')">Italy</button>
        </div>
    </div>
</div>

<!-- Leagues Grid -->
<div class="row" id="leaguesContainer">
    {% for league in leagues %}
    <div class="col-md-6 col-lg-4 mb-3 league-card" data-league="{{ league.league_name }}">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-trophy me-2 text-primary"></i>
                    {{ league.league_name.replace('_', ' ').title() }}
                </h5>
                <p class="card-text text-muted">
                    <small>
                        <i class="fas fa-calendar me-1"></i>
                        Created: {{ league.created_at.split(' ')[0] if league.created_at else 'N/A' }}
                    </small>
                </p>
                <div class="d-grid">
                    <a href="{{ url_for('league_detail', league_name=league.league_name) }}" 
                       class="btn btn-primary">
                        <i class="fas fa-eye me-2"></i>View Details
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- No results message -->
<div id="noResults" class="text-center py-5" style="display: none;">
    <i class="fas fa-search fa-3x text-muted mb-3"></i>
    <h4 class="text-muted">No leagues found</h4>
    <p class="text-muted">Try adjusting your search criteria</p>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
// League search functionality
document.getElementById('leagueSearch').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    filterLeagues(searchTerm);
});

function filterLeagues(searchTerm = '', region = '') {
    const cards = document.querySelectorAll('.league-card');
    let visibleCount = 0;
    
    cards.forEach(card => {
        const leagueName = card.dataset.league.toLowerCase();
        const matchesSearch = searchTerm === '' || leagueName.includes(searchTerm);
        const matchesRegion = region === '' || leagueName.includes(region.toLowerCase());
        
        if (matchesSearch && matchesRegion) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });
    
    // Show/hide no results message
    const noResults = document.getElementById('noResults');
    if (visibleCount === 0) {
        noResults.style.display = 'block';
    } else {
        noResults.style.display = 'none';
    }
}

function filterByRegion(region) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Clear search
    document.getElementById('leagueSearch').value = '';
    
    // Filter leagues
    if (region === 'All') {
        filterLeagues('', '');
    } else {
        filterLeagues('', region);
    }
}

function showAll() {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Clear search and show all
    document.getElementById('leagueSearch').value = '';
    filterLeagues('', '');
}
</script>
{% endblock %}