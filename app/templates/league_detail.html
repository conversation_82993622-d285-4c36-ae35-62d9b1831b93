{% extends "base_with_sidebar.html" %}

{% block title %}{{ league_name.replace('_', ' ').title() }} - Football Database{% endblock %}

{% block content %}
<!-- League Header -->
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('leagues') }}">Leagues</a></li>
                <li class="breadcrumb-item active">{{ league_name.replace('_', ' ').title() }}</li>
            </ol>
        </nav>
        
        <h1><i class="fas fa-trophy me-2"></i>{{ league_name.replace('_', ' ').title() }}</h1>
    </div>
</div>

<!-- League Statistics Cards -->
{% if stats and stats|length > 0 %}
<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-futbol fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(stats.get('avg_goals_per_match', 0)) }}</h4>
                <p class="card-text">Avg Goals/Match</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-home fa-2x mb-2"></i>
                <h4>{{ "%.0f"|format(stats.get('home_win_percentage', 0)) }}%</h4>
                <p class="card-text">Home Wins</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-plane fa-2x mb-2"></i>
                <h4>{{ "%.0f"|format(stats.get('away_win_percentage', 0)) }}%</h4>
                <p class="card-text">Away Wins</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h4>{{ "%.0f"|format(stats.get('both_teams_scored_percentage', 0)) }}%</h4>
                <p class="card-text">BTTS</p>
            </div>
        </div>
    </div>
</div>
{% else %}
<!-- Show basic info even if detailed stats are missing -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            League statistics are being loaded. Available data will be shown in the tabs below.
        </div>
    </div>
</div>
{% endif %}

<!-- Main Content Tabs -->
<ul class="nav nav-tabs" id="leagueTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="table-tab" data-bs-toggle="tab" data-bs-target="#table" type="button">
            <i class="fas fa-table me-2"></i>League Table
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="scorers-tab" data-bs-toggle="tab" data-bs-target="#scorers" type="button">
            <i class="fas fa-futbol me-2"></i>Top Scorers
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="matches-tab" data-bs-toggle="tab" data-bs-target="#matches" type="button">
            <i class="fas fa-calendar me-2"></i>Recent Matches
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="compare-tab" data-bs-toggle="tab" data-bs-target="#compare" type="button">
            <i class="fas fa-balance-scale me-2"></i>Compare Teams
        </button>
    </li>
</ul>

<div class="tab-content" id="leagueTabsContent">
    <!-- League Table Tab -->
    <div class="tab-pane fade show active" id="table" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-table me-2"></i>League Table</h5>
            </div>
            <div class="card-body">
                {% if table %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Pos</th>
                                <th>Team</th>
                                <th>MP</th>
                                <th>W</th>
                                <th>D</th>
                                <th>L</th>
                                <th>GF</th>
                                <th>GA</th>
                                <th>GD</th>
                                <th>Pts</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for team in table %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ team.position }}</span>
                                </td>
                                <td>
                                    <strong>{{ team.team_name }}</strong>
                                </td>
                                <td>{{ team.MP }}</td>
                                <td class="text-success">{{ team.W }}</td>
                                <td class="text-warning">{{ team.D }}</td>
                                <td class="text-danger">{{ team.L }}</td>
                                <td>{{ team.GF }}</td>
                                <td>{{ team.GA }}</td>
                                <td class="{% if team.GD > 0 %}text-success{% elif team.GD < 0 %}text-danger{% endif %}">
                                    {{ team.GD }}
                                </td>
                                <td><strong>{{ team.Pts }}</strong></td>
                                <td>
                                    <a href="{{ url_for('team_detail', league_name=league_name, team_name=team.team_name) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No league table data available</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Top Scorers Tab -->
    <div class="tab-pane fade" id="scorers" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-futbol me-2"></i>Top Scoring Teams</h5>
            </div>
            <div class="card-body">
                {% if top_scorers %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Rank</th>
                                <th>Team</th>
                                <th>Total Goals</th>
                                <th>Goals/Match</th>
                                <th>Matches Played</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for team in top_scorers %}
                            <tr>
                                <td>
                                    <span class="badge bg-warning">{{ loop.index }}</span>
                                </td>
                                <td><strong>{{ team.team_name }}</strong></td>
                                <td class="text-success">{{ team.goals_scored_all }}</td>
                                <td>{{ "%.2f"|format(team.goals_scored_per_match_all) }}</td>
                                <td>{{ team.total_played }}</td>
                                <td>
                                    <a href="{{ url_for('team_detail', league_name=league_name, team_name=team.team_name) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No scoring data available</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Matches Tab -->
    <div class="tab-pane fade" id="matches" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-calendar me-2"></i>Recent Matches</h5>
            </div>
            <div class="card-body">
                {% if recent_matches %}
                <div class="row">
                    {% for match in recent_matches %}
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-4 text-end">
                                        <strong>{{ match.home_team }}</strong>
                                    </div>
                                    <div class="col-4 text-center">
                                        {% if match.home_score is not none and match.away_score is not none %}
                                        <span class="badge bg-primary fs-6">
                                            {{ match.home_score }} - {{ match.away_score }}
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">TBD</span>
                                        {% endif %}
                                    </div>
                                    <div class="col-4">
                                        <strong>{{ match.away_team }}</strong>
                                    </div>
                                </div>
                                <div class="text-center mt-2">
                                    <small class="text-muted">
                                        <i class=\"fas fa-calendar me-1\"></i>{{ match.match_date|format_date }}\n
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">No recent matches available</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Compare Teams Tab -->
    <div class="tab-pane fade" id="compare" role="tabpanel">
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-balance-scale me-2"></i>Compare Teams</h5>
            </div>
            <div class="card-body">
                <form id="compareForm">
                    <div class="row">
                        <div class="col-md-5">
                            <label for="team1" class="form-label">Team 1</label>
                            <select class="form-select" id="team1" name="team1">
                                <option value="">Select Team 1</option>
                                {% for team in teams %}
                                <option value="{{ team }}">{{ team }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2 text-center d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-balance-scale"></i> Compare
                            </button>
                        </div>
                        <div class="col-md-5">
                            <label for="team2" class="form-label">Team 2</label>
                            <select class="form-select" id="team2" name="team2">
                                <option value="">Select Team 2</option>
                                {% for team in teams %}
                                <option value="{{ team }}">{{ team }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </form>
                
                <div class="mt-3 text-center text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    Select two teams to view their head-to-head statistics
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
document.getElementById('compareForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const team1 = document.getElementById('team1').value;
    const team2 = document.getElementById('team2').value;
    
    if (team1 && team2 && team1 !== team2) {
        window.location.href = `/h2h/{{ league_name }}/${encodeURIComponent(team1)}/${encodeURIComponent(team2)}`;
    } else {
        alert('Please select two different teams');
    }
});
</script>
{% endblock %}