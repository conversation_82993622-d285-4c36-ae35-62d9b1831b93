{% extends "base_with_team_sidebar.html" %}

{% block title %}{{ team1 }} vs {{ team2 }} - Head-to-Head{% endblock %}

{% block extra_head %}
<style>
.prediction-section {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.prediction-visual {
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
}

.prediction-summary-card {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.prediction-probability {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 5px;
}

.probability-bar {
    height: 6px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    margin-top: 0.25rem;
}

.confidence-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.confidence-high { background: linear-gradient(45deg, #00b894, #00cec9); color: white; }
.confidence-medium { background: linear-gradient(45deg, #fdcb6e, #e17055); color: white; }
.confidence-low { background: linear-gradient(45deg, #fd79a8, #e84393); color: white; }

.risk-low { background: linear-gradient(45deg, #00b894, #00cec9); color: white; }
.risk-medium { background: linear-gradient(45deg, #fdcb6e, #e17055); color: white; }
.risk-high { background: linear-gradient(45deg, #fd79a8, #e84393); color: white; }

.prediction-match-header {
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.prediction-results-show {
    animation: fadeInUp 0.6s ease;
}
</style>
{% endblock %}

{% block content %}
<!-- H2H Header -->
<div class="row mb-4">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-3">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('leagues') }}">Leagues</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('league_detail', league_name=league_name) }}">{{ league_name.replace('_', ' ').title() }}</a></li>
                <li class="breadcrumb-item active">{{ team1 }} vs {{ team2 }}</li>
            </ol>
        </nav>
        
        <div class="text-center">
            <h2 class="mb-2">
                <span class="text-primary">{{ team1 }}</span>
                <i class="fas fa-balance-scale mx-3 text-muted"></i>
                <span class="text-danger">{{ team2 }}</span>
            </h2>
            <p class="lead text-muted">Head-to-Head Analysis</p>
        </div>
    </div>
</div>

{% if h2h and h2h|length > 0 %}
<!-- H2H Summary Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-info text-white h2h-summary-card">
            <div class="card-body text-center py-3">
                <i class="fas fa-futbol fa-lg mb-2"></i>
                <h4 class="mb-1">{{ h2h.total_matches }}</h4>
                <p class="card-text mb-0">Total Matches</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-primary text-white h2h-summary-card">
            <div class="card-body text-center py-3">
                <i class="fas fa-trophy fa-lg mb-2"></i>
                <h4 class="mb-1">{{ "%.1f"|format(h2h.home_win_percentage) }}%</h4>
                <p class="card-text mb-0">{{ h2h.home_team }} Wins</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-danger text-white h2h-summary-card">
            <div class="card-body text-center py-3">
                <i class="fas fa-trophy fa-lg mb-2"></i>
                <h4 class="mb-1">{{ "%.1f"|format(h2h.away_win_percentage) }}%</h4>
                <p class="card-text mb-0">{{ h2h.away_team }} Wins</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-warning text-white h2h-summary-card">
            <div class="card-body text-center py-3">
                <i class="fas fa-handshake fa-lg mb-2"></i>
                <h4 class="mb-1">{{ "%.1f"|format(h2h.draw_percentage) }}%</h4>
                <p class="card-text mb-0">Draws</p>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Statistics -->
<div class="row">
    <!-- Win/Loss Record with Pie Chart -->
    <div class="col-lg-6 mb-4">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Match Results</h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-4">
                        <div class="p-3 bg-primary text-white rounded">
                            <h5 class="mb-1">{{ h2h.home_wins }}</h5>
                            <small>{{ h2h.home_team }} Wins</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="p-3 bg-warning text-white rounded">
                            <h5 class="mb-1">{{ h2h.draws }}</h5>
                            <small>Draws</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="p-3 bg-danger text-white rounded">
                            <h5 class="mb-1">{{ h2h.away_wins }}</h5>
                            <small>{{ h2h.away_team }} Wins</small>
                        </div>
                    </div>
                </div>
                
                <!-- Win Percentage Chart -->
                <div class="chart-container">
                    <canvas id="winChart" width="400" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Goals & Betting Statistics -->
    <div class="col-lg-6 mb-4">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Goals & Historical Trends</h5>
            </div>
            <div class="card-body">
                <h6 class="text-primary mb-3"><i class="fas fa-futbol me-2"></i>Goal Statistics</h6>
                <table class="table table-sm mb-3">
                    <tr>
                        <td><i class="fas fa-futbol me-2 text-primary"></i>{{ h2h.home_team }} Goals:</td>
                        <td class="text-end"><strong class="text-primary">{{ h2h.home_goals }}</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-futbol me-2 text-danger"></i>{{ h2h.away_team }} Goals:</td>
                        <td class="text-end"><strong class="text-danger">{{ h2h.away_goals }}</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-chart-line me-2"></i>Avg Goals/Match:</td>
                        <td class="text-end"><strong>{{ "%.2f"|format((h2h.home_goals + h2h.away_goals) / h2h.total_matches if h2h.total_matches > 0 else 0) }}</strong></td>
                    </tr>
                </table>
                
                <h6 class="text-warning mb-3"><i class="fas fa-chart-line me-2"></i>Historical Trends</h6>
                <table class="table table-sm mb-0">
                    <tr>
                        <td><i class="fas fa-exchange-alt me-2 text-success"></i>Both Teams Scored:</td>
                        <td class="text-end"><strong class="text-success">{{ "%.1f"|format(h2h.btts_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-arrow-up me-2"></i>Over 1.5 Goals:</td>
                        <td class="text-end"><strong>{{ "%.1f"|format(h2h.over_1_5_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-arrow-up me-2"></i>Over 2.5 Goals:</td>
                        <td class="text-end"><strong>{{ "%.1f"|format(h2h.over_2_5_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-arrow-up me-2"></i>Over 3.5 Goals:</td>
                        <td class="text-end"><strong>{{ "%.1f"|format(h2h.over_3_5_percentage) }}%</strong></td>
                    </tr>
                </table>
                <div class="text-center mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Based on {{ h2h.total_matches }} historical matches
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Betting Markets & Analysis Row -->
<div class="row">
    <!-- Live Betting Markets -->
    <div class="col-lg-6 mb-4">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Live Betting Markets</h5>
            </div>
            <div class="card-body">
                {% if betting_odds %}
                    <h6 class="text-success mb-3"><i class="fas fa-trophy me-2"></i>Match Result</h6>
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <div class="odds-card home-odds">
                                <div class="odds-value">{{ betting_odds.home_win_odds }}</div>
                                <small>{{ team1 }} Win</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="odds-card draw-odds">
                                <div class="odds-value">{{ betting_odds.draw_odds }}</div>
                                <small>Draw</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="odds-card away-odds">
                                <div class="odds-value">{{ betting_odds.away_win_odds }}</div>
                                <small>{{ team2 }} Win</small>
                            </div>
                        </div>
                    </div>
                    
                    <h6 class="text-warning mb-3"><i class="fas fa-futbol me-2"></i>Goals Markets</h6>
                    <table class="table table-sm mb-0">
                        <tr>
                            <td><i class="fas fa-arrow-up me-2"></i>Over 2.5 Goals:</td>
                            <td class="text-end"><strong>{{ betting_odds.over_2_5_odds }}</strong></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-arrow-down me-2"></i>Under 2.5 Goals:</td>
                            <td class="text-end"><strong>{{ betting_odds.under_2_5_odds }}</strong></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-exchange-alt me-2"></i>Both Teams Score:</td>
                            <td class="text-end"><strong>{{ betting_odds.btts_yes_odds }}</strong></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-times me-2"></i>BTTS No:</td>
                            <td class="text-end"><strong>{{ betting_odds.btts_no_odds }}</strong></td>
                        </tr>
                    </table>
                    
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Last updated: {{ betting_odds.last_updated }}
                        </small>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-circle fa-2x text-muted mb-3"></i>
                        <h6 class="text-muted">Betting Markets Unavailable</h6>
                        <p class="text-muted small mb-3">
                            Live betting odds are not available for this matchup.<br>
                            This could be because:
                        </p>
                        <ul class="text-muted small text-start">
                            <li>No upcoming fixture scheduled</li>
                            <li>Match is too far in the future</li>
                            <li>Limited betting interest for this league</li>
                            <li>Betting markets not yet opened</li>
                        </ul>
                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshBettingOdds()">
                                <i class="fas fa-sync-alt me-1"></i>Check for Updates
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Clean Sheets & Season Comparison -->
    <div class="col-lg-6 mb-4">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Season Analysis</h5>
            </div>
            <div class="card-body">
                <h6 class="text-info mb-3"><i class="fas fa-shield-alt me-2"></i>Defensive Records</h6>
                <table class="table table-sm mb-3">
                    <tr>
                        <td><i class="fas fa-shield-alt me-2 text-primary"></i>{{ h2h.home_team }} Clean Sheets:</td>
                        <td class="text-end"><strong class="text-primary">{{ "%.1f"|format(h2h.home_clean_sheet_percentage) }}%</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-shield-alt me-2 text-danger"></i>{{ h2h.away_team }} Clean Sheets:</td>
                        <td class="text-end"><strong class="text-danger">{{ "%.1f"|format(h2h.away_clean_sheet_percentage) }}%</strong></td>
                    </tr>
                </table>
                
                <h6 class="text-info mb-3"><i class="fas fa-balance-scale me-2"></i>Current Form</h6>
                <div class="row">
                    <div class="col-6">
                        <h6 class="text-primary mb-2 small">{{ team1 }}</h6>
                        {% if team1_stats %}
                        <table class="table table-sm table-borderless mb-0 small">
                            <tr>
                                <td>Goals/Match:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team1_stats.goals_scored_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Conceded/Match:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team1_stats.goals_conceded_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Points/Game:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team1_stats.points_per_game) }}</strong></td>
                            </tr>
                        </table>
                        {% else %}
                        <p class="text-muted small">No data</p>
                        {% endif %}
                    </div>
                    <div class="col-6">
                        <h6 class="text-danger mb-2 small">{{ team2 }}</h6>
                        {% if team2_stats %}
                        <table class="table table-sm table-borderless mb-0 small">
                            <tr>
                                <td>Goals/Match:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team2_stats.goals_scored_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Conceded/Match:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team2_stats.goals_conceded_per_match_all) }}</strong></td>
                            </tr>
                            <tr>
                                <td>Points/Game:</td>
                                <td class="text-end"><strong>{{ "%.2f"|format(team2_stats.points_per_game) }}</strong></td>
                            </tr>
                        </table>
                        {% else %}
                        <p class="text-muted small">No data</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- Recent Results -->
{% if h2h.recent_results %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card h2h-balanced-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Head-to-Head Results</h5>
            </div>
            <div class="card-body">
                <div class="recent-results-container">
                    {% set results_list = h2h.recent_results.split(' | ') %}
                    {% if results_list|length > 0 and results_list[0].strip() %}
                        <div class="row">
                            {% for result in results_list %}
                                {% if result.strip() %}
                                    {% set match_parts = result.strip().split(': ') %}
                                    {% if match_parts|length >= 2 %}
                                        {% set date_part = match_parts[0] %}
                                        {% set match_part = match_parts[1] %}
                                        {% set vs_parts = match_part.split(' vs ') %}
                                        {% if vs_parts|length == 2 %}
                                            {% set team1_part = vs_parts[0] %}
                                            {% set team2_part = vs_parts[1] %}
                                            
                                            <!-- Extract scores using regex-like logic -->
                                            {% set team1_score = team1_part[-1] if team1_part[-1].isdigit() else '0' %}
                                            {% set team2_score = team2_part[-1] if team2_part[-1].isdigit() else '0' %}
                                            {% set team1_name = team1_part[:-1] if team1_part[-1].isdigit() else team1_part %}
                                            {% set team2_name = team2_part[:-1] if team2_part[-1].isdigit() else team2_part %}
                                            
                                            <div class="col-lg-6 col-md-12 mb-3">
                                                <div class="recent-match-card">
                                                    <div class="match-date-header">
                                                        <i class="fas fa-calendar-alt me-1"></i>
                                                        <small class="text-muted">{{ date_part }}</small>
                                                    </div>
                                                    <div class="match-teams">
                                                        <div class="team-score">
                                                            <span class="team-name {% if team1_name.strip() == team1 %}text-primary{% else %}text-danger{% endif %}">
                                                                {{ team1_name.strip() }}
                                                            </span>
                                                            <span class="score">{{ team1_score }}</span>
                                                        </div>
                                                        <div class="vs-divider">
                                                            <i class="fas fa-circle"></i>
                                                        </div>
                                                        <div class="team-score">
                                                            <span class="score">{{ team2_score }}</span>
                                                            <span class="team-name {% if team2_name.strip() == team2 %}text-danger{% else %}text-primary{% endif %}">
                                                                {{ team2_name.strip() }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="match-result">
                                                        {% set score1 = team1_score|int %}
                                                        {% set score2 = team2_score|int %}
                                                        {% if score1 > score2 %}
                                                            <span class="result-badge win">{{ team1_name.strip() }} Win</span>
                                                        {% elif score2 > score1 %}
                                                            <span class="result-badge loss">{{ team2_name.strip() }} Win</span>
                                                        {% else %}
                                                            <span class="result-badge draw">Draw</span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        </div>
                    {% else %}
                        <!-- Fallback for unstructured data -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Recent Results:</strong> {{ h2h.recent_results }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Showing most recent head-to-head encounters between these teams
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Match Prediction Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card prediction-section">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-crystal-ball me-2"></i>AI Match Prediction
                    <small class="float-end">
                        <span class="badge bg-light text-dark">Powered by ML</span>
                    </small>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p class="text-muted mb-3">
                            <i class="fas fa-info-circle me-1"></i>
                            Get AI-powered predictions for this matchup using advanced machine learning models trained on team statistics, head-to-head records, and recent form.
                        </p>

                        <button id="generatePredictionBtn" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic me-2"></i>Generate Match Prediction
                        </button>

                        <div id="predictionLoading" class="text-center mt-3" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Analyzing teams and generating predictions...</p>
                        </div>
                    </div>

                    <div class="col-md-4 text-center">
                        <div class="prediction-visual">
                            <i class="fas fa-chart-line fa-3x text-muted mb-2"></i>
                            <p class="text-muted small">Advanced Analytics</p>
                        </div>
                    </div>
                </div>

                <!-- Prediction Results (Hidden initially) -->
                <div id="predictionResults" style="display: none;">
                    <hr class="my-4">

                    <!-- Match Header -->
                    <div class="prediction-match-header text-center mb-4">
                        <h4 id="predictionMatchTitle" class="text-primary"></h4>
                        <p class="text-muted" id="predictionLeague"></p>
                    </div>

                    <!-- Quick Summary -->
                    <div class="row mb-4">
                        <div class="col-md-4 text-center">
                            <div class="prediction-summary-card">
                                <h6 class="text-muted">Predicted Winner</h6>
                                <h4 id="predictedWinner" class="text-primary"></h4>
                                <small id="winnerConfidence" class="text-muted"></small>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="prediction-summary-card">
                                <h6 class="text-muted">Expected Goals</h6>
                                <h4 id="expectedGoalsDisplay" class="text-success"></h4>
                                <small class="text-muted">Total Match Goals</small>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="prediction-summary-card">
                                <h6 class="text-muted">Confidence Level</h6>
                                <h4 id="overallConfidenceDisplay" class="text-warning"></h4>
                                <small id="riskLevelDisplay" class="text-muted"></small>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Predictions -->
                    <div class="row">
                        <div class="col-lg-6 mb-3">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-trophy me-1"></i>Match Result (1X2)</h6>
                                </div>
                                <div class="card-body">
                                    <div id="threeWayPrediction"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 mb-3">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-chart-line me-1"></i>Over/Under 2.5 Goals</h6>
                                </div>
                                <div class="card-body">
                                    <div id="ou25Prediction"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 mb-3">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-bullseye me-1"></i>Both Teams to Score</h6>
                                </div>
                                <div class="card-body">
                                    <div id="bttsPrediction"></div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 mb-3">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-shield-alt me-1"></i>Double Chance</h6>
                                </div>
                                <div class="card-body">
                                    <div id="dcPrediction"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Analysis -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb me-1"></i>Prediction Insights</h6>
                                <div id="predictionInsights">
                                    <p class="mb-1"><strong>Expected Goals Analysis:</strong> <span id="xgAnalysis"></span></p>
                                    <p class="mb-1"><strong>Head-to-Head Factor:</strong> <span id="h2hFactor"></span></p>
                                    <p class="mb-0"><strong>Confidence Assessment:</strong> <span id="confidenceAssessment"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- View Full Analysis Link -->
                    <div class="text-center mt-3">
                        <a href="{{ url_for('predictions') }}" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt me-1"></i>View Full Prediction Analysis
                        </a>
                    </div>
                </div>

                <!-- Error Message -->
                <div id="predictionError" class="alert alert-danger mt-3" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="predictionErrorText"></span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- No H2H Data -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-warning text-center">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h4>No Head-to-Head Data Available</h4>
            <p>We don't have historical matchup data for {{ team1 }} vs {{ team2 }} in {{ league_name.replace('_', ' ').title() }}.</p>
            <a href="{{ url_for('league_detail', league_name=league_name) }}" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to League
            </a>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_scripts %}
{% if h2h %}
<script>
// Win Percentage Pie Chart
const winCtx = document.getElementById('winChart').getContext('2d');
new Chart(winCtx, {
    type: 'doughnut',
    data: {
        labels: ['{{ h2h.home_team }}', 'Draw', '{{ h2h.away_team }}'],
        datasets: [{
            data: [{{ h2h.home_win_percentage }}, {{ h2h.draw_percentage }}, {{ h2h.away_win_percentage }}],
            backgroundColor: ['#0d6efd', '#ffc107', '#dc3545'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 15,
                    usePointStyle: true
                }
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ' + context.parsed + '%';
                    }
                }
            }
        }
    }
});

// Betting odds refresh function
function refreshBettingOdds() {
    const button = event.target;
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Checking...';
    button.disabled = true;

    // Make API call to check for betting odds
    fetch(`/api/betting-odds/{{ league_name }}/{{ team1 }}/{{ team2 }}`)
        .then(response => response.json())
        .then(data => {
            if (data.available) {
                // Reload the page to show new odds
                location.reload();
            } else {
                // Show message that no odds are available
                button.innerHTML = '<i class="fas fa-times me-1"></i>Not Available';
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }, 3000);
            }
        })
        .catch(error => {
            console.error('Error checking betting odds:', error);
            button.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Error';
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 3000);
        });
}

// Prediction functionality
document.addEventListener('DOMContentLoaded', function() {
    const generateBtn = document.getElementById('generatePredictionBtn');
    const loadingDiv = document.getElementById('predictionLoading');
    const resultsDiv = document.getElementById('predictionResults');
    const errorDiv = document.getElementById('predictionError');

    if (generateBtn) {
        generateBtn.addEventListener('click', function() {
            generatePrediction();
        });
    }
});

function generatePrediction() {
    const generateBtn = document.getElementById('generatePredictionBtn');
    const loadingDiv = document.getElementById('predictionLoading');
    const resultsDiv = document.getElementById('predictionResults');
    const errorDiv = document.getElementById('predictionError');

    // Hide previous results and errors
    resultsDiv.style.display = 'none';
    errorDiv.style.display = 'none';

    // Show loading state
    generateBtn.style.display = 'none';
    loadingDiv.style.display = 'block';

    // Make prediction request
    fetch('/api/predict', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            league: '{{ league_name }}',
            home_team: '{{ team1 }}',
            away_team: '{{ team2 }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        loadingDiv.style.display = 'none';
        generateBtn.style.display = 'inline-block';

        if (data.success) {
            displayPredictionResults(data);
        } else {
            showPredictionError(data.error || 'Prediction failed');
        }
    })
    .catch(error => {
        loadingDiv.style.display = 'none';
        generateBtn.style.display = 'inline-block';
        console.error('Error generating prediction:', error);
        showPredictionError('Failed to generate prediction. Please try again.');
    });
}

function displayPredictionResults(data) {
    const resultsDiv = document.getElementById('predictionResults');

    // Update match header
    document.getElementById('predictionMatchTitle').textContent = data.match;
    document.getElementById('predictionLeague').textContent = data.league.replace('_', ' ').toUpperCase();

    // Update quick summary
    const threeWay = data.predictions.three_way;
    if (threeWay) {
        document.getElementById('predictedWinner').textContent = threeWay.prediction;
        const confidence = Math.max(...Object.values(threeWay.probabilities)) * 100;
        document.getElementById('winnerConfidence').textContent = `${confidence.toFixed(1)}% confidence`;
    }

    // Expected goals
    const homeXG = data.expected_goals.home || 0;
    const awayXG = data.expected_goals.away || 0;
    const totalXG = homeXG + awayXG;
    document.getElementById('expectedGoalsDisplay').textContent = `${homeXG.toFixed(1)} - ${awayXG.toFixed(1)}`;

    // Overall confidence
    const overallConf = data.confidence_analysis.three_way?.prediction_strength || 'Medium';
    document.getElementById('overallConfidenceDisplay').textContent = overallConf;
    const riskLevel = data.risk_assessment.overall?.risk_level || 'Medium';
    document.getElementById('riskLevelDisplay').textContent = `${riskLevel} Risk`;

    // Update detailed predictions
    updateMarketPrediction('threeWay', data.predictions.three_way);
    updateMarketPrediction('ou25', data.predictions.over_under_2_5);
    updateMarketPrediction('btts', data.predictions.btts);
    updateMarketPrediction('dc', data.predictions.double_chance);

    // Update insights
    updatePredictionInsights(data);

    // Show results with animation
    resultsDiv.style.display = 'block';
    resultsDiv.classList.add('prediction-results-show');
}

function updateMarketPrediction(prefix, prediction) {
    if (!prediction) return;

    const container = document.getElementById(prefix + 'Prediction');
    if (!container) return;

    let html = `<div class="mb-2"><strong>Prediction: ${prediction.prediction}</strong></div>`;

    if (prediction.probabilities) {
        Object.entries(prediction.probabilities).forEach(([outcome, prob]) => {
            const percentage = (prob * 100).toFixed(1);
            html += `
                <div class="prediction-probability">
                    <span>${outcome}</span>
                    <span><strong>${percentage}%</strong></span>
                </div>
                <div class="probability-bar" style="width: ${percentage}%"></div>
            `;
        });
    }

    container.innerHTML = html;
}

function updatePredictionInsights(data) {
    const homeXG = data.expected_goals.home || 0;
    const awayXG = data.expected_goals.away || 0;
    const totalXG = homeXG + awayXG;

    // XG Analysis
    let xgText = `Total expected goals: ${totalXG.toFixed(1)}. `;
    if (totalXG > 2.5) {
        xgText += "High-scoring match expected.";
    } else if (totalXG < 1.5) {
        xgText += "Low-scoring match expected.";
    } else {
        xgText += "Moderate scoring expected.";
    }
    document.getElementById('xgAnalysis').textContent = xgText;

    // H2H Factor
    document.getElementById('h2hFactor').textContent = "Historical matchup data incorporated into prediction model.";

    // Confidence Assessment
    const overallRisk = data.risk_assessment.overall?.risk_level || 'Medium';
    let confText = `Overall prediction confidence is ${overallRisk.toLowerCase()}. `;
    if (overallRisk === 'Low') {
        confText += "High confidence in predictions.";
    } else if (overallRisk === 'High') {
        confText += "Predictions have higher uncertainty.";
    } else {
        confText += "Moderate confidence in predictions.";
    }
    document.getElementById('confidenceAssessment').textContent = confText;
}

function showPredictionError(message) {
    const errorDiv = document.getElementById('predictionError');
    const errorText = document.getElementById('predictionErrorText');

    errorText.textContent = message;
    errorDiv.style.display = 'block';
}
</script>
{% endif %}
{% endblock %}