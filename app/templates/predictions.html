{% extends "base.html" %}

{% block title %}Match Predictions - Football Database{% endblock %}

{% block extra_head %}
<style>
.prediction-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.prediction-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.prediction-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 2rem;
    color: white;
    margin-bottom: 2rem;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid rgba(255,255,255,0.2);
    background: rgba(255,255,255,0.1);
    color: white;
    backdrop-filter: blur(10px);
}

.form-control:focus, .form-select:focus {
    border-color: rgba(255,255,255,0.5);
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
    background: rgba(255,255,255,0.2);
    color: white;
}

.form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.form-select option {
    background: #333;
    color: white;
}

.btn-predict {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-predict:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(238, 90, 36, 0.3);
}

.prediction-result {
    display: none;
    animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.probability-bar {
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.8s ease;
}

.confidence-badge {
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.confidence-high { background: linear-gradient(45deg, #00b894, #00cec9); }
.confidence-medium { background: linear-gradient(45deg, #fdcb6e, #e17055); }
.confidence-low { background: linear-gradient(45deg, #fd79a8, #e84393); }

.risk-badge {
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.risk-low { background: linear-gradient(45deg, #00b894, #00cec9); }
.risk-medium { background: linear-gradient(45deg, #fdcb6e, #e17055); }
.risk-high { background: linear-gradient(45deg, #fd79a8, #e84393); }

.loading-spinner {
    display: none;
    text-align: center;
    padding: 2rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.match-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
    text-align: center;
}

.vs-divider {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.prediction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.market-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.market-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.market-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.prediction-value {
    font-size: 1.3rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 1rem;
}

.probability-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.probability-label {
    font-weight: 500;
    color: #333;
}

.probability-value {
    font-weight: 600;
    color: #667eea;
}

.expected-goals {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
}

.xg-value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.correct-scores {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: rgba(255,255,255,0.2);
    border-radius: 8px;
}

.error-message {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    margin: 1rem 0;
    text-align: center;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
                <li class="breadcrumb-item active">Predictions</li>
            </ol>
        </nav>
        
        <h1><i class="fas fa-crystal-ball me-2"></i>Match Predictions</h1>
        <p class="lead">Get AI-powered predictions for football matches using advanced machine learning models.</p>
    </div>
</div>

<!-- Prediction Form -->
<div class="prediction-form">
    <div class="row">
        <div class="col-12 text-center mb-4">
            <h2><i class="fas fa-magic me-2"></i>Predict Match Outcome</h2>
            <p class="mb-0">Select a league and teams to get comprehensive match predictions</p>
        </div>
    </div>
    
    <form id="predictionForm">
        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="league" class="form-label">
                    <i class="fas fa-trophy me-1"></i>League
                </label>
                <select class="form-select" id="league" required>
                    <option value="">Select League...</option>
                    {% for league in leagues %}
                    <option value="{{ league.code }}">{{ league.name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="homeTeam" class="form-label">
                    <i class="fas fa-home me-1"></i>Home Team
                </label>
                <select class="form-select" id="homeTeam" required disabled>
                    <option value="">Select Home Team...</option>
                </select>
            </div>
            
            <div class="col-md-4 mb-3">
                <label for="awayTeam" class="form-label">
                    <i class="fas fa-plane me-1"></i>Away Team
                </label>
                <select class="form-select" id="awayTeam" required disabled>
                    <option value="">Select Away Team...</option>
                </select>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12 text-center">
                <button type="submit" class="btn btn-predict btn-lg">
                    <i class="fas fa-crystal-ball me-2"></i>Generate Prediction
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Loading Spinner -->
<div class="loading-spinner" id="loadingSpinner">
    <div class="spinner"></div>
    <h4>Generating Predictions...</h4>
    <p>Training models and analyzing match data...</p>
</div>

<!-- Error Message -->
<div id="errorMessage" class="error-message" style="display: none;">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <span id="errorText"></span>
</div>

<!-- Prediction Results -->
<div class="prediction-result" id="predictionResult">
    <!-- Match Header -->
    <div class="match-header" id="matchHeader">
        <h2 id="matchTitle"></h2>
        <p class="mb-0" id="leagueTitle"></p>
    </div>
    
    <!-- Main Predictions Grid -->
    <div class="prediction-grid" id="predictionsGrid">
        <!-- Three-Way Prediction -->
        <div class="market-card">
            <div class="market-title">
                <i class="fas fa-trophy"></i>
                Match Result (1X2)
            </div>
            <div class="prediction-value" id="threeWayPrediction"></div>
            <div id="threeWayProbs"></div>
            <div class="mt-2">
                <span class="confidence-badge" id="threeWayConfidence"></span>
                <span class="risk-badge ms-2" id="threeWayRisk"></span>
            </div>
        </div>
        
        <!-- Over/Under 2.5 -->
        <div class="market-card">
            <div class="market-title">
                <i class="fas fa-chart-line"></i>
                Over/Under 2.5 Goals
            </div>
            <div class="prediction-value" id="ou25Prediction"></div>
            <div id="ou25Probs"></div>
            <div class="mt-2">
                <span class="confidence-badge" id="ou25Confidence"></span>
                <span class="risk-badge ms-2" id="ou25Risk"></span>
            </div>
        </div>
        
        <!-- Both Teams to Score -->
        <div class="market-card">
            <div class="market-title">
                <i class="fas fa-bullseye"></i>
                Both Teams to Score
            </div>
            <div class="prediction-value" id="bttsPrediction"></div>
            <div id="bttsProbs"></div>
            <div class="mt-2">
                <span class="confidence-badge" id="bttsConfidence"></span>
                <span class="risk-badge ms-2" id="bttsRisk"></span>
            </div>
        </div>
        
        <!-- Double Chance -->
        <div class="market-card">
            <div class="market-title">
                <i class="fas fa-shield-alt"></i>
                Double Chance
            </div>
            <div class="prediction-value" id="dcPrediction"></div>
            <div id="dcProbs"></div>
            <div class="mt-2">
                <span class="confidence-badge" id="dcConfidence"></span>
                <span class="risk-badge ms-2" id="dcRisk"></span>
            </div>
        </div>
        
        <!-- Over/Under 1.5 -->
        <div class="market-card">
            <div class="market-title">
                <i class="fas fa-chart-bar"></i>
                Over/Under 1.5 Goals
            </div>
            <div class="prediction-value" id="ou15Prediction"></div>
            <div id="ou15Probs"></div>
            <div class="mt-2">
                <span class="confidence-badge" id="ou15Confidence"></span>
                <span class="risk-badge ms-2" id="ou15Risk"></span>
            </div>
        </div>
        
        <!-- Over/Under 3.5 -->
        <div class="market-card">
            <div class="market-title">
                <i class="fas fa-chart-area"></i>
                Over/Under 3.5 Goals
            </div>
            <div class="prediction-value" id="ou35Prediction"></div>
            <div id="ou35Probs"></div>
            <div class="mt-2">
                <span class="confidence-badge" id="ou35Confidence"></span>
                <span class="risk-badge ms-2" id="ou35Risk"></span>
            </div>
        </div>
    </div>
    
    <!-- Expected Goals and Correct Scores -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="expected-goals">
                <h4><i class="fas fa-crosshairs me-2"></i>Expected Goals</h4>
                <div class="row">
                    <div class="col-6">
                        <div id="homeTeamName">Home</div>
                        <div class="xg-value" id="homeXG">0.0</div>
                    </div>
                    <div class="col-6">
                        <div id="awayTeamName">Away</div>
                        <div class="xg-value" id="awayXG">0.0</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="correct-scores">
                <h4><i class="fas fa-target me-2"></i>Most Likely Scores</h4>
                <div id="correctScores"></div>
            </div>
        </div>
    </div>
    
    <!-- Overall Analysis -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-analytics me-2"></i>Prediction Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Overall Confidence</h6>
                            <span class="confidence-badge" id="overallConfidence"></span>
                        </div>
                        <div class="col-md-6">
                            <h6>Overall Risk Level</h6>
                            <span class="risk-badge" id="overallRisk"></span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Predictions are based on advanced machine learning models analyzing team statistics, 
                            head-to-head records, recent form, and league performance data.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const leagueSelect = document.getElementById('league');
    const homeTeamSelect = document.getElementById('homeTeam');
    const awayTeamSelect = document.getElementById('awayTeam');
    const predictionForm = document.getElementById('predictionForm');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const errorMessage = document.getElementById('errorMessage');
    const predictionResult = document.getElementById('predictionResult');
    
    // Load teams when league is selected
    leagueSelect.addEventListener('change', function() {
        const league = this.value;
        if (league) {
            loadTeams(league);
        } else {
            homeTeamSelect.disabled = true;
            awayTeamSelect.disabled = true;
            homeTeamSelect.innerHTML = '<option value="">Select Home Team...</option>';
            awayTeamSelect.innerHTML = '<option value="">Select Away Team...</option>';
        }
    });
    
    // Update away team options when home team is selected
    homeTeamSelect.addEventListener('change', function() {
        updateAwayTeamOptions();
    });
    
    // Handle form submission
    predictionForm.addEventListener('submit', function(e) {
        e.preventDefault();
        makePrediction();
    });
    
    function loadTeams(league) {
        fetch(`/api/teams/${league}`)
            .then(response => response.json())
            .then(teams => {
                homeTeamSelect.innerHTML = '<option value="">Select Home Team...</option>';
                awayTeamSelect.innerHTML = '<option value="">Select Away Team...</option>';
                
                teams.forEach(team => {
                    homeTeamSelect.innerHTML += `<option value="${team}">${team}</option>`;
                });
                
                homeTeamSelect.disabled = false;
                awayTeamSelect.disabled = false;
            })
            .catch(error => {
                console.error('Error loading teams:', error);
                showError('Failed to load teams for selected league');
            });
    }
    
    function updateAwayTeamOptions() {
        const homeTeam = homeTeamSelect.value;
        const league = leagueSelect.value;
        
        if (!league) return;
        
        fetch(`/api/teams/${league}`)
            .then(response => response.json())
            .then(teams => {
                awayTeamSelect.innerHTML = '<option value="">Select Away Team...</option>';
                
                teams.forEach(team => {
                    if (team !== homeTeam) {
                        awayTeamSelect.innerHTML += `<option value="${team}">${team}</option>`;
                    }
                });
            })
            .catch(error => {
                console.error('Error updating away teams:', error);
            });
    }
    
    function makePrediction() {
        const league = leagueSelect.value;
        const homeTeam = homeTeamSelect.value;
        const awayTeam = awayTeamSelect.value;
        
        if (!league || !homeTeam || !awayTeam) {
            showError('Please select league and both teams');
            return;
        }
        
        // Show loading
        hideError();
        hidePredictionResult();
        showLoading();
        
        // Make prediction request
        fetch('/api/predict', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                league: league,
                home_team: homeTeam,
                away_team: awayTeam
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                displayPrediction(data);
            } else {
                showError(data.error || 'Prediction failed');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error making prediction:', error);
            showError('Failed to generate prediction. Please try again.');
        });
    }
    
    function displayPrediction(data) {
        // Update match header
        document.getElementById('matchTitle').textContent = data.match;
        document.getElementById('leagueTitle').textContent = data.league.replace('_', ' ').toUpperCase();
        
        // Update predictions
        updateMarketPrediction('threeWay', data.predictions.three_way, data.confidence_analysis.three_way, data.risk_assessment.three_way);
        updateMarketPrediction('ou25', data.predictions.over_under_2_5, data.confidence_analysis.over_under_2_5, data.risk_assessment.over_under_2_5);
        updateMarketPrediction('btts', data.predictions.btts, data.confidence_analysis.btts, data.risk_assessment.btts);
        updateMarketPrediction('dc', data.predictions.double_chance, data.confidence_analysis.double_chance, data.risk_assessment.double_chance);
        updateMarketPrediction('ou15', data.predictions.over_under_1_5, data.confidence_analysis.over_under_1_5, data.risk_assessment.over_under_1_5);
        updateMarketPrediction('ou35', data.predictions.over_under_3_5, data.confidence_analysis.over_under_3_5, data.risk_assessment.over_under_3_5);
        
        // Update expected goals
        const homeTeam = data.match.split(' vs ')[0];
        const awayTeam = data.match.split(' vs ')[1];
        document.getElementById('homeTeamName').textContent = homeTeam;
        document.getElementById('awayTeamName').textContent = awayTeam;
        document.getElementById('homeXG').textContent = (data.expected_goals.home || 0).toFixed(1);
        document.getElementById('awayXG').textContent = (data.expected_goals.away || 0).toFixed(1);
        
        // Update correct scores
        updateCorrectScores(data.correct_scores);
        
        // Update overall analysis
        updateOverallAnalysis(data.confidence_analysis, data.risk_assessment);
        
        // Show result
        showPredictionResult();
    }
    
    function updateMarketPrediction(prefix, prediction, confidence, risk) {
        if (!prediction) return;
        
        document.getElementById(prefix + 'Prediction').textContent = prediction.prediction || 'N/A';
        
        // Update probabilities
        const probsContainer = document.getElementById(prefix + 'Probs');
        probsContainer.innerHTML = '';
        
        if (prediction.probabilities) {
            // Define the correct order for different prediction types
            let orderedOutcomes = [];
            
            if (prefix === 'threeWay') {
                // Three-way: Home, Draw, Away
                orderedOutcomes = ['Home', 'Draw', 'Away'];
            } else if (prefix === 'dc') {
                // Double Chance: Home or Draw, Away or Draw, Home or Away
                orderedOutcomes = ['Home or Draw', 'Away or Draw', 'Home or Away'];
            } else if (prefix === 'btts') {
                // BTTS: Yes, No
                orderedOutcomes = ['Yes', 'No'];
            } else {
                // Over/Under: Over X.X, Under X.X
                orderedOutcomes = Object.keys(prediction.probabilities).sort((a, b) => {
                    if (a.startsWith('Over')) return -1;
                    if (b.startsWith('Over')) return 1;
                    return 0;
                });
            }
            
            // Display probabilities in the correct order
            orderedOutcomes.forEach(outcome => {
                if (prediction.probabilities[outcome] !== undefined) {
                    const probItem = document.createElement('div');
                    probItem.className = 'probability-item';
                    
                    // For double chance, show both raw and normalized values
                    let displayText = `${(prediction.probabilities[outcome] * 100).toFixed(1)}%`;
                    if (prefix === 'dc' && prediction.probabilities._raw_values && prediction.probabilities._raw_values[outcome]) {
                        const rawValue = (prediction.probabilities._raw_values[outcome] * 100).toFixed(1);
                        displayText = `${displayText} (Raw: ${rawValue}%)`;
                    }
                    
                    probItem.innerHTML = `
                        <span class="probability-label">${outcome}</span>
                        <span class="probability-value">${displayText}</span>
                    `;
                    probsContainer.appendChild(probItem);
                }
            });
            
            // Add any remaining outcomes that weren't in the ordered list
            Object.entries(prediction.probabilities).forEach(([outcome, prob]) => {
                if (!orderedOutcomes.includes(outcome)) {
                    const probItem = document.createElement('div');
                    probItem.className = 'probability-item';
                    probItem.innerHTML = `
                        <span class="probability-label">${outcome}</span>
                        <span class="probability-value">${(prob * 100).toFixed(1)}%</span>
                    `;
                    probsContainer.appendChild(probItem);
                }
            });
        }
        
        // Update confidence and risk badges
        if (confidence) {
            const confBadge = document.getElementById(prefix + 'Confidence');
            const confLevel = confidence.prediction_strength || 'Medium';
            confBadge.textContent = confLevel + ' Confidence';
            confBadge.className = 'confidence-badge confidence-' + confLevel.toLowerCase();
        }
        
        if (risk) {
            const riskBadge = document.getElementById(prefix + 'Risk');
            const riskLevel = risk.risk_level || 'Medium';
            riskBadge.textContent = riskLevel + ' Risk';
            riskBadge.className = 'risk-badge risk-' + riskLevel.toLowerCase();
        }
    }
    
    function updateCorrectScores(scores) {
        const container = document.getElementById('correctScores');
        container.innerHTML = '';
        
        if (scores && Object.keys(scores).length > 0) {
            Object.entries(scores).forEach(([score, prob]) => {
                const scoreItem = document.createElement('div');
                scoreItem.className = 'score-item';
                scoreItem.innerHTML = `
                    <span>${score}</span>
                    <span>${(prob * 100).toFixed(1)}%</span>
                `;
                container.appendChild(scoreItem);
            });
        } else {
            container.innerHTML = '<p class="text-center mb-0">No score predictions available</p>';
        }
    }
    
    function updateOverallAnalysis(confidence, risk) {
        // Calculate overall confidence (simplified)
        const overallConf = document.getElementById('overallConfidence');
        overallConf.textContent = 'Medium Confidence';
        overallConf.className = 'confidence-badge confidence-medium';
        
        // Calculate overall risk (simplified)
        const overallRisk = document.getElementById('overallRisk');
        if (risk && risk.overall) {
            const riskLevel = risk.overall.risk_level || 'Medium';
            overallRisk.textContent = riskLevel + ' Risk';
            overallRisk.className = 'risk-badge risk-' + riskLevel.toLowerCase();
        } else {
            overallRisk.textContent = 'Medium Risk';
            overallRisk.className = 'risk-badge risk-medium';
        }
    }
    
    function showLoading() {
        loadingSpinner.style.display = 'block';
    }
    
    function hideLoading() {
        loadingSpinner.style.display = 'none';
    }
    
    function showError(message) {
        document.getElementById('errorText').textContent = message;
        errorMessage.style.display = 'block';
    }
    
    function hideError() {
        errorMessage.style.display = 'none';
    }
    
    function showPredictionResult() {
        predictionResult.style.display = 'block';
    }
    
    function hidePredictionResult() {
        predictionResult.style.display = 'none';
    }
});
</script>
{% endblock %}