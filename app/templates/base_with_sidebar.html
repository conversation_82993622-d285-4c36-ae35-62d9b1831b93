<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Football Database{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-futbol me-2"></i>Football Database
            </a>
            
            <button class="navbar-toggler me-2" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('leagues') }}">
                            <i class="fas fa-trophy me-1"></i>Leagues
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('search') }}">
                            <i class="fas fa-search me-1"></i>Search
                        </a>
                    </li>
                </ul>
                
                <!-- Quick Search -->
                <form class="d-flex" action="{{ url_for('search') }}" method="GET">
                    <input class="form-control me-2" type="search" name="q" placeholder="Search teams, leagues..." 
                           value="{{ request.args.get('q', '') }}">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Layout with Sidebar -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar-container">
                <div class="sidebar">
                    <div class="sidebar-header">
                        <h6><i class="fas fa-trophy me-2"></i>Leagues</h6>
                        <div class="sidebar-search">
                            <input type="text" class="form-control form-control-sm" id="sidebarSearch" 
                                   placeholder="Search leagues...">
                        </div>
                    </div>
                    
                    <div class="sidebar-content">
                        {% if sidebar_leagues %}
                            {% for country, leagues in sidebar_leagues.items() %}
                            <div class="league-group">
                                <div class="league-group-header" data-bs-toggle="collapse" 
                                     data-bs-target="#collapse-{{ country }}" aria-expanded="false">
                                    <i class="fas fa-chevron-right me-2"></i>
                                    {{ country.replace('_', ' ').title() }}
                                    <span class="badge bg-secondary ms-auto">{{ leagues|length }}</span>
                                </div>
                                <div class="collapse" id="collapse-{{ country }}">
                                    <div class="league-list">
                                        {% for league in leagues %}
                                        <a href="{{ url_for('league_detail', league_name=league) }}" 
                                           class="league-link {% if request.view_args and request.view_args.get('league_name') == league %}active{% endif %}"
                                           data-league="{{ league }}">
                                            {{ league.replace('_', ' ').title() }}
                                        </a>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-muted text-center py-3">
                                <i class="fas fa-exclamation-triangle"></i>
                                <br>No leagues available
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <main class="mt-4">
                    {% block content %}{% endblock %}
                </main>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                <i class="fas fa-database me-1"></i>
                Football Betting Database System | 
                <i class="fas fa-calendar me-1"></i>
                2024
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    <!-- Sidebar functionality -->
    <script>
    // Mobile sidebar toggle
    document.getElementById('sidebarToggle').addEventListener('click', function() {
        const sidebar = document.querySelector('.sidebar-container');
        const overlay = document.getElementById('sidebarOverlay');
        
        sidebar.classList.toggle('show');
        overlay.classList.toggle('show');
    });
    
    // Close sidebar when clicking overlay
    document.getElementById('sidebarOverlay').addEventListener('click', function() {
        const sidebar = document.querySelector('.sidebar-container');
        const overlay = document.getElementById('sidebarOverlay');
        
        sidebar.classList.remove('show');
        overlay.classList.remove('show');
    });
    
    // Sidebar search functionality
    document.getElementById('sidebarSearch').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const leagueLinks = document.querySelectorAll('.league-link');
        const leagueGroups = document.querySelectorAll('.league-group');
        
        leagueGroups.forEach(group => {
            let hasVisibleLeagues = false;
            const links = group.querySelectorAll('.league-link');
            
            links.forEach(link => {
                const leagueName = link.dataset.league.toLowerCase();
                if (searchTerm === '' || leagueName.includes(searchTerm)) {
                    link.style.display = 'block';
                    hasVisibleLeagues = true;
                } else {
                    link.style.display = 'none';
                }
            });
            
            // Show/hide group based on whether it has visible leagues
            if (hasVisibleLeagues) {
                group.style.display = 'block';
                // Expand group if searching
                if (searchTerm !== '') {
                    const collapse = group.querySelector('.collapse');
                    if (collapse && !collapse.classList.contains('show')) {
                        new bootstrap.Collapse(collapse, {show: true});
                    }
                }
            } else {
                group.style.display = 'none';
            }
        });
    });
    
    // Auto-expand current league's group
    document.addEventListener('DOMContentLoaded', function() {
        const activeLink = document.querySelector('.league-link.active');
        if (activeLink) {
            const group = activeLink.closest('.league-group');
            if (group) {
                const collapse = group.querySelector('.collapse');
                if (collapse) {
                    new bootstrap.Collapse(collapse, {show: true});
                }
            }
        }
    });
    
    // Toggle chevron icons
    document.querySelectorAll('.league-group-header').forEach(header => {
        header.addEventListener('click', function() {
            const icon = this.querySelector('.fas');
            const collapse = this.nextElementSibling;
            
            collapse.addEventListener('shown.bs.collapse', function() {
                icon.classList.remove('fa-chevron-right');
                icon.classList.add('fa-chevron-down');
            });
            
            collapse.addEventListener('hidden.bs.collapse', function() {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-right');
            });
        });
    });
    
    // Close sidebar when clicking on a league link (mobile)
    document.querySelectorAll('.league-link').forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 767.98) {
                const sidebar = document.querySelector('.sidebar-container');
                const overlay = document.getElementById('sidebarOverlay');
                
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            }
        });
    });
    </script>
    
    {% block extra_scripts %}{% endblock %}
</body>
</html>