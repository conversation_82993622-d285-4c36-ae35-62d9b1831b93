{% extends "base.html" %}

{% block title %}Football Database - Home{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white rounded p-5 mb-4">
            <h1 class="display-4">
                <i class="fas fa-futbol me-3"></i>Football Database
            </h1>
            <p class="lead">
                Comprehensive football statistics and analysis across 300+ leagues worldwide.
                Explore team performance, head-to-head records, and league standings.
            </p>
            <hr class="my-4 border-light">
            <p>Access detailed statistics, compare teams, and analyze football data with our intuitive web interface.</p>
            <a class="btn btn-light btn-lg" href="{{ url_for('leagues') }}" role="button">
                <i class="fas fa-trophy me-2"></i>Explore Leagues
            </a>
        </div>
    </div>
</div>

<!-- Database Statistics -->
<div class="row mb-4">
    <div class="col-12">
        <h2><i class="fas fa-chart-bar me-2"></i>Database Overview</h2>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-trophy fa-2x mb-2"></i>
                <h4>{{ "{:,}".format(summary.get('leagues', 0)) }}</h4>
                <p class="card-text">Leagues</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ "{:,}".format(summary.get('teams', 0)) }}</h4>
                <p class="card-text">Teams</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-futbol fa-2x mb-2"></i>
                <h4>{{ "{:,}".format(summary.get('match_results', 0)) }}</h4>
                <p class="card-text">Match Results</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h4>{{ "{:,}".format(summary.get('head_to_head_stats', 0)) }}</h4>
                <p class="card-text">H2H Records</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Access -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-star me-2"></i>Popular Leagues</h5>
            </div>
            <div class="card-body">
                {% if sample_leagues %}
                    <div class="list-group list-group-flush">
                        {% for league in sample_leagues %}
                        <a href="{{ url_for('league_detail', league_name=league) }}" 
                           class="list-group-item list-group-item-action">
                            <i class="fas fa-trophy me-2 text-primary"></i>
                            {{ league.replace('_', ' ').title() }}
                        </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">No leagues available</p>
                {% endif %}
                
                <div class="mt-3">
                    <a href="{{ url_for('leagues') }}" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>View All Leagues
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>Database Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><i class="fas fa-database me-2"></i>Database Size:</td>
                        <td><strong>{{ "%.1f"|format(summary.get('database_size_mb', 0)) }} MB</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-chart-bar me-2"></i>Team Statistics:</td>
                        <td><strong>{{ "{:,}".format(summary.get('team_stats', 0)) }} records</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-table me-2"></i>League Tables:</td>
                        <td><strong>{{ "{:,}".format(summary.get('league_table', 0)) }} entries</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-chart-pie me-2"></i>League Stats:</td>
                        <td><strong>{{ "{:,}".format(summary.get('league_stats', 0)) }} records</strong></td>
                    </tr>
                    {% if summary.get('most_teams_league') %}
                    <tr>
                        <td><i class="fas fa-crown me-2"></i>Largest League:</td>
                        <td><strong>{{ summary.get('most_teams_league', '').replace('_', ' ').title() }}</strong> 
                            ({{ summary.get('most_teams_count', 0) }} teams)</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Features -->
<div class="row">
    <div class="col-12">
        <h3><i class="fas fa-rocket me-2"></i>Features</h3>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-trophy fa-3x text-primary mb-3"></i>
                <h5>League Analysis</h5>
                <p class="card-text">
                    Explore comprehensive league statistics, standings, and team performance across 300+ leagues worldwide.
                </p>
                <a href="{{ url_for('leagues') }}" class="btn btn-primary">Explore Leagues</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-users fa-3x text-success mb-3"></i>
                <h5>Team Statistics</h5>
                <p class="card-text">
                    Detailed team performance metrics including goals, form, home/away records, and recent match history.
                </p>
                <a href="{{ url_for('search') }}" class="btn btn-success">Search Teams</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-3x text-warning mb-3"></i>
                <h5>Head-to-Head</h5>
                <p class="card-text">
                    Compare teams with detailed head-to-head statistics, win percentages, and historical matchup data.
                </p>
                <a href="{{ url_for('search') }}" class="btn btn-warning">Compare Teams</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}