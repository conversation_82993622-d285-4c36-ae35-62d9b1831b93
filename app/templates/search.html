{% extends "base_with_sidebar.html" %}

{% block title %}Search - Football Database{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-search me-2"></i>Search</h1>
        <p class="lead">Find leagues, teams, and statistics</p>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-4">
    <div class="col-md-8 mx-auto">
        <form action="{{ url_for('search') }}" method="GET">
            <div class="input-group input-group-lg">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" name="q" 
                       placeholder="Search for teams, leagues..." 
                       value="{{ query }}" autofocus>
                <button class="btn btn-primary" type="submit">Search</button>
            </div>
        </form>
    </div>
</div>

{% if query %}
<!-- Search Results -->
<div class="row">
    <div class="col-12">
        <h3>Search Results for "{{ query }}"</h3>
    </div>
</div>

<!-- Leagues Results -->
{% if results.leagues %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-trophy me-2"></i>Leagues ({{ results.leagues|length }})</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for league in results.leagues %}
                    <div class="col-md-6 col-lg-4 mb-2">
                        <a href="{{ url_for('league_detail', league_name=league) }}" 
                           class="list-group-item list-group-item-action border-0">
                            <i class="fas fa-trophy me-2 text-primary"></i>
                            {{ league.replace('_', ' ').title() }}
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Teams Results -->
{% if results.teams %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users me-2"></i>Teams ({{ results.teams|length }})</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for team in results.teams %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-users me-2 text-success"></i>
                                    {{ team.team_name }}
                                </h6>
                                <p class="card-text text-muted small">
                                    {{ team.league_name.replace('_', ' ').title() }}
                                </p>
                                <a href="{{ url_for('team_detail', league_name=team.league_name, team_name=team.team_name) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- No Results -->
{% if not results.leagues and not results.teams %}
<div class="row">
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No results found</h4>
            <p class="text-muted">Try searching with different keywords or check your spelling.</p>
            <a href="{{ url_for('leagues') }}" class="btn btn-primary">
                <i class="fas fa-list me-2"></i>Browse All Leagues
            </a>
        </div>
    </div>
</div>
{% endif %}

{% else %}
<!-- Search Suggestions -->
<div class="row">
    <div class="col-12">
        <h3>Popular Searches</h3>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-star me-2"></i>Popular Leagues</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('search') }}?q=ENGLAND_PREMIER_LEAGUE" class="list-group-item list-group-item-action">
                        <i class="fas fa-trophy me-2 text-primary"></i>Premier League
                    </a>
                    <a href="{{ url_for('search') }}?q=SPAIN_LA_LIGA" class="list-group-item list-group-item-action">
                        <i class="fas fa-trophy me-2 text-primary"></i>La Liga
                    </a>
                    <a href="{{ url_for('search') }}?q=GERMANY_BUNDESLIGA" class="list-group-item list-group-item-action">
                        <i class="fas fa-trophy me-2 text-primary"></i>Bundesliga
                    </a>
                    <a href="{{ url_for('search') }}?q=ITALY_SERIE_A" class="list-group-item list-group-item-action">
                        <i class="fas fa-trophy me-2 text-primary"></i>Serie A
                    </a>
                    <a href="{{ url_for('search') }}?q=FRANCE_LIGUE_1" class="list-group-item list-group-item-action">
                        <i class="fas fa-trophy me-2 text-primary"></i>Ligue 1
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users me-2"></i>Popular Teams</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('search') }}?q=Arsenal" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2 text-success"></i>Arsenal
                    </a>
                    <a href="{{ url_for('search') }}?q=Manchester" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2 text-success"></i>Manchester United / City
                    </a>
                    <a href="{{ url_for('search') }}?q=Liverpool" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2 text-success"></i>Liverpool
                    </a>
                    <a href="{{ url_for('search') }}?q=Chelsea" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2 text-success"></i>Chelsea
                    </a>
                    <a href="{{ url_for('search') }}?q=Barcelona" class="list-group-item list-group-item-action">
                        <i class="fas fa-users me-2 text-success"></i>Barcelona
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Tips -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-lightbulb me-2"></i>Search Tips</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Search for team names like "Arsenal", "Manchester United"</li>
                    <li><i class="fas fa-check text-success me-2"></i>Search for league names like "Premier League", "La Liga"</li>
                    <li><i class="fas fa-check text-success me-2"></i>Use partial names - "Man" will find Manchester teams</li>
                    <li><i class="fas fa-check text-success me-2"></i>Search is case-insensitive</li>
                    <li><i class="fas fa-check text-success me-2"></i>Minimum 2 characters required</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_scripts %}
<script>
// Auto-focus search input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="q"]');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});
</script>
{% endblock %}