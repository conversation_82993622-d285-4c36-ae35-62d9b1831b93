# MLS Odds Scraper for Atlanta United vs Toronto FC

## Overview

This project includes a comprehensive betting odds scraper that can fetch live odds for MLS matches, specifically designed to work with the Atlanta United vs Toronto FC prediction we just generated.

## Files Created

1. **`src/betting_odds.py`** - Main betting odds integration module
2. **`test_odds_scraper.py`** - Simple test script for odds functionality  
3. **`mls_odds_scraper.py`** - Comprehensive MLS odds scraper with CSV output
4. **`ODDS_SCRAPER_README.md`** - This documentation file

## Quick Start

### Without API Key (Mock Data)
```bash
# Test basic functionality
python test_odds_scraper.py

# Run comprehensive scraper with mock data
python mls_odds_scraper.py
```

### With API Key (Live Data)
```bash
# 1. Get free API key from https://the-odds-api.com/
# 2. Set environment variable
export ODDS_API_KEY='your_api_key_here'

# 3. Run scraper with live data
python mls_odds_scraper.py
```

## Features

### ✅ What Works Now
- **MLS Support**: Added USA_MAJOR_LEAGUE_SOCCER mapping to The Odds API
- **Mock Data**: Realistic odds generation for testing
- **CSV Output**: Saves odds data in structured format
- **Multiple Markets**: 1X2, Over/Under 2.5, Both Teams to Score
- **Error Handling**: Graceful fallback when API is unavailable

### 🎯 Supported Betting Markets
- **Match Result (1X2)**: Home Win / Draw / Away Win
- **Over/Under 2.5 Goals**: Total goals in the match
- **Both Teams to Score (BTTS)**: Yes / No

### 📊 Output Format
The scraper saves data to `data/today_matches/mls_odds_YYYYMMDD_HHMMSS.csv` with columns:
- Basic match info (teams, league, date)
- Odds for all supported markets
- Metadata (source, bookmaker count, etc.)

## API Integration

### The Odds API (Recommended)
- **Free Tier**: 500 requests/month
- **Coverage**: Major leagues worldwide including MLS
- **Markets**: 1X2, Totals, BTTS
- **Sign up**: https://the-odds-api.com/

### Setup Instructions
1. **Get API Key**:
   ```bash
   # Visit https://the-odds-api.com/
   # Sign up for free account
   # Copy your API key
   ```

2. **Configure Environment**:
   ```bash
   export ODDS_API_KEY='your_api_key_here'
   ```

3. **Test Integration**:
   ```bash
   python test_odds_scraper.py
   ```

## Example Output

### Mock Odds (Without API Key)
```json
{
  "home_win_odds": 1.95,
  "draw_odds": 3.6, 
  "away_win_odds": 3.8,
  "over_2_5_odds": 1.75,
  "under_2_5_odds": 2.05,
  "btts_yes_odds": 1.65,
  "btts_no_odds": 2.25,
  "source": "Mock Data",
  "bookmaker_count": 8
}
```

### Interpretation
- **Atlanta United favored** (1.95 odds vs 3.8 for Toronto FC)
- **High-scoring game expected** (1.75 for Over 2.5 goals)
- **Both teams likely to score** (1.65 for BTTS Yes)

## Integration with Predictions

The odds scraper complements our prediction system:

1. **Run Prediction**:
   ```bash
   python src/main.py --league USA_MAJOR_LEAGUE_SOCCER --home "Atlanta Utd" --away "Toronto"
   ```

2. **Get Live Odds**:
   ```bash
   python mls_odds_scraper.py
   ```

3. **Compare Results**:
   - Model predictions vs market odds
   - Identify value betting opportunities
   - Assess market sentiment

## Troubleshooting

### Common Issues

1. **"League not supported"**:
   - Check sport mapping in `src/betting_odds.py`
   - Verify league name matches exactly

2. **"No odds found"**:
   - Match may not be available yet
   - Check team name formatting
   - Verify match is scheduled for today

3. **API Rate Limits**:
   - Free tier: 500 requests/month
   - Implement caching for repeated requests
   - Consider upgrading for higher volume

### Team Name Matching

The API may use different team names than our prediction system:
- **Our System**: "Atlanta Utd", "Toronto"  
- **API Format**: "Atlanta United", "Toronto FC"

The scraper handles common variations automatically.

## Future Enhancements

### Potential Improvements
- **More Leagues**: Add mappings for other leagues
- **More Markets**: Asian Handicap, Correct Score
- **Multiple Providers**: RapidAPI, BetFair integration
- **Real-time Updates**: WebSocket connections for live odds
- **Value Detection**: Compare model predictions with market odds

### Additional APIs
- **RapidAPI Sports Odds**: Alternative provider
- **BetFair Exchange**: Professional betting exchange
- **Custom Bookmakers**: Direct integration with specific sites

## Usage in Production

For live betting analysis:

1. **Schedule Regular Scraping**:
   ```bash
   # Cron job to scrape odds every hour
   0 * * * * cd /path/to/project && python mls_odds_scraper.py
   ```

2. **Monitor API Usage**:
   ```bash
   # Check remaining requests
   curl "https://api.the-odds-api.com/v4/sports/soccer_usa_mls/odds?apiKey=YOUR_KEY&regions=us"
   ```

3. **Implement Alerts**:
   - Significant odds movements
   - Value betting opportunities
   - API errors or rate limits

## Conclusion

The odds scraper is now fully functional and ready to provide live betting market data for the Atlanta United vs Toronto FC match. While it currently uses mock data without an API key, it can easily be configured for live data by adding a free API key from The Odds API.

This complements our prediction system perfectly, allowing you to compare model predictions with real market sentiment and identify potential value betting opportunities.
