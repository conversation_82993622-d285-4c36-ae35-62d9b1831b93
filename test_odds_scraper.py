#!/usr/bin/env python3
"""
Test script for betting odds scraping for Atlanta United vs Toronto FC
"""

import sys
import os
sys.path.append('src')

from betting_odds import BettingOddsProvider
import json
from datetime import datetime

def test_mls_odds():
    """Test betting odds for MLS match."""
    print("🎯 Testing Betting Odds Scraper for MLS")
    print("=" * 50)
    
    # Initialize provider (without API key for demo)
    provider = BettingOddsProvider()
    
    # Test the Atlanta United vs Toronto FC match
    league = "USA_MAJOR_LEAGUE_SOCCER"
    home_team = "Atlanta United"
    away_team = "Toronto FC"
    
    print(f"📊 Attempting to fetch odds for:")
    print(f"   🏠 Home: {home_team}")
    print(f"   ✈️  Away: {away_team}")
    print(f"   🏆 League: {league}")
    print()
    
    # Try to get odds
    odds = provider.get_match_odds(league, home_team, away_team)
    
    if odds:
        print("✅ Odds found!")
        print(json.dumps(odds, indent=2))
    else:
        print("❌ No odds found (expected without API key)")
        print()
        print("📝 To enable live odds scraping:")
        print("1. Get a free API key from https://the-odds-api.com/")
        print("2. Create betting_config.py with:")
        print("   ODDS_API_KEY = 'your_api_key_here'")
        print("   ENABLE_BETTING_ODDS = True")
        print()
        print("🎲 Mock odds data for demonstration:")
        
        # Generate mock odds for the match
        mock_odds = {
            'home_win_odds': 1.95,  # Atlanta United favored
            'draw_odds': 3.60,
            'away_win_odds': 3.80,  # Toronto FC underdog
            'over_2_5_odds': 1.75,
            'under_2_5_odds': 2.05,
            'btts_yes_odds': 1.65,
            'btts_no_odds': 2.25,
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'source': 'Mock Data for Demo',
            'bookmaker_count': 8,
            'match': f"{home_team} vs {away_team}",
            'league': league
        }
        
        print(json.dumps(mock_odds, indent=2))
        
        print()
        print("💡 Interpretation:")
        print(f"   • {home_team} is favored to win (odds: {mock_odds['home_win_odds']})")
        print(f"   • Draw is less likely (odds: {mock_odds['draw_odds']})")
        print(f"   • {away_team} is the underdog (odds: {mock_odds['away_win_odds']})")
        print(f"   • Over 2.5 goals is favored (odds: {mock_odds['over_2_5_odds']})")
        print(f"   • Both teams to score is likely (odds: {mock_odds['btts_yes_odds']})")

def test_with_api_key():
    """Show how to test with a real API key."""
    print("\n" + "=" * 50)
    print("🔑 Testing with API Key (if available)")
    print("=" * 50)
    
    # Check if API key is available
    api_key = os.environ.get('ODDS_API_KEY')
    
    if api_key:
        print(f"✅ API key found: {api_key[:8]}...")
        provider = BettingOddsProvider(api_key=api_key)
        
        # Test with real API
        odds = provider.get_match_odds("USA_MAJOR_LEAGUE_SOCCER", "Atlanta United", "Toronto FC")
        
        if odds:
            print("🎉 Live odds retrieved!")
            print(json.dumps(odds, indent=2))
        else:
            print("❌ No live odds found for this match")
            print("   (Match may not be available or already started)")
    else:
        print("❌ No API key found in environment variable ODDS_API_KEY")
        print("   Set it with: export ODDS_API_KEY='your_key_here'")

if __name__ == "__main__":
    test_mls_odds()
    test_with_api_key()
