{"league_name": "LITHUANIA_A_LYGA", "completed_urls": ["Fk Kauno Zalgiris vs Fk Transinvest Vilnius", "Fc Hegelmann Litauen Kaunas vs Fk Suduva Marijampole", "Fk Banga Gargzdai vs Vmfd Zalgiris Vilnius", "Fk Suduva Marijampole vs Fk Transinvest Vilnius", "Fk Kauno Zalgiris vs Fk Panevezys", "Fa Siauliai vs Vmfd Zalgiris Vilnius", "Fc Hegelmann Litauen Kaunas vs Fk Banga Gargzdai", "Fc Hegelmann Litauen Kaunas vs Fk Panevezys", "Fk Banga Gargzdai vs Fk Kauno Zalgiris", "Fk Dziugas Telsiai vs Fk Kauno Zalgiris", "Fk Kauno Zalgiris vs Fk Suduva Marijampole", "Fk Panevezys vs Fk Suduva Marijampole", "Fk Banga Gargzdai vs Fk Transinvest Vilnius", "Fk Dziugas Telsiai vs Fk Suduva Marijampole", "Fk Kauno Zalgiris vs Vmfd Zalgiris Vilnius", "Fa Siauliai vs Fc Hegelmann Litauen Kaunas", "Alytaus Dfk Dainava vs Fk Panevezys", "Alytaus Dfk Dainava vs Vmfd Zalgiris Vilnius", "Fk Dziugas Telsiai vs Fk Panevezys", "Alytaus Dfk Dainava vs Fk Suduva Marijampole", "Fc Hegelmann Litauen Kaunas vs Fk Dziugas Telsiai", "Fa Siauliai vs Fk Transinvest Vilnius", "Fk Panevezys vs Vmfd Zalgiris Vilnius", "Fk Banga Gargzdai vs Fk Panevezys", "Fa Siauliai vs Fk Dziugas Telsiai", "Fc Hegelmann Litauen Kaunas vs Fk Transinvest Vilnius", "Fa Siauliai vs Fk Banga Gargzdai", "Fk Panevezys vs Fk Transinvest Vilnius", "Fk Banga Gargzdai vs Fk Dziugas Telsiai", "Fk Suduva Marijampole vs Vmfd Zalgiris Vilnius", "Fk Dziugas Telsiai vs Fk Transinvest Vilnius", "Fc Hegelmann Litauen Kaunas vs Fk Kauno Zalgiris", "Alytaus Dfk Dainava vs Fk Banga Gargzdai", "Fk Dziugas Telsiai vs Vmfd Zalgiris Vilnius", "Fc Hegelmann Litauen Kaunas vs Vmfd Zalgiris Vilnius", "Alytaus Dfk Dainava vs Fk Kauno Zalgiris", "Alytaus Dfk Dainava vs Fc Hegelmann Litauen Kaunas", "Alytaus Dfk Dainava vs Fa Siauliai", "Alytaus Dfk Dainava vs Fk Transinvest Vilnius", "Fa Siauliai vs Fk Suduva Marijampole", "Fa Siauliai vs Fk Panevezys", "Fk Banga Gargzdai vs Fk Suduva Marijampole", "Fa Siauliai vs Fk Kauno Zalgiris", "Alytaus Dfk Dainava vs Fk Dziugas Telsiai"], "failed_urls": [], "retry_data": {}, "permanently_failed_urls": [], "processed_since_last_retry": 44, "current_position": 44, "total_urls": 45, "last_updated": "2025-06-24T19:23:25.246558", "batch_size": 4, "max_retries": 3, "retry_cooldown_urls": 20}