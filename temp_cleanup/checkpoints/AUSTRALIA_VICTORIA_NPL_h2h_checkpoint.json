{"league_name": "AUSTRALIA_VICTORIA_NPL", "completed_urls": ["Dandenong Thunder Sc vs Manningham United Blues", "Dandenong Thunder Sc vs Green Gully Sc", "Dandenong City Sc vs Manningham United Blues", "Heidelberg United Fc vs South Melbourne Fc", "Manningham United Blues vs South Melbourne Fc", "Green Gully Sc vs Melbourne Knights Fc", "Manningham United Blues vs Port Melbourne Sharks Sc", "Heidelberg United Fc vs Melbourne Knights Fc", "Melbourne Knights Fc vs St Albans Saints Fc", "Altona Magic Sc vs Oakleigh Cannons Fc", "Altona Magic Sc vs Dandenong Thunder Sc", "Avondale Fc vs South Melbourne Fc", "Dandenong City Sc vs Green Gully Sc", "Melbourne Knights Fc vs Port Melbourne Sharks Sc", "Hume City Fc vs St Albans Saints Fc", "Avondale Fc vs Moreland City Fc", "Dandenong City Sc vs Heidelberg United Fc", "Altona Magic Sc vs South Melbourne Fc", "Green Gully Sc vs Moreland City Fc", "Dandenong Thunder Sc vs Moreland City Fc", "Green Gully Sc vs Heidelberg United Fc", "Dandenong Thunder Sc vs Melbourne Knights Fc", "Dandenong City Sc vs Dandenong Thunder Sc", "Moreland City Fc vs South Melbourne Fc", "Altona Magic Sc vs Avondale Fc", "Altona Magic Sc vs St Albans Saints Fc", "Avondale Fc vs Oakleigh Cannons Fc", "Oakleigh Cannons Fc vs St Albans Saints Fc", "Avondale Fc vs Heidelberg United Fc", "Dandenong Thunder Sc vs St Albans Saints Fc", "Avondale Fc vs Dandenong City Sc", "Dandenong Thunder Sc vs Hume City Fc", "Dandenong City Sc vs South Melbourne Fc", "Altona Magic Sc vs Green Gully Sc", "Avondale Fc vs Melbourne Knights Fc", "Manningham United Blues vs Melbourne Knights Fc", "Dandenong Thunder Sc vs Heidelberg United Fc", "Altona Magic Sc vs Melbourne Knights Fc", "Hume City Fc vs Moreland City Fc", "Altona Magic Sc vs Heidelberg United Fc", "Green Gully Sc vs Port Melbourne Sharks Sc", "Altona Magic Sc vs Port Melbourne Sharks Sc", "Oakleigh Cannons Fc vs South Melbourne Fc", "Green Gully Sc vs Hume City Fc", "Melbourne Knights Fc vs Moreland City Fc", "Manningham United Blues vs Moreland City Fc", "Melbourne Knights Fc vs Oakleigh Cannons Fc", "Dandenong City Sc vs Oakleigh Cannons Fc", "Heidelberg United Fc vs St Albans Saints Fc", "Hume City Fc vs Port Melbourne Sharks Sc", "Avondale Fc vs Port Melbourne Sharks Sc", "Altona Magic Sc vs Manningham United Blues", "Altona Magic Sc vs Dandenong City Sc", "Avondale Fc vs Hume City Fc", "Green Gully Sc vs St Albans Saints Fc", "Dandenong Thunder Sc vs South Melbourne Fc", "Altona Magic Sc vs Moreland City Fc", "Dandenong Thunder Sc vs Oakleigh Cannons Fc", "Dandenong City Sc vs Hume City Fc", "Hume City Fc vs Melbourne Knights Fc", "Green Gully Sc vs South Melbourne Fc", "Avondale Fc vs St Albans Saints Fc", "Moreland City Fc vs Port Melbourne Sharks Sc", "Manningham United Blues vs Oakleigh Cannons Fc", "Moreland City Fc vs St Albans Saints Fc", "Heidelberg United Fc vs Manningham United Blues", "Moreland City Fc vs Oakleigh Cannons Fc", "Dandenong Thunder Sc vs Port Melbourne Sharks Sc", "Melbourne Knights Fc vs South Melbourne Fc", "Hume City Fc vs Manningham United Blues", "Heidelberg United Fc vs Hume City Fc", "Oakleigh Cannons Fc vs Port Melbourne Sharks Sc", "Green Gully Sc vs Oakleigh Cannons Fc", "Avondale Fc vs Manningham United Blues", "Heidelberg United Fc vs Oakleigh Cannons Fc", "Dandenong City Sc vs Port Melbourne Sharks Sc", "Dandenong City Sc vs Moreland City Fc", "Avondale Fc vs Dandenong Thunder Sc", "Heidelberg United Fc vs Moreland City Fc", "Dandenong City Sc vs Melbourne Knights Fc", "Avondale Fc vs Green Gully Sc", "Altona Magic Sc vs Hume City Fc", "Green Gully Sc vs Manningham United Blues", "Hume City Fc vs Oakleigh Cannons Fc", "Manningham United Blues vs St Albans Saints Fc", "Dandenong City Sc vs St Albans Saints Fc", "Heidelberg United Fc vs Port Melbourne Sharks Sc", "Hume City Fc vs South Melbourne Fc"], "failed_urls": [], "retry_data": {}, "permanently_failed_urls": [], "processed_since_last_retry": 88, "current_position": 88, "total_urls": 91, "last_updated": "2025-06-23T21:30:58.249989", "batch_size": 4, "max_retries": 3, "retry_cooldown_urls": 20}