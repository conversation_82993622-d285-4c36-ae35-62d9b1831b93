{"league_name": "AUSTRALIA_VICTORIA_NPL", "current_position": 91, "total_urls": 91, "completed_urls": ["Dandenong Thunder Sc vs Hume City Fc", "Port Melbourne Sharks Sc vs St Albans Saints Fc", "Dandenong City Sc vs Heidelberg United Fc", "Green Gully Sc vs Moreland City Fc", "Dandenong Thunder Sc vs Green Gully Sc", "Moreland City Fc vs St Albans Saints Fc", "Avondale Fc vs Oakleigh Cannons Fc", "Dandenong Thunder Sc vs South Melbourne Fc", "Green Gully Sc vs Melbourne Knights Fc", "Oakleigh Cannons Fc vs South Melbourne Fc", "Melbourne Knights Fc vs Port Melbourne Sharks Sc", "Avondale Fc vs Dandenong Thunder Sc", "Green Gully Sc vs Port Melbourne Sharks Sc", "Moreland City Fc vs Oakleigh Cannons Fc", "Avondale Fc vs Melbourne Knights Fc", "Dandenong City Sc vs Dandenong Thunder Sc", "Avondale Fc vs Heidelberg United Fc", "Altona Magic Sc vs Melbourne Knights Fc", "Altona Magic Sc vs Dandenong City Sc", "Heidelberg United Fc vs Port Melbourne Sharks Sc", "Hume City Fc vs St Albans Saints Fc", "Avondale Fc vs Dandenong City Sc", "Altona Magic Sc vs Hume City Fc", "Dandenong Thunder Sc vs Manningham United Blues", "Dandenong Thunder Sc vs St Albans Saints Fc", "Hume City Fc vs Oakleigh Cannons Fc", "Green Gully Sc vs South Melbourne Fc", "Dandenong City Sc vs Manningham United Blues", "Altona Magic Sc vs Moreland City Fc", "Manningham United Blues vs Oakleigh Cannons Fc", "Heidelberg United Fc vs St Albans Saints Fc", "Avondale Fc vs Moreland City Fc", "Dandenong City Sc vs South Melbourne Fc", "Melbourne Knights Fc vs Oakleigh Cannons Fc", "Avondale Fc vs St Albans Saints Fc", "Altona Magic Sc vs Avondale Fc", "Moreland City Fc vs Port Melbourne Sharks Sc", "Heidelberg United Fc vs Manningham United Blues", "Hume City Fc vs South Melbourne Fc", "Avondale Fc vs Manningham United Blues", "Manningham United Blues vs Melbourne Knights Fc", "Altona Magic Sc vs Green Gully Sc", "Avondale Fc vs Port Melbourne Sharks Sc", "Avondale Fc vs Hume City Fc", "Oakleigh Cannons Fc vs St Albans Saints Fc", "Dandenong City Sc vs Moreland City Fc", "Altona Magic Sc vs Dandenong Thunder Sc", "Dandenong City Sc vs Melbourne Knights Fc", "Altona Magic Sc vs Port Melbourne Sharks Sc", "South Melbourne Fc vs St Albans Saints Fc", "Green Gully Sc vs Manningham United Blues", "Avondale Fc vs Green Gully Sc", "Hume City Fc vs Melbourne Knights Fc", "Manningham United Blues vs Port Melbourne Sharks Sc", "Altona Magic Sc vs St Albans Saints Fc", "Altona Magic Sc vs South Melbourne Fc", "Dandenong City Sc vs Green Gully Sc", "Dandenong City Sc vs Hume City Fc", "Manningham United Blues vs St Albans Saints Fc", "Dandenong City Sc vs St Albans Saints Fc", "Dandenong Thunder Sc vs Port Melbourne Sharks Sc", "Dandenong City Sc vs Port Melbourne Sharks Sc", "Melbourne Knights Fc vs South Melbourne Fc", "Melbourne Knights Fc vs St Albans Saints Fc", "Hume City Fc vs Moreland City Fc", "Altona Magic Sc vs Manningham United Blues", "Hume City Fc vs Port Melbourne Sharks Sc", "Green Gully Sc vs Hume City Fc", "Green Gully Sc vs Heidelberg United Fc", "Heidelberg United Fc vs South Melbourne Fc", "Moreland City Fc vs South Melbourne Fc", "Hume City Fc vs Manningham United Blues", "Heidelberg United Fc vs Melbourne Knights Fc", "Dandenong Thunder Sc vs Melbourne Knights Fc", "Manningham United Blues vs Moreland City Fc", "Port Melbourne Sharks Sc vs South Melbourne Fc", "Green Gully Sc vs St Albans Saints Fc", "Manningham United Blues vs South Melbourne Fc", "Altona Magic Sc vs Oakleigh Cannons Fc", "Heidelberg United Fc vs Moreland City Fc", "Dandenong Thunder Sc vs Moreland City Fc", "Melbourne Knights Fc vs Moreland City Fc", "Altona Magic Sc vs Heidelberg United Fc", "Avondale Fc vs South Melbourne Fc", "Heidelberg United Fc vs Oakleigh Cannons Fc", "Dandenong City Sc vs Oakleigh Cannons Fc", "Dandenong Thunder Sc vs Oakleigh Cannons Fc", "Oakleigh Cannons Fc vs Port Melbourne Sharks Sc", "Heidelberg United Fc vs Hume City Fc", "Green Gully Sc vs Oakleigh Cannons Fc", "Dandenong Thunder Sc vs Heidelberg United Fc"], "failed_urls": [], "retry_urls": [], "last_updated": "2025-07-11T06:11:36.281003", "batch_size": 4}