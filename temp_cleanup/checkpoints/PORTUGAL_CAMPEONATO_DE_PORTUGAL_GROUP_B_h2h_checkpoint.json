{"league_name": "PORTUGAL_CAMPEONATO_DE_PORTUGAL_GROUP_B", "completed_urls": ["Cs Maritimo Funchal Ii vs Sc Salgueiros 08", "<PERSON> vs Cs Maritimo Funchal Ii", "Associacao Desportiva De Marco De Canaveses 09 vs Sc Salgueiros 08", "Guarda Fc vs Sc Coimbroes", "Futebol Clube De Alpendorada vs Sport Clube Da Regua", "Cf <PERSON><PERSON><PERSON> vs Cs Maritimo Funchal I<PERSON>", "Ad Machico vs Sport Clube Da Regua", "Leca Fc vs Sc Beira Mar", "Futebol Clube De Alpendorada vs Guarda Fc", "<PERSON> vs Cd Cinfaes", "<PERSON> vs Sc Beira Mar", "Cf Un<PERSON>o <PERSON> vs Guarda Fc", "Cf Uniao Lamas vs Sport Clube Da Regua", "Associacao Desportiva De Marco De Canaveses 09 vs Guarda Fc", "<PERSON> vs Cf <PERSON><PERSON>", "C<PERSON> <PERSON><PERSON><PERSON> vs Sc Beira Mar", "Leca Fc vs Sc Salgueiros 08", "Ad Camacha vs Futebol Clube De Alpendorada", "Gondomar Sc vs Guarda Fc", "Guarda Fc vs Leca Fc", "Futebol Clube De Alpendorada vs Sc Beira Mar", "Cd Cinfaes vs Gondomar Sc", "Cs <PERSON><PERSON><PERSON> Fun<PERSON> vs Gondomar Sc", "Cd Cinfaes vs Futebol Clube De Alpendorada", "C<PERSON> <PERSON><PERSON><PERSON> vs Sc Coimbroes", "Ad Camacha vs Associacao Desportiva De Marco De Canaveses 09", "Cd Cinfaes vs Sc Coimbroes", "Cd Cinfaes vs Cs Maritimo Funchal Ii", "Sc Beira Mar vs Sc Coimbroes", "Associacao Desportiva De Marco De Canaveses 09 vs Cs Maritimo Funchal Ii", "<PERSON> vs Cf Un<PERSON> Lama<PERSON>", "Cf Uniao Lamas vs Sc Salgueiros 08", "Cd Cinfaes vs Sc Salgueiros 08", "Guarda Fc vs Sc Salgueiros 08", "Ad <PERSON> vs Sc Salgueiros 08", "Cs Mari<PERSON><PERSON> Funchal <PERSON> vs Sc Beira Mar", "<PERSON> vs Leca Fc", "Gondomar Sc vs Sport Clube Da Regua", "<PERSON> vs Gondomar Sc", "Cf Un<PERSON>o Lamas vs Leca Fc", "<PERSON> vs Sc Beira Mar", "Leca Fc vs Sc Coimbroes", "Guarda Fc vs Sc Beira Mar", "<PERSON> vs Sc Salgueiros 08", "Associacao Desportiva De Marco De Canaveses 09 vs Sport Clube Da Regua", "Associacao Desportiva De Marco De Canaveses 09 vs Cf Uniao Lamas", "Cs Maritimo Funchal Ii vs Sc Coimbroes", "Ad <PERSON> vs Guarda Fc", "Cs Maritimo Funchal Ii vs Leca Fc", "Associacao Desportiva De Marco De Canaveses 09 vs Cd Cinfaes", "Cd Cinfaes vs Sport Clube Da Regua", "Futebol Clube De Alpendorada vs Leca Fc", "Guarda Fc vs Sport Clube Da Regua", "Cd Cinfaes vs Guarda Fc", "Cs Maritimo Funchal Ii vs Guarda Fc", "<PERSON>d Cinfaes vs Cf <PERSON><PERSON><PERSON>", "Futebol Clube De Alpendorada vs Sc Salgueiros 08", "Associacao Desportiva De Marco De Canaveses 09 vs Sc Beira Mar", "Associacao Desportiva De Marco De Canaveses 09 vs Leca Fc", "Associacao Desportiva De Marco De Canaveses 09 vs Gondomar Sc", "Cd Cinfaes vs Sc Beira Mar", "Futebol Clube De Alpendorada vs Sc Coimbroes", "Cd Cinfaes vs Leca Fc", "Cf Uniao Lamas vs Futebol Clube De Alpendorada", "Sc Beira Mar vs Sc Salgueiros 08", "<PERSON> vs Leca Fc", "Gondomar Sc vs Sc Beira Mar", "<PERSON> vs Sc Coimbroes", "<PERSON> vs Gondomar Sc", "Cs Maritimo Funchal Ii vs Sport Clube Da Regua", "<PERSON> vs Ad Machico", "<PERSON> vs Sc Coimbroes", "Gondomar Sc vs Leca Fc", "Cs Maritimo Funchal Ii vs Futebol Clube De Alpendorada", "Sc Beira Mar vs Sport Clube Da Regua", "Cf <PERSON><PERSON><PERSON> vs Gondomar Sc", "Gondomar Sc vs Sc Salgueiros 08", "Associacao Desportiva De Marco De Canaveses 09 vs Futebol Clube De Alpendorada", "Ad Machico vs Futebol Clube De Alpendorada", "<PERSON> vs Cd Cinfaes", "Leca Fc vs Sport Clube Da Regua", "<PERSON> vs Cs Maritimo Funchal <PERSON>", "<PERSON> vs Guarda Fc", "Ad Cam<PERSON>a vs Sport Clube Da Regua", "Futebol Clube De Alpendorada vs Gondomar Sc", "Gondomar Sc vs Sc Coimbroes", "Ad Mac<PERSON>o vs Associacao Desportiva De Marco De Canaveses 09", "Associacao Desportiva De Marco De Canaveses 09 vs Sc Coimbroes"], "failed_urls": [], "retry_data": {}, "permanently_failed_urls": [], "processed_since_last_retry": 88, "current_position": 88, "total_urls": 91, "last_updated": "2025-06-25T05:37:55.831007", "batch_size": 4, "max_retries": 3, "retry_cooldown_urls": 20}