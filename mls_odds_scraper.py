#!/usr/bin/env python3
"""
MLS Odds Scraper for Atlanta United vs Toronto FC
"""

import sys
import os
import pandas as pd
from datetime import datetime
import json

sys.path.append('src')

from betting_odds import Betting<PERSON>ddsProvider

def scrape_mls_odds(api_key=None, save_to_csv=True):
    """
    Scrape odds for MLS matches, specifically Atlanta United vs Toronto FC.
    
    Args:
        api_key (str): The Odds API key
        save_to_csv (bool): Whether to save results to CSV
    
    Returns:
        dict: Odds data or None if not found
    """
    
    print("🏈 MLS Odds Scraper")
    print("=" * 40)
    
    # Initialize provider
    provider = BettingOddsProvider(api_key=api_key) if api_key else BettingOddsProvider()
    
    # Match details
    league = "USA_MAJOR_LEAGUE_SOCCER"
    home_team = "Atlanta United"
    away_team = "Toronto FC"
    
    print(f"🎯 Target Match: {home_team} vs {away_team}")
    print(f"🏆 League: {league}")
    print(f"⏰ Scrape Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Attempt to get live odds
    if api_key:
        print("🔍 Fetching live odds...")
        odds = provider.get_match_odds(league, home_team, away_team)
        
        if odds:
            print("✅ Live odds retrieved!")
            print_odds_summary(odds)
            
            if save_to_csv:
                save_odds_to_csv(odds, home_team, away_team, league)
            
            return odds
        else:
            print("❌ No live odds found")
            print("   Possible reasons:")
            print("   • Match not available in API")
            print("   • Match already started/finished")
            print("   • Team names don't match API format")
            print()
    else:
        print("⚠️  No API key provided - using mock data")
    
    # Generate mock odds for demonstration
    print("🎲 Generating mock odds for demonstration...")
    mock_odds = generate_mock_odds(home_team, away_team, league)
    print_odds_summary(mock_odds)
    
    if save_to_csv:
        save_odds_to_csv(mock_odds, home_team, away_team, league, is_mock=True)
    
    return mock_odds

def generate_mock_odds(home_team, away_team, league):
    """Generate realistic mock odds for the match."""
    return {
        'home_win_odds': 1.95,  # Atlanta United slightly favored
        'draw_odds': 3.60,
        'away_win_odds': 3.80,  # Toronto FC underdog
        'over_2_5_odds': 1.75,  # High-scoring game expected
        'under_2_5_odds': 2.05,
        'btts_yes_odds': 1.65,  # Both teams likely to score
        'btts_no_odds': 2.25,
        'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'source': 'Mock Data' if not os.environ.get('ODDS_API_KEY') else 'The Odds API',
        'bookmaker_count': 8,
        'match': f"{home_team} vs {away_team}",
        'league': league,
        'home_team': home_team,
        'away_team': away_team
    }

def print_odds_summary(odds):
    """Print a formatted summary of the odds."""
    print("📊 Odds Summary:")
    print(f"   🏠 {odds.get('home_team', 'Home')} Win: {odds.get('home_win_odds', 'N/A')}")
    print(f"   🤝 Draw: {odds.get('draw_odds', 'N/A')}")
    print(f"   ✈️  {odds.get('away_team', 'Away')} Win: {odds.get('away_win_odds', 'N/A')}")
    print(f"   ⚽ Over 2.5 Goals: {odds.get('over_2_5_odds', 'N/A')}")
    print(f"   🛡️  Under 2.5 Goals: {odds.get('under_2_5_odds', 'N/A')}")
    print(f"   🎯 Both Teams Score: {odds.get('btts_yes_odds', 'N/A')}")
    print(f"   🚫 No BTTS: {odds.get('btts_no_odds', 'N/A')}")
    print(f"   📅 Updated: {odds.get('last_updated', 'N/A')}")
    print(f"   📊 Source: {odds.get('source', 'N/A')}")
    print()

def save_odds_to_csv(odds, home_team, away_team, league, is_mock=False):
    """Save odds data to CSV file."""
    
    # Create data directory if it doesn't exist
    os.makedirs('data/today_matches', exist_ok=True)
    
    # Prepare data for CSV
    csv_data = {
        'scraped_date': datetime.now().strftime('%Y-%m-%d'),
        'match_date': datetime.now().strftime('%Y-%m-%d'),  # Assuming today's match
        'home_team': home_team,
        'away_team': away_team,
        'league_name': league,
        'home_score': '',  # Empty for pre-match
        'away_score': '',  # Empty for pre-match
        'partial_results': '',
        'venue': '',
        'venue_town': 'Atlanta',  # Assuming home venue
        'venue_country': 'USA',
        '1x2_market': f"1:{odds.get('home_win_odds', '')},X:{odds.get('draw_odds', '')},2:{odds.get('away_win_odds', '')}",
        'over_under_1_5_market': '',  # Not provided by this API
        'over_under_2_5_market': f"Over:{odds.get('over_2_5_odds', '')},Under:{odds.get('under_2_5_odds', '')}",
        'over_under_3_5_market': '',  # Not provided by this API
        'btts_market': f"Yes:{odds.get('btts_yes_odds', '')},No:{odds.get('btts_no_odds', '')}",
        'double_chance_market': '',  # Not provided by this API
        'source': odds.get('source', 'Unknown'),
        'is_mock': is_mock,
        'bookmaker_count': odds.get('bookmaker_count', 0)
    }
    
    # Convert to DataFrame
    df = pd.DataFrame([csv_data])
    
    # Save to CSV
    filename = f"data/today_matches/mls_odds_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    df.to_csv(filename, index=False)
    
    print(f"💾 Odds saved to: {filename}")
    print()

def main():
    """Main function to run the odds scraper."""
    
    # Check for API key in environment variable
    api_key = os.environ.get('ODDS_API_KEY')
    
    if api_key:
        print(f"🔑 Using API key: {api_key[:8]}...")
    else:
        print("⚠️  No API key found. Set ODDS_API_KEY environment variable for live data.")
        print("   Example: export ODDS_API_KEY='your_key_here'")
    
    print()
    
    # Run the scraper
    odds = scrape_mls_odds(api_key=api_key, save_to_csv=True)
    
    # Show JSON output
    print("📋 Full JSON Output:")
    print(json.dumps(odds, indent=2))
    
    print()
    print("✅ Scraping complete!")
    
    if not api_key:
        print()
        print("🚀 To get live odds:")
        print("1. Visit: https://the-odds-api.com/")
        print("2. Sign up for free API key (500 requests/month)")
        print("3. Set environment variable: export ODDS_API_KEY='your_key'")
        print("4. Re-run this script")

if __name__ == "__main__":
    main()
