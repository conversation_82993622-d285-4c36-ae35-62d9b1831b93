# Design Document

## Overview

The import path fixing system will analyze Python files in the scripts directory, identify broken imports and file path references, and automatically fix them to work with the new directory structure. The system will use static analysis to detect import patterns and apply systematic transformations to update paths.

## Architecture

The solution consists of three main components:

1. **Import Analyzer**: Scans Python files to identify import statements and file path references
2. **Path Transformer**: Applies transformation rules to fix broken imports and paths
3. **Validator**: Tests the fixed imports to ensure they resolve correctly

```mermaid
graph TD
    A[Python Files in scripts/] --> B[Import Analyzer]
    B --> C[Path Transformer]
    C --> D[Validator]
    D --> E[Fixed Files]
    D --> F[Validation Report]
```

## Components and Interfaces

### Import Analyzer

**Purpose**: Parse Python files and extract import statements and file path references

**Key Methods**:
- `analyze_file(file_path)`: Parse a single Python file and extract imports
- `find_import_statements(ast_tree)`: Extract import statements from AST
- `find_file_paths(ast_tree)`: Extract hardcoded file paths from string literals
- `categorize_imports(imports)`: Classify imports as local, src, external, etc.

**Input**: Python file paths
**Output**: Dictionary of imports and file paths with their locations in the file

### Path Transformer

**Purpose**: Apply transformation rules to fix broken imports and paths

**Key Methods**:
- `transform_imports(file_path, imports)`: Apply import transformations
- `transform_file_paths(file_path, paths)`: Fix hardcoded file paths
- `apply_transformation_rules(import_stmt)`: Apply specific transformation rules
- `backup_original_file(file_path)`: Create backup before modifying

**Transformation Rules**:
1. `from src.module import X` → `from ..src.module import X` (relative import)
2. `import src.module` → `import sys; sys.path.append('..'); import src.module`
3. `'data/file.db'` → `'../data/file.db'` (relative path adjustment)
4. `'logs/file.log'` → `'../logs/file.log'` (log path adjustment)

**Input**: File path and list of imports/paths to transform
**Output**: Modified file content with fixed imports

### Validator

**Purpose**: Verify that fixed imports resolve correctly

**Key Methods**:
- `validate_file(file_path)`: Test if a file's imports work
- `test_import_resolution(import_stmt)`: Check if specific import resolves
- `generate_validation_report(results)`: Create summary of validation results
- `identify_remaining_issues(failed_imports)`: Analyze unresolved imports

**Input**: Fixed Python files
**Output**: Validation report with success/failure status for each file

## Data Models

### ImportInfo
```python
@dataclass
class ImportInfo:
    statement: str          # Original import statement
    module: str            # Module being imported
    line_number: int       # Line number in file
    import_type: str       # 'absolute', 'relative', 'from'
    is_local: bool         # Whether it's a local project import
```

### FilePathInfo
```python
@dataclass
class FilePathInfo:
    path: str              # Original file path
    line_number: int       # Line number in file
    context: str           # Surrounding code context
    path_type: str         # 'data', 'log', 'config', etc.
```

### ValidationResult
```python
@dataclass
class ValidationResult:
    file_path: str         # Path to the validated file
    success: bool          # Whether validation passed
    errors: List[str]      # List of import errors
    warnings: List[str]    # List of warnings
```

## Error Handling

1. **File Access Errors**: Handle cases where files cannot be read or written
2. **Parse Errors**: Handle malformed Python files that cannot be parsed
3. **Import Resolution Errors**: Handle cases where imports cannot be resolved even after fixing
4. **Backup Failures**: Handle cases where backup creation fails

Error handling strategy:
- Log all errors with detailed context
- Continue processing other files when one file fails
- Provide clear error messages with suggested manual fixes
- Create a separate error report for manual review

## Testing Strategy

### Unit Tests
- Test import statement parsing and categorization
- Test transformation rule application
- Test file path detection and fixing
- Test validation logic

### Integration Tests
- Test end-to-end processing of sample Python files
- Test with various import patterns found in the actual codebase
- Test error handling with malformed files
- Test backup and restore functionality

### Validation Tests
- Run the fixed scripts to ensure they execute without import errors
- Test a representative sample of scripts with different import patterns
- Verify that file operations (database access, log writing) work correctly

## Implementation Approach

1. **Phase 1**: Build the Import Analyzer to scan and categorize all imports
2. **Phase 2**: Implement Path Transformer with basic transformation rules
3. **Phase 3**: Add Validator to test fixed imports
4. **Phase 4**: Add comprehensive error handling and reporting
5. **Phase 5**: Test with actual project files and refine transformation rules